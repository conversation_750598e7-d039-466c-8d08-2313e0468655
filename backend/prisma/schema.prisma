generator client {
  provider        = "prisma-client-js"
  previewFeatures = ["fullTextSearch", "fullTextIndex"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

enum training_approval_status {
  APPROVED
  REJECTED
  PENDING
}

enum DocNumberValidationType {
  NUMERIC
  ALPHA_NUMERIC
}

enum preferred_language {
  ENGLISH
  HINDI
}

enum fulfillment_type {
  IN_HOUSE
  CONTRACTOR
}

enum company_type {
  OTHER
  LIMITED_LIABILITY_COMPANY
  PRIVATE_COMPANY
  PARTNERSHIP_FIRM
  SOLE_PROPRIETORSHIP
  NOT_SPECIFIED
}

model user_status {
  id          String   @id @default(uuid())
  title       String
  status      String
  description String?  @db.Text
  meta        Json?    @default("{}")
  user        user     @relation(fields: [user_id], references: [id], onDelete: Cascade)
  user_id     Int
  created_at  DateTime @default(now())
  updated_at  DateTime @updatedAt

  @@index([user_id])
}

model user {
  id                            Int                             @id @default(autoincrement())
  name                          String                          @default("")
  gender                        Gender?
  email                         String?                         @unique
  password                      String?
  phone                         String?                         @unique
  organization_id               Int
  meta                          Json?
  photoUrl                      String?
  isActive                      Boolean                         @default(true)
  created_at                    DateTime                        @default(now())
  updated_at                    DateTime?
  onboarding_stage              OnboardingStage                 @default(NOT_STARTED)
  emailVerified                 Boolean                         @default(false)
  phoneVerified                 Boolean                         @default(false)
  doc_verification_state        DocVerificationStatus           @default(NOT_SUBMITTED)
  location_id                   Int?
  creator_id                    Int?
  transfer_to_tms_status        Boolean                         @default(false)
  onboarding_stage_timestamps   Json[]                          @default([])
  onboarded                     Boolean                         @default(false)
  user_training_approval_status training_approval_status        @default(PENDING)
  creator                       user?                           @relation("user_creator_relation", fields: [creator_id], references: [id], onDelete: Cascade)
  location                      location?                       @relation(fields: [location_id], references: [id], onDelete: Cascade)
  organization                  organization                    @relation(fields: [organization_id], references: [id], onDelete: Cascade)
  document_user                 document[]                      @relation("user_doc_relation")
  tms_lead_is_updated           Boolean                         @default(false)
  poc                           String?
  source                        String?                         @default("Partner App")
  preferred_language            preferred_language              @default(ENGLISH)
  interview_feedback            interview_feedback[]
  role_map                      role_map[]
  user                          user[]                          @relation("user_creator_relation")
  user_expertise_map            user_expertise_map[]
  work_experience               work_experience[]
  calendar_event                calendar_event[]
  user_onboarding_data          user_onboarding_data[]
  bank_details                  bank_details[]
  user_state                    String?
  remark                        String?
  user_logs                     user_logs[]
  referral_system_points_ledger referral_system_points_ledger[]
  user_referred_to              user_referral_details?          @relation("referred_to")
  user_referred_by              user_referral_details[]         @relation("referred_by")
  unregistered_referrals        unregistered_referrals[]
  user_professional_data        user_professional_data[]
  interview                     interview[]
  assignment                    assignment[]
  notification                  notification[]
  manual_assignments            manual_assignments[]
  fulfillment_type              fulfillment_type                @default(IN_HOUSE)
  content_user_map              content_user_map[]
  course_user_map               course_user_map[]
  mascot_alert                  mascot_alert[]
  designation                   designation?                    @relation(fields: [designation_id], references: [id], onDelete: Cascade)
  designation_id                String?
  user_chapter_progress_track   user_chapter_progress_track[]
  user_video_progress           user_video_progress[]
  course_user_assignment_map    course_user_assignment_map[]
  user_status                   user_status[]
  user_type                     String                          @default("")
  organization_owner            organization?                   @relation("organization_owner")
  created_expertise             expertise[]                     @relation("creator_expertise_relation")
  contact_details               contact?                        @relation(fields: [contact_detail_id], references: [id])
  contact_detail_id             String?                         @unique // Ensure this is unique
  user_contacts                 user_contact_map[]
  last_contacts_synced_at       DateTime?
  tms_order_id                  String                          @default("")
  lead_already_exist_in_lms     Boolean                         @default(false)
  user_policy_tracking          user_policy_tracking[]
  user_checklist_data           user_checklist_data[]

  team_member_invitation_sent     team_member_invitation[]        @relation("inviter")
  team_member_invitation_received team_member_invitation[]        @relation("invited")
  team_member_invitation_accepted team_member_invitation[]
  app_installations               app_installation[]
  user_activities                 user_activity[]
  user_session_screen_recording   user_session_screen_recording[]
  app_installation_user_map       app_installation_user_map[]
  device_activity_daily           device_activity_daily[]
}

enum user_type_document {
  SERVICE_PROVIDER
  TECHNICIAN
}

enum user_type {
  SERVICE_PROVIDER
  TECHNICIAN
}

model document_type {
  id                                      Int                     @id @default(autoincrement())
  name                                    String
  required                                Boolean                 @default(false)
  creator_id                              Int
  instructions                            Json?
  created_at                              DateTime                @default(now())
  updated_at                              DateTime?
  icon                                    String?
  user_type                               user_type_document      @default(TECHNICIAN)
  meta                                    Json                    @default("{}")
  visible_to_service_provider_technicians Boolean                 @default(false)
  organizationId                          Int?
  status                                  DocumentStatus?         @default(ACTIVE)
  creator                                 admin                   @relation(fields: [creator_id], references: [id])
  organization                            organization?           @relation(fields: [organizationId], references: [id])
  document                                document[]
  //is_doc_number_required boolean default false
  is_doc_number_required                  Boolean                 @default(false)
  document_number_config                  document_number_config?
  updator_id                              Int?
  updator                                 admin?                  @relation("DocumentTypeUpdator", fields: [updator_id], references: [id])
}

enum DocumentCreatorType {
  ADMIN
  USER
  UNKNOWN
}

model document {
  id                   Int                 @id @default(autoincrement())
  url                  String
  type_id              Int
  user_id              Int
  verifier_id          Int?
  verified_on          DateTime?
  created_at           DateTime            @default(now())
  updated_at           DateTime?
  verified             Boolean             @default(false)
  adminId              Int?
  userId               Int?
  verification_message String?
  verification_status  VerificationStatus  @default(PENDING)
  admin                admin?              @relation(fields: [adminId], references: [id])
  type                 document_type       @relation(fields: [type_id], references: [id])
  user                 user                @relation("user_doc_relation", fields: [user_id], references: [id], onDelete: Cascade)
  verified_by          admin?              @relation("admin_doc_relation", fields: [verifier_id], references: [id])
  creator_type         DocumentCreatorType @default(UNKNOWN)
  //doc_number optional string
  doc_number           String              @default("")
}

model work_experience {
  id                 Int         @id @default(autoincrement())
  user_id            Int
  created_at         DateTime    @default(now())
  updated_at         DateTime?
  year_of_experience Int         @default(0)
  expertise          expertise[]
  organization       String?
  user               user        @relation(fields: [user_id], references: [id], onDelete: Cascade)
}

model role {
  id              Int          @id @default(autoincrement())
  name            String
  created_at      DateTime     @default(now())
  updated_at      DateTime?
  organization_id Int
  permission      Json         @default("{}")
  organization    organization @relation(fields: [organization_id], references: [id], onDelete: Cascade)
  role_map        role_map[]
}

model role_map {
  id         Int       @id @default(autoincrement())
  role_id    Int
  user_id    Int
  created_at DateTime  @default(now())
  updated_at DateTime?
  role       role      @relation(fields: [role_id], references: [id], onDelete: Cascade)
  user       user      @relation(fields: [user_id], references: [id], onDelete: Cascade)
}

model organization {
  id                            Int                             @id @default(autoincrement())
  name                          String
  trades                        Json?
  logo_url                      String?
  description                   String?
  created_at                    DateTime                        @default(now())
  updated_at                    DateTime?
  location_id                   Int?
  parent_id                     Int?
  location                      location?                       @relation(fields: [location_id], references: [id])
  parent                        organization?                   @relation("parent_relation_org", fields: [parent_id], references: [id])
  document_type                 document_type[]
  expertise                     expertise[]
  organization                  organization[]                  @relation("parent_relation_org")
  role                          role[]
  user                          user[]
  admin_on_organization         admin_on_organization[]
  referral_system_configuration referral_system_configuration[]
  question                      question[]
  question_bucket               question_bucket[]
  assignment_config             assignment_config[]
  auto_assignment_gen_rules     auto_assignment_gen_rules[]
  org_expertise_map             organization_expertise_map[]
  org_brand_map                 organization_brand_map[]
  organization_location_map     organization_location_map[] //how can an org belong to 1+ location ? 🔴 //by default  mumbai
  //org_owner 1-1 relation with User , user id relation
  org_owner                     user?                           @relation(name: "organization_owner", fields: [org_owner_id], references: [id], onDelete: Cascade)
  org_owner_id                  Int?                            @unique //should be give a default and keep it manadatory?🔴
  //add meta also here
  meta                          Json?
  is_active                     Boolean                         @default(true)
  //add service_type col
  organization_service_type_map organization_service_type_map[]
  //company_type Enum take from figma
  company_type                  company_type                    @default(NOT_SPECIFIED)
}

//crate a service_type_table : name , description? , meta? , is_active true

//are we gonna seed the types or configure from admin ? 🔴
model service_type {
  id                            String                          @id @default(uuid())
  name                          String
  description                   String?
  meta                          Json?
  is_deleted                    Boolean                         @default(false)
  is_active                     Boolean                         @default(true)
  created_at                    DateTime                        @default(now())
  updated_at                    DateTime?                       @updatedAt
  organization_service_type_map organization_service_type_map[]
}

//org_servcie_type_table : M-M
model organization_service_type_map {
  id              String       @id @default(uuid())
  organization_id Int
  service_type_id String
  organization    organization @relation(fields: [organization_id], references: [id], onDelete: Cascade)
  service_type    service_type @relation(fields: [service_type_id], references: [id])

  @@unique([organization_id, service_type_id]) //what is better having uniques index on it or keeping the @@id🔴
}

model organization_expertise_map {
  id                String       @id @default(uuid())
  org_id            Int
  expertise_id      Int
  organization      organization @relation(fields: [org_id], references: [id], onDelete: Cascade)
  expertise         expertise    @relation(fields: [expertise_id], references: [id])
  //add meta and team member count
  meta              Json?
  team_member_count Int

  @@unique([org_id, expertise_id])
}

model brand {
  id            String                   @id @default(uuid())
  name          String
  description   String?
  icon          String?
  url           String?
  is_active     Boolean                  @default(true)
  is_deleted    Boolean                  @default(false)
  created_at    DateTime                 @default(now())
  updated_at    DateTime                 @updatedAt
  org_brand_map organization_brand_map[]
  // add meta
  meta          Json?
}

model organization_brand_map {
  id           String       @id @default(uuid())
  org_id       Int
  brand_id     String
  organization organization @relation(fields: [org_id], references: [id], onDelete: Cascade)
  brand        brand        @relation(fields: [brand_id], references: [id], onDelete: Cascade)

  @@unique([org_id, brand_id])
}

model calendar_event {
  id              Int          @id @default(autoincrement())
  name            String
  data            Json?
  google_event_id String?
  type            EVENT_TYPE
  admin_id        Int?
  admin           admin?       @relation(fields: [admin_id], references: [id])
  user_id         Int?
  user            user?        @relation(fields: [user_id], references: [id], onDelete: Cascade)
  interviewer_id  Int?
  interviewer     interviewer? @relation(fields: [interviewer_id], references: [id])
  created_at      DateTime     @default(now())
  updated_at      DateTime?
}

model location {
  id                        Int                         @id @default(autoincrement())
  pincode                   String
  state                     String
  country                   String
  work_address              String?
  landmark                  String?
  city                      String?
  district_taluka           String?
  organization              organization[]
  user                      user[]
  interviewer               interviewer[]
  user_onboarding_data      user_onboarding_data[]
  user_professional_data    user_professional_data[]
  organization_location_map organization_location_map[]

  @@index([pincode])
}

model organization_location_map {
  id           String       @id @default(uuid())
  org_id       Int
  location_id  Int
  organization organization @relation(fields: [org_id], references: [id], onDelete: Cascade)
  location     location     @relation(fields: [location_id], references: [id], onDelete: Cascade)

  @@unique([location_id, org_id])
}

//rename notification_logs
model SmsEmailLogs {
  id               Int      @id @default(autoincrement())
  receiver         String
  type             LogType
  data             Json
  providerResponse Json
  result           Boolean
  createdAt        DateTime @default(now())
  meta             Json?
}

model expertise {
  id                        Int                          @id @default(autoincrement())
  name                      String
  icon                      String
  organization_id           Int
  created_at                DateTime                     @default(now())
  updated_at                DateTime?
  organization              organization                 @relation(fields: [organization_id], references: [id], onDelete: Cascade)
  expertise_user_map        user_expertise_map[]
  work_experience           work_experience?             @relation(fields: [work_experienceId], references: [id])
  work_experienceId         Int?
  expertise_course_map      expertise_course_map[]
  expertise_content_map     expertise_content_map[]
  designation_expertise_map designation_expertise_map[]
  org_expertise_map         organization_expertise_map[]
  //created_By map with User  table 1-1
  order                     Int?
  active                    Boolean                      @default(true)

  created_by Int?
  creator    user? @relation(fields: [created_by], references: [id], name: "creator_expertise_relation")
}

model user_expertise_map {
  id           Int       @id @default(autoincrement())
  user_id      Int
  expertise_id Int
  created_at   DateTime  @default(now())
  updated_at   DateTime?
  expertise    expertise @relation(fields: [expertise_id], references: [id], onDelete: Cascade)
  user         user      @relation(fields: [user_id], references: [id], onDelete: Cascade)
}

model admin {
  id                        Int                      @id @default(autoincrement())
  admin_role_id             Int?
  admin_role                admin_role?              @relation("admin_role_relation", fields: [admin_role_id], references: [admin_role_id], onDelete: Restrict)
  name                      String
  email                     String?                  @unique
  phone                     String                   @unique
  password                  String
  profile_photo             String?
  type                      AdminType
  phone_verified            Boolean?                 @default(false)
  email_verified            Boolean?                 @default(false)
  created_at                DateTime                 @default(now())
  updated_at                DateTime?
  created_by                Int?
  meta                      Json?
  active_org_id             Int?
  parent_id                 Int?
  parent                    admin?                   @relation("parent_relation_admin", fields: [parent_id], references: [id])
  document                  document[]
  admin_docs                document[]               @relation("admin_doc_relation")
  interview_template        feedback_template[]
  interviewer               interviewer[]
  document_type             document_type[]
  calendar_event            calendar_event[]
  lead_landing_page         lead_landing_page[]
  admin_on_organization     admin_on_organization[]
  admin                     admin[]                  @relation("parent_relation_admin")
  user_onboarding_data      user_onboarding_data[]
  user_logs                 user_logs[]
  interview                 interview[]
  assignment_config         assignment_config[]
  question_bucket           question_bucket[]
  manual_assignments        manual_assignments[]
  course                    course[]
  content                   content[]
  content_user_map          content_user_map[]
  course_user_map           course_user_map[]
  banner_config             banner_config[]
  banner                    banner[]
  designation               designation[]
  last_release_version_seen String                   @default("")
  document_number_config    document_number_config[]
  updated_document_types    document_type[]          @relation("DocumentTypeUpdator")
  created_checklists        user_checklist_data[]    @relation("creator_checklist_relation")
  edited_checklists         user_checklist_data[]    @relation("editor_checklist_relation")
}

model interviewer {
  id                 Int                  @id @default(autoincrement())
  name               String
  email              String               @unique
  phone              String?              @unique
  location_id        Int?
  location           location?            @relation(fields: [location_id], references: [id])
  created_by         Int
  created_on         DateTime             @default(now())
  updated_on         DateTime
  active             Boolean              @default(false)
  meta               Json                 @default("{}")
  creator            admin                @relation(fields: [created_by], references: [id])
  interview_feedback interview_feedback[]
  calendar_event     calendar_event[]
  interview          interview[]
}

model feedback_template {
  id                           Int                  @id @default(autoincrement())
  title                        String
  status                       Boolean              @default(false)
  created_by                   Int
  interviewer_feedback_meta    Json                 @default("{}")
  interviewer_feedback_enabled Boolean              @default(false)
  interviewee_feedback_meta    Json                 @default("{}")
  interviewee_feedback_enabled Boolean              @default(false)
  meta                         Json                 @default("{}")
  created_at                   DateTime?            @default(now())
  creator                      admin                @relation(fields: [created_by], references: [id])
  feedback_section             feedback_section[]
  interview_feedback           interview_feedback[]
}

model feedback_section {
  id              Int               @id @default(autoincrement())
  title           String
  max_score       Int               @default(5)
  instruction     String?
  template_id     Int
  required        Boolean           @default(false)
  template        feedback_template @relation(fields: [template_id], references: [id], onDelete: Cascade)
  feedback_result feedback_result[]
}

model feedback_result {
  id          Int                @id @default(autoincrement())
  feedback_id Int
  section_id  Int
  score       Int
  comments    String?
  feedback    interview_feedback @relation(fields: [feedback_id], references: [id], onDelete: Cascade)
  section     feedback_section   @relation(fields: [section_id], references: [id])
  created_at  DateTime           @default(now())
}

model interview_feedback {
  id                          Int               @id @default(autoincrement())
  feedback_form_id            String?           @unique @default(uuid())
  interviewer_id              Int
  summary                     String?
  user_id                     Int               @default(autoincrement())
  template_id                 Int
  interviewer_feedback_result Json?             @default("{}")
  interviewee_feedback_result Json?             @default("{}")
  interviewee_feedback_state  FeedbackState?    @default(PENDING)
  interviewer_feedback_state  FeedbackState?    @default(PENDING)
  meta                        Json?             @default("{}") //to store the date time of the feedback
  created_at                  DateTime          @default(now())
  updated_at                  DateTime?         @updatedAt
  interview_state             InterviewState?   @default(SCHEDULED)
  interviewer                 interviewer       @relation(fields: [interviewer_id], references: [id])
  template                    feedback_template @relation(fields: [template_id], references: [id])
  user                        user              @relation(fields: [user_id], references: [id], onDelete: Cascade)
  feedback_result             feedback_result[]
  interview                   interview[]
}

enum InterviewState {
  SCHEDULED
  COMPLETED
  EXPIRED
  REJECTED
  CANCELLED
  RESCHEDULED
}

enum FeedbackState {
  PENDING
  COMPLETED
  EXPIRED
}

enum OnboardingStage {
  NOT_STARTED
  SIGN_UP
  GENERAL_DETAILS
  WORK_EXPERIENCE
  DOCUMENTS
  VERIFICATION
  ONBOARD
  SKILLS
  INTERVIEW
  TRAINING_AND_TESTS
  SCREENING_TEST
  TRANSFERRED_TO_TMS
  DASHBOARD
}

enum Gender {
  MALE
  FEMALE
  NOT_SPECIFIED
}

enum VerificationStatus {
  PENDING
  APPROVED
  REJECTED
}

enum EVENT_TYPE {
  INTERVIEW_ONLINE
  INTERVIEW_OFFLINE
}

enum LogType {
  EMAIL
  SMS
  //add WHATSAPP
}

enum AdminType {
  SUPER_USER
  ADMIN
}

enum DocVerificationStatus {
  NOT_SUBMITTED
  FAILED
  COMPLETED
  PENDING
  RE_UPLOAD
}

enum LeadHandlingStatus {
  PENDING
  HANDLED
}

enum MartialStatus {
  MARRIED
  UNMARRIED
}

enum HiringCriteria {
  TRAINING_ASSESSMENT_TEST
  TECHNICAL_ROUND_AND_PAST_EXPERIENCE_OF_MODULAR
  ONSITE_PRACTICAL_TEST_AND_THEORETICAL_TEST
}

enum UserType {
  ADMIN
  USER
}

enum DocumentStatus {
  ACTIVE
  INACTIVE
}

model lead_landing_page {
  id           Int                @id @default(autoincrement())
  name         String
  phone_number String             @unique
  admin_id     Int
  admin        admin              @relation(references: [id], fields: [admin_id])
  location     String
  current_work String
  meta         Json?
  created_at   DateTime           @default(now())
  updated_at   DateTime
  status       LeadHandlingStatus

  @@index([phone_number])
}

model permission {
  id                    Int                     @id @default(autoincrement())
  dashboard             Json
  assignment            Json                    @default("[]")
  users                 Json
  documents             Json
  interviews            Json
  assessment            Json
  skills                Json
  document_type         Json
  lead_management       Json
  training_and_test     Json
  transfer_to_tms       Json?
  reject_user           Json?
  referral              Json?
  name                  String?
  service_providers     Json                    @default("[]")
  users_manage          Json                    @default("[]")
  banners               Json                    @default("[]")
  settings              Json                    @default("[]")
  lead_contacts         Json                    @default("[]")
  admin_on_organization admin_on_organization[]
}

model admin_on_organization {
  admin_id        Int
  admin           admin        @relation(references: [id], fields: [admin_id])
  organization_id Int
  organization    organization @relation(references: [id], fields: [organization_id])
  permission_id   Int
  permission      permission   @relation(references: [id], fields: [permission_id])

  @@id([admin_id, organization_id])
}

model user_logs {
  id                     Int                    @id @default(autoincrement())
  user_id                Int?
  user_phone             String?
  initiator_id           Int?
  initiated_by           UserType?
  created_at             DateTime               @default(now())
  transfer_to_tms_status Boolean?
  onboarding_stage       OnboardingStage?
  doc_verification_state DocVerificationStatus?
  isActive               Boolean?
  description            String?
  meta                   Json?
  user_deleted           Boolean?               @default(false)
  user                   user?                  @relation(fields: [user_id], references: [id], onDelete: SetNull)
  admin                  admin?                 @relation(fields: [initiator_id], references: [id])
}

model api_logs {
  id        Int      @id @default(autoincrement())
  method    String
  path      String
  headers   String?
  req_body  String?
  res_body  String?
  status    Int
  success   Boolean
  response  Json?
  receiver  String?
  meta      Json?
  timestamp DateTime @default(now())
}

model user_onboarding_data {
  id                  Int             @id @default(autoincrement())
  martial_status      MartialStatus?
  source              String?
  hiring_criteria     HiringCriteria?
  date_of_joining     DateTime?
  designation         String?
  hiring_manager_id   Int?
  esic_number         String?
  mothers_name        String?
  fathers_name        String?
  salary_offered      String?
  remark              String?
  aadhar_address      String?
  pancard_number      String?
  aadhar_number       String?
  user_created_in_tms Boolean         @default(false)
  current_address     Int?
  employee_id         Int?
  form_no_11          String?
  city                String?
  assessment_score    String?
  family_details      String?
  assessed_by         String?
  poc                 String?
  user_id             Int             @unique
  user                user            @relation(fields: [user_id], references: [id], onDelete: Cascade)
  location            location?       @relation(fields: [current_address], references: [id])
  hiring_manager      admin?          @relation(fields: [hiring_manager_id], references: [id])
  meta                Json            @default("{}")
  salary_vp           String?
}

model bank_details {
  id        Int    @id @default(autoincrement())
  user      user   @relation(fields: [user_id], references: [id], onDelete: Cascade)
  user_id   Int    @unique
  encrypted String
}

model user_referral_details {
  id                            Int                            @id @default(autoincrement())
  referral_code                 String?                        @unique
  available_points              Int?
  user_id                       Int                            @unique
  referred_to                   user                           @relation(fields: [user_id], references: [id], onDelete: Cascade, name: "referred_to")
  user_referral_ledger_id       Int?
  referral_system_points_ledger referral_system_points_ledger? @relation(fields: [user_referral_ledger_id], references: [id], onDelete: Cascade)
  referred_by_id                Int?
  referred_by                   user?                          @relation(fields: [referred_by_id], references: [id], onDelete: Cascade, name: "referred_by")
  created_at                    DateTime                       @default(now())
  updated_at                    DateTime                       @default(now())
}

model unregistered_referrals {
  id             String    @id @default(uuid())
  name           String
  phone_number   String
  referrer_id    Int
  referrer       user      @relation(fields: [referrer_id], references: [id], onDelete: Cascade)
  created_at     DateTime  @default(now())
  updated_at     DateTime  @updatedAt
  meta           Json      @default("{}")
  
  @@index([phone_number])
  @@index([referrer_id])
}

model referral_system_configuration {
  id                      Int             @id @default(autoincrement())
  referral_enabled        Boolean         @default(true)
  existing_user_points    Int
  new_user_points         Int
  created_at              DateTime        @default(now())
  updated_at              DateTime        @default(now())
  user_point_redeem_stage OnboardingStage
  organization_id         Int
  organization            organization    @relation(fields: [organization_id], references: [id])
  terms_and_condition     Json            @default("{}")
}

model referral_system_points_ledger {
  id                    Int                     @id @default(autoincrement())
  credited              Boolean
  debited               Boolean
  credited_amount       Int?
  debited_amount        Int?
  available_balance     Int
  message               String?
  user_id               Int
  user                  user                    @relation(fields: [user_id], references: [id], onDelete: Cascade)
  user_referral_details user_referral_details[]
  created_at            DateTime                @default(now())
  updated_at            DateTime                @default(now())
}

model user_professional_data {
  id                       Int       @id @default(autoincrement())
  highest_education        String
  experienced              Boolean
  year_of_experience       Int       @default(0)
  current_work_location_id Int?
  user_id                  Int       @unique
  user                     user      @relation(fields: [user_id], references: [id], onDelete: Cascade)
  current_work_location    location? @relation(fields: [current_work_location_id], references: [id])
  created_at               DateTime  @default(now())
  updated_at               DateTime  @default(now())
}

model interview {
  id               Int                @id @default(autoincrement())
  title            String
  start_time       DateTime
  end_time         DateTime
  location         String? // Offline interview location
  interview_type   EVENT_TYPE // Offline or Online
  google_meet_link String
  event_id         String             @unique
  note             String?
  admin_id         Int // Change to Int type
  user_id          Int // Change to Int type @unique
  interviewer_id   Int?
  attempts         Int
  created_at       DateTime           @default(now())
  updated_at       DateTime           @default(now())
  status           InterviewState
  feedback_id      Int
  is_active        Boolean            @default(true)
  scheduled_by     admin              @relation(fields: [admin_id], references: [id])
  schedule_for     user?              @relation(fields: [user_id], references: [id], onDelete: Cascade)
  interviewer      interviewer?       @relation(fields: [interviewer_id], references: [id])
  feedback         interview_feedback @relation(fields: [feedback_id], references: [id], onDelete: Cascade)
}

enum assignment_type {
  AUTOMATIC
  MANUAL
}

enum auto_assignment_gen_rules_type {
  QUESTION
  CONFIG
}

model auto_assignment_gen_rules {
  id                         String                         @id @default(uuid())
  name                       String
  org_id                     Int
  org                        organization                   @relation(references: [id], fields: [org_id])
  rules                      Json
  type                       auto_assignment_gen_rules_type
  is_active                  Boolean                        @default(true)
  assign_to                  user_type                      @default(TECHNICIAN)
  question_assignment_config assignment_config[]            @relation("config_rules")
  config_assignment_config   assignment_config[]            @relation("question_rules")
  created_at                 DateTime                       @default(now())
  updated_at                 DateTime                       @updatedAt
  assignment_config          assignment_config[]
}

model assignment_config {
  id                                  String                           @id @default(uuid())
  name                                String
  config_assignment_type              assignment_type
  config_assignment_rules             auto_assignment_gen_rules?       @relation(name: "config_rules", fields: [config_auto_assignment_gen_rules_id], references: [id])
  config_auto_assignment_gen_rules_id String?
  questions_assignment_type           assignment_type
  questions_assignment_rules          auto_assignment_gen_rules?       @relation(name: "question_rules", fields: [auto_assignment_gen_rules_id], references: [id])
  auto_assignment_gen_rules_id        String?
  no_of_questions                     Int
  general_instructions_md             String?
  is_active                           Boolean                          @default(true)
  draft_mode                          Boolean                          @default(true)
  meta                                Json?
  creator                             admin                            @relation(fields: [creator_id], references: [id])
  org_id                              Int
  org                                 organization                     @relation(references: [id], fields: [org_id])
  creator_id                          Int
  created_at                          DateTime                         @default(now())
  updated_at                          DateTime                         @updatedAt
  questions_on_assignment_config      questions_on_assignment_config[]
  time_bounded                        Boolean                          @default(false)
  easy_question_time                  Int?
  medium_question_time                Int?
  hard_question_time                  Int?
  on_basis_of                         Json                             @default("[]")
  question_assignment_rules_id        String?
  assigned_to                         user_type                        @default(TECHNICIAN)
  assigned_to_only_team_members       Boolean                          @default(false)
  question_assignment_rules           auto_assignment_gen_rules?       @relation(fields: [question_assignment_rules_id], references: [id])
  assignment                          assignment[]
  manual_assignments                  manual_assignments[]
  course_assignment_map               course_assignment_map[]
  course_user_assignment_map          course_user_assignment_map[]
}

model option {
  id                     String                   @id @default(uuid())
  question_id            String
  option_text            String                   @db.Text
  option_audio           String                   @default("")
  is_right_option        Boolean                  @default(false)
  question               question                 @relation(references: [id], fields: [question_id], onDelete: Cascade)
  question_on_assignment question_on_assignment[]
}

enum question_diffculty {
  HARD
  MEDIUM
  EASY
}

model question {
  id                             String                           @id @default(uuid())
  question_text                  String
  question_audio                 String                           @default("")
  images                         Json[]                           @default([])
  question_difficulty            question_diffculty
  option                         option[]
  bucket_id                      String
  bucket                         question_bucket                  @relation(references: [id], fields: [bucket_id])
  org_id                         Int
  org                            organization                     @relation(references: [id], fields: [org_id])
  questions_on_assignment_config questions_on_assignment_config[]
  created_at                     DateTime                         @default(now())
  updated_at                     DateTime                         @updatedAt
  question_on_assignment         question_on_assignment[]
  meta                           Json                             @default("{}")
}

model questions_on_assignment_config {
  question_id          String
  assignment_config_id String
  question             question          @relation(references: [id], fields: [question_id])
  assignment_config    assignment_config @relation(references: [id], fields: [assignment_config_id], onDelete: Cascade)
  meta                 Json              @default("{}")

  @@id([question_id, assignment_config_id])
}

model assignment_result {
  id                    String       @id @default(uuid())
  correct_answer        Int
  wrong_answer          Int
  total_time_taken      Int
  unattempted_questions Int
  total_questions       Int
  created_at            DateTime     @default(now())
  updated_at            DateTime     @updatedAt
  assignment            assignment[]
  meta                  Json         @default("{}")
}

enum assignment_status {
  NOT_STARTED
  ON_GOING
  COMPLETED
}

model assignment {
  id                          String                      @id @default(uuid())
  assignment_configration     assignment_config           @relation(fields: [assignment_configuration_id], references: [id], onDelete: Cascade)
  result                      assignment_result?          @relation(fields: [assignment_result_id], references: [id])
  given_by                    user                        @relation(fields: [user_id], references: [id], onDelete: Cascade)
  status                      assignment_status           @default(NOT_STARTED)
  started_at                  DateTime
  finished_at                 DateTime?
  assignment_duration_seconds Int                         @default(0)
  user_id                     Int
  created_at                  DateTime                    @default(now())
  updated_at                  DateTime                    @updatedAt
  assignment_configuration_id String
  assignment_result_id        String?
  question_on_assignment      question_on_assignment[]
  meta                        Json                        @default("{}")
  course_user_assignment_map  course_user_assignment_map?
}

model manual_assignments {
  user_id              Int
  user                 user              @relation(fields: [user_id], references: [id], onDelete: Cascade)
  admin_id             Int?
  assigned_by          admin?            @relation(fields: [admin_id], references: [id])
  created_on           DateTime          @default(now())
  updated_on           DateTime          @updatedAt
  assignment_config_id String
  assignment_config    assignment_config @relation(fields: [assignment_config_id], references: [id], onDelete: Cascade)

  @@id([user_id, assignment_config_id])
}

model question_on_assignment {
  assignment_id   String
  question_id     String
  user_answer_id  String?
  submited_answer option?    @relation(references: [id], fields: [user_answer_id], onDelete: Cascade)
  question        question   @relation(references: [id], fields: [question_id], onDelete: Cascade)
  assignmnet      assignment @relation(references: [id], fields: [assignment_id], onDelete: Cascade)
  meta            Json       @default("{}")

  @@id([question_id, assignment_id])
}

model question_bucket {
  id         String       @id @default(uuid())
  name       String
  created_by admin        @relation(references: [id], fields: [creator_id])
  creator_id Int
  created_at DateTime     @default(now())
  updated_at DateTime     @updatedAt
  org_id     Int
  assign_to  user_type    @default(TECHNICIAN)
  org        organization @relation(references: [id], fields: [org_id])
  question   question[]
  meta       Json         @default("{}")
}

enum notification_priority {
  HIGH
  MEDIUM
  LOW
}

enum section_type {
  LAUNCH_SCREEN
  TRAINING
}

model notification {
  id          String                @id @default(uuid())
  user_id     Int
  section_id  Int?
  title       String
  message     String
  is_read     Boolean               @default(false)
  redirect_to String?
  priority    notification_priority @default(LOW)
  meta        Json                  @default("{}")
  created_at  DateTime              @default(now())
  updated_at  DateTime              @updatedAt
  is_deleted  Boolean               @default(false)
  user        user                  @relation(fields: [user_id], references: [id], onDelete: Cascade)
}

model mascot_alert {
  id           String                @id @default(uuid())
  user_id      Int
  section_id   Int?
  type         section_type
  title        String?
  message      String
  is_read      Boolean               @default(false)
  cta_required Boolean               @default(false)
  cta_link     String?
  priority     notification_priority @default(LOW)
  meta         Json?                 @default("{}")
  created_at   DateTime              @default(now())
  is_deleted   Boolean               @default(false)
  cta_text     String                @default("")
  user         user                  @relation(fields: [user_id], references: [id], onDelete: Cascade)
}

enum data_deletion_request_status {
  PENDING
  DELETED
  NOT_VALID
}

model data_deletion_request {
  id         String                       @id @default(uuid())
  name       String
  email      String?
  phone      String?
  reason     String?
  status     data_deletion_request_status @default(PENDING)
  created_at DateTime                     @default(now())
  updated_at DateTime                     @updatedAt
}

enum CourseType {
  SOFT
  TECHNICAL
}

enum CourseMethod {
  PRACTICAL
  THEORETICAL
}

enum CourseStatus {
  DRAFT
  ACTIVE
  INACTIVE
}

enum business_vertical {
  MKW_AND_MEASUREMENT
  LOOSE_FURNITURE_AND_AMAZON
  WARRANTY_AND_ELECTRIC
  WATER
  SMART_LOCKS
  DOORS
}

model course {
  id                          String                        @id @default(uuid())
  internal_course_id          String                        @unique
  name                        String
  published                   Boolean                       @default(false)
  duration                    Int                           @default(0)
  published_on                DateTime?
  is_active                   Boolean                       @default(false)
  status                      CourseStatus                  @default(DRAFT)
  image                       String?
  description                 String?
  objective                   String?
  created_at                  DateTime                      @default(now())
  updated_at                  DateTime                      @updatedAt
  creator                     admin                         @relation(fields: [admin_id], references: [id])
  meta                        Json                          @default("{}")
  type                        CourseType?
  method                      CourseMethod?
  admin_id                    Int
  is_deleted                  Boolean                       @default(false)
  chapter                     chapter[]
  rating                      Float?
  designation_id              String?
  business_vertical           business_vertical
  designation                 designation?                  @relation(fields: [designation_id], references: [id], onDelete: Cascade)
  user_course                 course_user_map[]
  expertise_course_map        expertise_course_map[]
  course_assessment_map       course_assignment_map[]
  user_chapter_progress_track user_chapter_progress_track[]
  course_designation_map      course_designation_map[]
}

model chapter {
  id                          String                        @id @default(uuid())
  title                       String
  order                       Int
  duration                    Int?
  objective                   String?
  course                      course                        @relation(fields: [course_id], references: [id], onDelete: Cascade)
  course_id                   String
  meta                        Json                          @default("{}")
  lesson                      lesson[]
  is_deleted                  Boolean                       @default(false)
  created_at                  DateTime                      @default(now())
  updated_at                  DateTime                      @default(now())
  user_lesson_progress_track  user_lesson_progress_track[]
  user_chapter_progress_track user_chapter_progress_track[]
}

model lesson {
  id                         String                       @id @default(uuid())
  title                      String
  order                      Int
  duration                   Int?
  chapter                    chapter                      @relation(fields: [chapter_id], references: [id], onDelete: Cascade)
  objective                  String?
  content                    content?                     @relation(fields: [content_id], references: [id], onDelete: Cascade)
  content_id                 String?
  meta                       Json                         @default("{}")
  chapter_id                 String
  is_deleted                 Boolean                      @default(false)
  created_at                 DateTime                     @default(now())
  updated_at                 DateTime                     @default(now())
  user_lesson_progress_track user_lesson_progress_track[]
}

enum ContentType {
  VIDEO
  IMAGE
}

enum ContentAssignedTo {
  USERS
  LESSONS
}

model content {
  id                      String                    @id @default(uuid())
  internal_content_id     String                    @unique
  title                   String
  description             String?                   @db.Text
  image                   String?
  type                    ContentType
  content_assigned_to     ContentAssignedTo?
  published               Boolean                   @default(false)
  published_on            DateTime?
  is_active               Boolean                   @default(true)
  meta                    Json                      @default("{}")
  created_at              DateTime                  @default(now())
  updated_at              DateTime                  @updatedAt
  is_deleted              Boolean                   @default(false)
  admin_id                Int
  creator                 admin                     @relation(fields: [admin_id], references: [id])
  lesson                  lesson[]
  content_user_map        content_user_map[]
  expertise_content_map   expertise_content_map[]
  user_video_progress     user_video_progress[]
  video_content           video_content?
  content_designation_map content_designation_map[]
}

model video_content {
  id         String   @id @default(uuid())
  content_id String   @unique
  video_url  String
  duration   Int // Duration of the video in seconds
  meta       Json     @default("{}")
  created_at DateTime @default(now())
  updated_at DateTime @updatedAt
  content    content  @relation(fields: [content_id], references: [id], onDelete: Cascade)
}

model course_assignment_map {
  course               course            @relation(fields: [course_id], references: [id], onDelete: Cascade)
  assignment_config    assignment_config @relation(fields: [assignment_config_id], references: [id], onDelete: Cascade)
  assignment_config_id String
  course_id            String
  created_at           DateTime          @default(now())
  updated_at           DateTime          @updatedAt

  @@id([course_id, assignment_config_id])
}

model content_user_map {
  content    content  @relation(fields: [content_id], references: [id], onDelete: Cascade)
  user       user     @relation(fields: [user_id], references: [id], onDelete: Cascade)
  admin      admin    @relation(fields: [admin_id], references: [id], onDelete: Cascade)
  content_id String
  user_id    Int
  created_at DateTime @default(now())
  updated_at DateTime @updatedAt
  admin_id   Int

  @@id([content_id, user_id])
}

model expertise_course_map {
  expertise    expertise @relation(fields: [expertise_id], references: [id], onDelete: Cascade)
  course       course    @relation(fields: [course_id], references: [id], onDelete: Cascade)
  expertise_id Int
  course_id    String
  created_at   DateTime  @default(now())

  @@id([expertise_id, course_id])
}

model expertise_content_map {
  expertise    expertise @relation(fields: [expertise_id], references: [id], onDelete: Cascade)
  content      content   @relation(fields: [content_id], references: [id], onDelete: Cascade)
  expertise_id Int
  content_id   String
  created_at   DateTime  @default(now())

  @@id([expertise_id, content_id])
}

enum UserCourseProgressStatus {
  PENDING
  IN_PROGRESS
  COMPLETED
}

model course_user_assignment_map {
  id                   String            @id @default(uuid())
  assignment_config_id String
  assignment_id        String?           @unique
  course_user_map_id   String
  user_id              Int
  is_deleted           Boolean           @default(false)
  is_active            Boolean           @default(true)
  user                 user              @relation(references: [id], fields: [user_id], onDelete: Cascade)
  assignment_config    assignment_config @relation(references: [id], fields: [assignment_config_id], onDelete: Cascade)
  assignment           assignment?       @relation(fields: [assignment_id], references: [id], onDelete: Cascade)
  course_user_map      course_user_map?  @relation(fields: [course_user_map_id], references: [id], onDelete: Cascade)
}

model course_user_map {
  id                         String                        @id @default(uuid())
  progress_status            UserCourseProgressStatus      @default(PENDING)
  course_completed           Boolean                       @default(false)
  training_completed         Boolean                       @default(false)
  time_spent                 Int                           @default(0)
  admin_id                   Int?
  course_id                  String
  user_id                    Int
  meta                       Json                          @default("{}")
  is_deleted                 Boolean                       @default(false)
  is_active                  Boolean                       @default(true)
  created_at                 DateTime                      @default(now())
  updated_at                 DateTime                      @updatedAt
  course                     course                        @relation(fields: [course_id], references: [id], onDelete: Cascade)
  user                       user                          @relation(fields: [user_id], references: [id], onDelete: Cascade)
  admin                      admin?                        @relation(fields: [admin_id], references: [id], onDelete: Cascade)
  course_progress            user_chapter_progress_track[]
  course_user_assignment_map course_user_assignment_map[]

  @@unique([user_id, course_id])
}

model user_chapter_progress_track {
  id               String                       @id @default(uuid())
  user_id          Int
  course_id        String
  chapter_id       String
  created_at       DateTime                     @default(now())
  updated_at       DateTime                     @updatedAt
  time_spent       Int
  is_completed     Boolean                      @default(false)
  is_current       Boolean                      @default(false)
  is_deleted       Boolean                      @default(false)
  is_unlocked      Boolean                      @default(false)
  user_course_id   String
  user_course      course_user_map              @relation(fields: [user_course_id], references: [id], onDelete: Cascade)
  course           course                       @relation(fields: [course_id], references: [id], onDelete: Cascade)
  user             user                         @relation(fields: [user_id], references: [id], onDelete: Cascade)
  chapter          chapter                      @relation(fields: [chapter_id], references: [id], onDelete: Cascade)
  lessons_progress user_lesson_progress_track[]

  @@unique([chapter_id, user_id])
}

model user_lesson_progress_track {
  id                     String                      @id @default(uuid())
  user_id                Int
  time_spent             Int?
  chapter_id             String
  created_at             DateTime                    @default(now())
  updated_at             DateTime                    @updatedAt
  is_completed           Boolean                     @default(false)
  is_current             Boolean                     @default(false)
  is_unlocked            Boolean                     @default(false)
  is_deleted             Boolean                     @default(false)
  lesson_id              String
  chapter_progress_id    String
  lesson                 lesson                      @relation(fields: [lesson_id], references: [id], onDelete: Cascade)
  chapter                chapter                     @relation(fields: [chapter_id], references: [id], onDelete: Cascade)
  chapter_progress_track user_chapter_progress_track @relation(fields: [chapter_progress_id], references: [id], onDelete: Cascade)

  @@unique([lesson_id, user_id])
}

model user_video_progress {
  id           String   @id @default(uuid())
  content_id   String
  user_id      Int
  progress     Int // Progress in seconds
  is_completed Boolean  @default(false)
  is_deleted   Boolean  @default(false)
  created_at   DateTime @default(now())
  updated_at   DateTime @default(now())
  content      content  @relation(fields: [content_id], references: [id], onDelete: Cascade)
  user         user     @relation(fields: [user_id], references: [id], onDelete: Cascade)

  @@unique([content_id, user_id])
}

model banner_config {
  id           String   @id @default(uuid())
  title        String
  description  String   @db.Text
  hero_image   String
  image        String?
  cta_required Boolean  @default(false)
  cta_link     String?
  cta_text     String?
  meta         Json     @default("{}")
  is_published Boolean  @default(false)
  is_active    Boolean  @default(true)
  is_deleted   Boolean  @default(false)
  created_at   DateTime @default(now())
  updated_at   DateTime @updatedAt
  banner       banner[]
  admin        admin?   @relation(fields: [admin_id], references: [id])
  admin_id     Int?
}

model banner {
  id               String        @id @default(uuid())
  banner_config_id String
  order            Int
  expiry_time      DateTime?
  is_active        Boolean       @default(true)
  is_deleted       Boolean       @default(false)
  created_at       DateTime      @default(now())
  updated_at       DateTime      @updatedAt
  banner_config    banner_config @relation(fields: [banner_config_id], references: [id])
  admin            admin?        @relation(fields: [admin_id], references: [id])
  admin_id         Int?
}

model course_designation_map {
  id             String      @id @default(uuid())
  designation_id String
  course_id      String
  designation    designation @relation(fields: [designation_id], references: [id], onDelete: Cascade)
  course         course      @relation(fields: [course_id], references: [id], onDelete: Cascade)
  created_at     DateTime    @default(now())
  updated_at     DateTime    @updatedAt

  @@unique([designation_id, course_id])
}

model content_designation_map {
  id             String      @id @default(uuid())
  designation_id String
  content_id     String
  designation    designation @relation(fields: [designation_id], references: [id], onDelete: Cascade)
  content        content     @relation(fields: [content_id], references: [id], onDelete: Cascade)
  created_at     DateTime    @default(now())
  updated_at     DateTime    @updatedAt

  @@unique([designation_id, content_id])
}

model designation {
  id                        String                      @id @default(uuid())
  name                      String
  level                     Int                         @default(1)
  is_active                 Boolean                     @default(true)
  is_deleted                Boolean                     @default(false)
  created_at                DateTime                    @default(now())
  updated_at                DateTime                    @updatedAt
  user                      user[]
  admin                     admin?                      @relation(fields: [admin_id], references: [id])
  admin_id                  Int?
  designation_expertise_map designation_expertise_map[]
  course                    course[]
  course_designation_map    course_designation_map[]
  content_designation_map   content_designation_map[]
}

model designation_expertise_map {
  id             Int         @id @default(autoincrement())
  designation_id String
  expertise_id   Int
  designation    designation @relation(fields: [designation_id], references: [id], onDelete: Cascade)
  expertise      expertise   @relation(fields: [expertise_id], references: [id])

  @@unique([designation_id, expertise_id])
}

model contact {
  id               String                   @id @default(uuid())
  name             String
  phone            String
  image_url        String?
  users            user_contact_map[]
  meta             Json?
  timeline         Json?
  is_active        Boolean                  @default(true)
  is_deleted       Boolean                  @default(false)
  moved_to_users   Boolean                  @default(false)
  created_at       DateTime                 @default(now())
  updated_at       DateTime                 @updatedAt
  user             user?
  phone_contact_id String
  invitations      team_member_invitation[]

  @@unique([name, phone], name: "unique_name_phone")
}

model user_contact_map {
  user_id    Int
  contact_id String
  is_active  Boolean  @default(true)
  created_at DateTime @default(now())
  updated_at DateTime @updatedAt
  user       user     @relation(fields: [user_id], references: [id], onDelete: Cascade)
  contact    contact  @relation(fields: [contact_id], references: [id], onDelete: Cascade)

  @@id([user_id, contact_id])
}

model document_number_config {
  id               String                  @id @default(uuid())
  validation_type  DocNumberValidationType @default(NUMERIC)
  max_length       Int
  min_length       Int
  creator_id       Int
  creator          admin                   @relation(fields: [creator_id], references: [id])
  created_at       DateTime                @default(now())
  updated_at       DateTime                @updatedAt
  document_type    document_type           @relation(fields: [document_type_id], references: [id], onDelete: Cascade)
  document_type_id Int                     @unique
}

enum TeamMemberInvitationStatus {
  INVITED
  PENDING
  APPROVED
  REJECTED
  EXPIRED
}

model team_member_invitation {
  id              String                     @id @default(uuid())
  inviter_id      Int
  contact_id      String
  invited_user_id Int?
  status          TeamMemberInvitationStatus @default(INVITED)
  message         String?
  meta            Json                       @default("{}")
  expires_at      DateTime                   @default(dbgenerated("NOW() + interval '7 days'"))
  created_at      DateTime                   @default(now())
  updated_at      DateTime                   @updatedAt

  inviter      user    @relation("inviter", fields: [inviter_id], references: [id], onDelete: Cascade)
  contact      contact @relation(fields: [contact_id], references: [id], onDelete: Cascade)
  invited_user user?   @relation("invited", fields: [invited_user_id], references: [id], onDelete: SetNull)
  user         user?   @relation(fields: [userId], references: [id])
  userId       Int?

  @@unique([inviter_id, contact_id])
  @@index([status])
  @@index([expires_at])
  @@map("team_member_invitations")
}

model history {
  id            String   @id @default(uuid())
  active        Boolean  @default(true)
  entity_module String? // Table name, e.g., 'organization', 'user'
  entity_id     String? // Record ID of the modified entity
  org_id        Int?
  user_id       Int?
  update_type   String? // CREATE, UPDATE, DELETE
  new_values    Json? // Stores old & new values for updates
  event_title   String? //trigger event message
  creator_id    Int? // User ID performing the action
  creator_type  String? //user or admin
  created_at    DateTime @default(now())
  meta          Json? //  user system details and user name and id , org id
}

enum AppPlatformType {
  ANDROID
  WEB
}

enum AppInstallationStatus {
  INSTALLED
  UNINSTALLED
}

model app_installation {
  id                        String                      @id @default(uuid())
  user_id                   Int?
  user                      user?                       @relation(fields: [user_id], references: [id], onDelete: Cascade)
  platform_type             AppPlatformType
  installation_date         DateTime                    @default(now())
  uninstallation_date       DateTime?
  status                    AppInstallationStatus       @default(INSTALLED)
  device_id                 String?                     @unique
  city                      String?
  state                     String?
  country                   String?
  ip_address                String?
  user_agent                String?
  referrer                  String?
  device_info               Json?                       @default("{}")
  app_version               String?
  installation_source       String? //play store, manual, etc
  last_active_at            DateTime?
  is_dropped                Boolean                     @default(true)
  session_count             Int                         @default(0)
  total_time_spent          Int                         @default(0) // in seconds
  push_enabled              Boolean                     @default(true)
  push_token                String?
  meta                      Json?                       @default("{}")
  created_at                DateTime                    @default(now())
  updated_at                DateTime                    @updatedAt
  user_activities           user_activity[]
  app_installation_user_map app_installation_user_map[]
  device_activity_daily     device_activity_daily[]

  @@index([user_id, platform_type])
  @@index([installation_date])
  @@index([status])
  @@index([last_active_at])
  @@index([app_version])
}

model app_installation_user_map {
  id               String           @id @default(uuid())
  device_id        String
  user_id          Int
  app_installation app_installation @relation(fields: [device_id], references: [device_id], onDelete: Cascade)
  user             user             @relation(fields: [user_id], references: [id], onDelete: Cascade)

  @@unique([device_id, user_id])
}

model user_session_screen_recording {
  id         String         @id @default(uuid())
  user_id    Int?
  user       user?          @relation(fields: [user_id], references: [id], onDelete: Cascade)
  file_path  String // Path to the recorded video file
  start_time DateTime
  session_id String
  end_time   DateTime?
  meta       Json?          @default("{}")
  duration   Int // Duration in seconds
  created_at DateTime       @default(now())
  session    user_activity? @relation(fields: [session_id], references: [id], onDelete: Cascade)

  @@index([user_id])
}

enum UserFunnelStage {
  VISITOR
  LOGIN_PAGE
  SIGNUP_START
  SIGNUP_COMPLETE
  GENERAL_DETAILS
  WORK_EXPERIENCE
  DOCUMENTS
  DOCUMENT_VERIFICATION
  SKILLS_ASSESSMENT
  INTERVIEW_SCHEDULED
  INTERVIEW_COMPLETED
  TRAINING_STARTED
  TRAINING_COMPLETED
  SCREENING_TEST
  ONBOARDING_COMPLETE
  ACTIVE_USER
  TECHNICIAN_HOMEPAGE
  SERVICE_PROVIDER_HOMEPAGE
  TRANSFERRED_TO_TMS
}

//history of the analytics of the user
model daily_analytics {
  id                         String   @id @default(uuid())
  date                       DateTime
  app_installations          Int      @default(0) //android
  app_installation_breakdown Json?    @default("{}") //to know the breakdown of the app installations by platform type
  unique_visitors            Int      @default(0)
  unique_visitors_breakdown  Json?    @default("{}") //to know the breakdown of the unique visitors by platform type
  signup                     Int      @default(0)
  signup_breakdown           Json?    @default("{}") //to know the breakdown of the signups by platform type
  login                      Int      @default(0)
  login_breakdown            Json?    @default("{}") //to know the breakdown of the logins by platform type
  drop_off                   Int      @default(0)
  drop_off_breakdown         Json?    @default("{}") //to know the breakdown of the dropoffs by platform type
  created_at                 DateTime @default(now())
  updated_at                 DateTime @updatedAt

  @@unique([date])
  @@index([date])
}

model user_activity {
  id                            String                          @id @default(uuid())
  user_id                       Int? // Optional since visitors may not be logged in
  session_id                    String
  ip_address                    String?
  activity_type                 String
  platform_type                 AppPlatformType
  language                      preferred_language
  funnel_stage                  UserFunnelStage?
  start_time                    DateTime
  end_time                      DateTime?
  time_spent                    Int?
  url_path                      String?
  user_agent                    String?
  took_action                   Boolean                         @default(false)
  meta                          Json?                           @default("{}")
  created_at                    DateTime                        @default(now())
  updated_at                    DateTime                        @updatedAt
  user                          user?                           @relation(fields: [user_id], references: [id], onDelete: SetNull)
  app_installation_id           String?
  app_installation              app_installation?               @relation(fields: [app_installation_id], references: [id], onDelete: SetNull)
  user_session_screen_recording user_session_screen_recording[]

  @@index([user_id])
  @@index([session_id])
  @@index([activity_type])
  @@index([platform_type])
  @@index([app_installation_id])
}

// Track daily device activity for easier DAD and MAD calculations
model device_activity_daily {
  id                  String          @id @default(uuid())
  device_id           String
  app_installation_id String
  date                DateTime
  user_id             Int?
  platform_type       AppPlatformType
  created_at          DateTime        @default(now())

  app_installation app_installation @relation(fields: [app_installation_id], references: [id], onDelete: Cascade)
  user             user?            @relation(fields: [user_id], references: [id], onDelete: SetNull)

  @@unique([device_id, date])
  @@index([date])
  @@index([device_id])
  @@index([user_id])
  @@index([platform_type])
}

model admin_role {
  admin_role_id         Int                     @id @default(autoincrement())
  name                  String                  @unique
  description           String?
  is_root_admin         Boolean                 @default(false) //cannot edit permissions of root admin
  has_admin_privileges  Boolean                 @default(false) //can create and manage roles
  can_reject            Boolean                 @default(false)
  can_ban               Boolean                 @default(false)
  can_accept            Boolean                 @default(false)
  can_download          Boolean                 @default(false)
  created_at            DateTime                @default(now())
  updated_at            DateTime?               @updatedAt
  creator_id            Int
  updated_by            Int?
  admin_role_permission admin_role_permission[] @relation("role_permissions")
  admin                 admin[]                 @relation("admin_role_relation")
}

//stores a particular role's particular feature permissions
model admin_role_permission {
  id                      Int                     @id @default(autoincrement())
  admin_role_id           Int
  feature_id              Int
  permission_for_user     Json?
  permission_for_sp       Json?
  admin_role              admin_role              @relation("role_permissions", fields: [admin_role_id], references: [admin_role_id], onDelete: Cascade)
  admin_protected_feature admin_protected_feature @relation("permission_feature", fields: [feature_id], references: [feature_id], onDelete: Cascade)
}

//defines the available feature permissions
model admin_protected_feature {
  feature_id       Int     @id @default(autoincrement())
  is_module        Boolean @default(false)
  parent_module_id Int? // draft ID for parent module
  module_id        Int? // draft ID for module

  parent_module_name  String?
  module_name         String?
  sub_module_name     String? // name of the page route
  sub_module_url_user String? //ui page individual url of sub-module
  sub_module_url_sp   String? //ui page sp url of sub-module

  icon_name  String    @default("RiLayoutGridLine")
  section_id String? //in future if we want to implement permissions on a particular section
  created_at DateTime  @default(now())
  created_by Int       @default(1)
  is_active  Boolean   @default(true)
  updated_at DateTime? @updatedAt

  admin_role_permission admin_role_permission[] @relation("permission_feature")
}

//list of all modules in navigation bar and permission list
model admin_module {
  module_name        String    @unique
  parent_module_name String?
  parent_module_id   Int?
  module_id          Int       @id @default(autoincrement())
  is_published       Boolean   @default(false)
  is_active          Boolean   @default(true)
  icon_name          String    @default("RiLayoutGridLine")
  visible_in_sp      Boolean   @default(true)
  visible_in_users   Boolean   @default(true)
  created_at         DateTime?
  created_by         Int       @default(1)
  published_at       DateTime?
  published_by       Int?
}

model policy {
  id                   String                 @id @default(uuid())
  policy_name          String
  policy_name_hindi    String?
  url                  String?
  is_active            Boolean                @default(true)
  created_at           DateTime               @default(now())
  updated_at           DateTime               @updatedAt
  user_policy_tracking user_policy_tracking[]
}

model user_policy_tracking {
  id         Int      @id @default(autoincrement())
  user_id    Int
  policy_id  String
  accepted   Boolean  @default(false)
  created_at DateTime @default(now())
  updated_at DateTime @updatedAt
  user       user     @relation(fields: [user_id], references: [id], onDelete: Cascade)
  policy     policy   @relation(fields: [policy_id], references: [id], onDelete: Cascade)

  @@unique([user_id, policy_id])
}

model checklist {
  id                  String                @id @default(uuid())
  name                String
  is_active_sp        Boolean               @default(true)
  created_at          DateTime              @default(now())
  updated_at          DateTime?             @updatedAt
  user_checklist_data user_checklist_data[]
}

model user_checklist_data {
  id                      String    @id @default(uuid())
  user_id                 Int
  checklist_id            String
  hiring_completed        Boolean   @default(false)
  hiring_completed_at     DateTime?
  technician_completed    Boolean   @default(false)
  technician_completed_at DateTime?
  created_at              DateTime  @default(now())
  updated_at              DateTime  @updatedAt
  created_by              Int?
  edited_by               Int?
  meta                    Json?
  user                    user      @relation(fields: [user_id], references: [id], onDelete: Cascade)
  checklist               checklist @relation(fields: [checklist_id], references: [id], onDelete: Cascade)
  creator                 admin?    @relation("creator_checklist_relation", fields: [created_by], references: [id])
  editor                  admin?    @relation("editor_checklist_relation", fields: [edited_by], references: [id])

  @@unique([user_id, checklist_id])
}
