{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "1.0.0", "description": "This is the backend of the rojgaar app", "main": "index.js", "scripts": {"dev": "NODE_ENV=development node build.js", "build": "NODE_ENV=production node build.js", "dev-win": "set NODE_OPTIONS=--max-old-space-size=4096 && nodemon src/index.ts", "build-win": "set NODE_OPTIONS=--max-old-space-size=4096 && node build.js", "start": "node dist/index.js", "prepare": "husky install", "lint": "./node_modules/.bin/eslint --max-warnings=0 --ext=ts,tsx src", "codegen": "graphql-codegen", "sqs-dev": "nodemon src/communication/sqs-consumer.ts", "sqs": "node dist/communication/sqs-consumer.js", "test": "jest --config jest.config.js", "db_tool": "ts-node db_helpers/index.ts", "create-db": "ts-node db_helpers/create_db.ts"}, "author": "wify.co.in", "license": "ISC", "devDependencies": {"@babel/core": "^7.23.2", "@babel/preset-env": "^7.23.2", "@babel/preset-typescript": "^7.23.2", "@faker-js/faker": "^8.2.0", "@graphql-codegen/cli": "^5.0.0", "@graphql-codegen/typescript": "^4.0.1", "@graphql-codegen/typescript-resolvers": "^4.0.1", "@jest/globals": "^29.7.0", "@parcel/watcher": "^2.3.0", "@types/bcrypt": "^5.0.0", "@types/bull": "^4.10.0", "@types/circular-json": "^0.4.0", "@types/cli-progress": "^3.11.4", "@types/cookie-parser": "^1.4.3", "@types/cors": "^2.8.12", "@types/express": "^4.17.14", "@types/graphql-fields": "^1.3.4", "@types/helmet": "^4.0.0", "@types/is-empty": "^1.2.1", "@types/jest": "^29.5.12", "@types/jsonwebtoken": "^8.5.9", "@types/mime-types": "^2.1.1", "@types/morgan": "^1.9.3", "@types/multer": "^1.4.11", "@types/nodemailer": "^6.4.6", "@types/passport": "^1.0.11", "@types/passport-facebook": "^2.1.11", "@types/pg": "^8.11.13", "@types/uuid": "^9.0.0", "@typescript-eslint/eslint-plugin": "^5.39.0", "@typescript-eslint/parser": "^5.39.0", "babel-jest": "^29.7.0", "cli-progress": "^3.12.0", "concurrently": "^8.2.2", "esbuild": "^0.23.0", "esbuild-plugin-tsc": "^0.4.0", "eslint": "^8.24.0", "eslint-config-prettier": "^8.5.0", "fs-extra": "^11.2.0", "husky": "^8.0.0", "jest": "^29.7.0", "jest-mock-extended": "^3.0.7", "nodemon": "^2.0.22", "pg": "^8.14.1", "prettier-eslint": "^15.0.1", "prisma": "^4.6.1", "regenerator-runtime": "^0.13.11", "ts-jest": "^29.2.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.3.3"}, "dependencies": {"@apollo/federation": "^0.38.1", "@apollo/server": "^4.9.3", "@aws-sdk/client-polly": "^3.614.0", "@aws-sdk/client-s3": "^3.614.0", "@aws-sdk/lib-storage": "^3.629.0", "@aws-sdk/s3-request-presigner": "^3.614.0", "@bull-board/express": "^5.21.1", "@capacitor/push-notifications": "^5.0.6", "@graphql-tools/schema": "^10.0.4", "@newrelic/apollo-server-plugin": "^5.1.0", "@prisma/client": "^4.4.0", "@redis/client": "^1.4.2", "@types/geoip-lite": "^1.4.4", "apollo-server-core": "^3.13.0", "apollo-server-express": "^3.13.0", "axios": "^1.5.1", "bcrypt": "^5.1.1", "body-parser": "^1.20.2", "bull": "^4.15.1", "bull-board": "^2.1.3", "chalk": "4.1.2", "circular-json": "^0.5.9", "class-validator": "^0.14.0", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "csv-parse": "^5.5.6", "dayjs": "^1.11.7", "dotenv": "^16.0.3", "express": "^4.18.1", "firebase-admin": "^11.10.1", "geoip-lite": "^1.4.10", "google-auth-library": "^9.0.0", "googleapis": "^109.0.1", "graphql": "^16.6.0", "graphql-fields": "^2.0.3", "graphql-middleware": "^6.1.35", "graphql-scalars": "^1.22.4", "graphql-shield": "^7.6.5", "graphql-subscriptions": "^2.0.0", "graphql-ws": "^5.14.0", "helmet": "^6.0.0", "is-empty": "^1.2.0", "joi": "^17.6.3", "jsonwebtoken": "^9.0.0", "lodash": "^4.17.21", "mime-types": "^2.1.35", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "newrelic": "^9.11.0", "nodemailer": "^6.9.3", "openai": "^4.24.1", "redis": "^4.5.1", "type-graphql": "^1.1.1", "uuid": "^9.0.0", "ws": "^8.18.0", "xlsx": "^0.18.5"}}