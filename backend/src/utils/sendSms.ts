import { SmsEmailLogs } from '@prisma/client'
import axios from 'axios'
import { logHistory } from '../services/history.service'
import { SMS_DATA } from './envVars'
import { EventList, EventType } from './eventTypes'
import { smsEmailLogger } from './loggers/smsEmail'
import { sendEmailAlert } from './emailAlert'
/**
 * Sends an SMS message to the specified recipient.
 *
 * @param {string} to - The phone number of the recipient.
 * @param {string} message - The content of the SMS message.
 * @return {Promise<string>} - A promise that resolves to the status of the SMS message.
 */
export const sendSms = async (to: string, message: string, meta?: any) => {
    const api_key = SMS_DATA.API_KEY
    const sid = SMS_DATA.SID
    const gateway_domain = SMS_DATA.GATEWAY_DOMAIN
    const path = encodeURI(
        `/V2/http-api.php?apikey=${api_key}&senderid=${sid}&number=${to}&message=${message}`
    )

    try {
        const response = await axios.get(`https://${gateway_domain}${path}`)
        const { status } = response.data.data[0]
        const log: Partial<SmsEmailLogs> = {
            receiver: to,
            type: 'SMS',
            data: { message },
            providerResponse: response.data,
            result: status === 'SUBMITTED',
        }
        if (response.status !== 200) {
            await sendEmailAlert('Supply App SMS Provider Error', response.data, {
                source: 'Supply SMS Provider',
                timestamp: new Date().toISOString(),
            })
        }

        smsEmailLogger(log)
        logHistory({
            ...EventList[EventType.NOTIFICATION_SENT],
            event_title: `${
                EventList[EventType.NOTIFICATION_SENT].event_title
            } SMS`,
            user_id: meta?.user_id,
            org_id: meta?.user_org_id,
            meta: {
                user_id: meta?.user_id,
                user_name: meta?.user_name,
                user_org_id: meta?.user_org_id,
            },
        })
        return status
    } catch (error) {

        await sendEmailAlert('Supply App SMS Provider Error', error, {
            source: 'Supply SMS Provider',
            timestamp: new Date().toISOString(),
        })

        const log: Partial<SmsEmailLogs> = {
            receiver: to,
            type: 'SMS',
            data: { message },
            providerResponse: error,
            result: false,
        }
        smsEmailLogger(log)

        throw error
    }
}
