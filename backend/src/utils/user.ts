import { UserType, user } from '@prisma/client'
import DataProvider from '../data'
import { logHistory } from '../services/history.service'
import { EventList, EventType } from './eventTypes'
const client = DataProvider.getDbInstance()

// This will check a user exists in the database and return the user information.
export const checkIfUserExists = async (email: string, phoneNumber: string) => {
    const userExists = await client.user.findMany({
        where: {
            OR: [{ email }, { phone: phoneNumber }],
        },
    })
    if (userExists.length !== 0 && userExists.at(0)?.email === email) {
        return {
            result: false,
            message: 'Email already exists.',
        }
    }
    if (userExists.length !== 0 && userExists.at(0)?.phone === phoneNumber) {
        return {
            result: false,
            message: 'Phone Number already exists.',
        }
    }
    return { result: true, userData: userExists }
}

// This will check a user exists in the database and return the user information.
export const checkIfUserVerified = async (
    email: string,
    phoneNumber: string
) => {
    const userExists = await client.user.findMany({
        where: {
            OR: [
                { email, emailVerified: true },
                { phone: phoneNumber, phoneVerified: true },
            ],
        },
    })
    if (userExists.length !== 0 && userExists.at(0)?.email === email) {
        return {
            result: false,
            message: 'Email already Verified.',
        }
    }
    if (userExists.length !== 0 && userExists.at(0)?.phone === phoneNumber) {
        return {
            result: false,
            message: 'Phone Number already Verified.',
        }
    }
    return { result: true, userData: userExists }
}

export const updateUserOnboardingTimestamp = async (user: user) => {
    const { onboarding_stage, onboarding_stage_timestamps } = user
    const timestamp = Math.floor(Date.now() / 1000)
    await client.user.update({
        data: {
            onboarding_stage_timestamps: {
                set: [
                    ...onboarding_stage_timestamps.filter(
                        (item: any) =>
                            item.onboarding_stage !== onboarding_stage
                    ),
                    { onboarding_stage, timestamp },
                ],
            },
        },
        where: {
            id: user.id,
        },
    })
}
interface UserWithAdmin {
    admin_id?: number | null
    description?: string
    user_id: number
}
export const createUserTimestamp = async ({
    admin_id,
    description,
    user_id,
}: UserWithAdmin) => {
    try {
        const user = await client.user.findFirst({
            where: { id: user_id },
            select: {
                id: true,
                onboarding_stage: true,
                transfer_to_tms_status: true,
                doc_verification_state: true,
                isActive: true,
                phone: true,
            },
        })

        if (user) {
            const userLogsData = {
                user_id: user.id,
                initiator_id: admin_id || null,
                description: description || null,
                initiated_by: admin_id ? UserType.ADMIN : UserType.USER,
                onboarding_stage: user.onboarding_stage || null,
                transfer_to_tms_status: user.transfer_to_tms_status || null,
                doc_verification_state: user.doc_verification_state,
                isActive: user.isActive,
                user_phone: user.phone,
            }

            await client.user_logs.create({ data: userLogsData })
        }
    } catch (error) {
        console.error(
            'Error Occurrent While Logging User Data ',
            error?.message
        )
    }
}

export const createUserReferral = async (
    user_id: number,
    referral_code: string
) => {
    try {
        const referrer = await client.user_referral_details.findFirst({
            where: {
                referral_code: referral_code,
            },
            select: {
                user_id: true,
            },
        })
        if (!referrer) return Promise.reject(new Error('Referrer Not Found'))

        const user = await client.user.findFirst({
            where: {
                id: user_id,
            },
        })

        await client.referral_system_configuration.findFirst({
            where: {
                organization_id: user?.organization_id,
            },
        })

        await client.user_referral_details.create({
            data: {
                user_id: user_id,
                referred_by_id: referrer?.user_id || -1,
                referral_code: generateReferralCode(user?.name),
            },
        })
    } catch (error) {
        return error
    }
}

export const validateReferralCode = async (referral_code: string) => {
    const referrer = await client.user_referral_details.findFirst({
        where: {
            referral_code: referral_code,
        },
        select: {
            user_id: true,
        },
    })
    if (!referrer)
        return Promise.reject(new Error('Referrer code does not exist'))
    return
}

export const awardReferralPoints = async (user: user) => {
    try {
        const getReferralConfiguration =
            await client.referral_system_configuration.findMany({
                where: {
                    organization_id: user?.organization_id,
                },
            })
        getReferralConfiguration.forEach(async (configuration) => {
            try {
                if (
                    user.onboarding_stage ==
                        configuration?.user_point_redeem_stage &&
                    configuration.referral_enabled
                ) {
                    const referredUser =
                        await client.user_referral_details.findFirst({
                            where: {
                                user_id: user.id || -1,
                            },
                            select: {
                                user_id: true,
                                referred_by_id: true,
                                available_points: true,
                            },
                        })

                    const referrer =
                        await client.user_referral_details.findFirst({
                            where: {
                                user_id: referredUser?.referred_by_id || -1,
                            },
                        })
                    const referredUserLog = await client.user_logs.findMany({
                        where: {
                            user_id: user.id,
                            user: {
                                organization_id: user?.organization_id || -1,
                            },
                            onboarding_stage:
                                configuration?.user_point_redeem_stage,
                        },
                    })

                    //if referred users stage is changed to redeem stage many times then don't award any points
                    if (referredUserLog.length === 1) {
                        //award points to referred
                        await client.user_referral_details.update({
                            where: {
                                user_id: user?.id || -1,
                            },
                            data: {
                                available_points:
                                    (referredUser?.available_points || 0) +
                                    configuration.new_user_points,
                                referral_system_points_ledger: {
                                    create: {
                                        credited: true,
                                        debited: false,
                                        available_balance:
                                            referredUser?.available_points ||
                                            0 + configuration.new_user_points,
                                        credited_amount:
                                            configuration.new_user_points,
                                        user: {
                                            connect: {
                                                id: user?.id || -1,
                                            },
                                        },
                                    },
                                },
                            },
                        })
                        //award points to referrer
                        const user_referral_details =
                            await client.user_referral_details.update({
                                where: {
                                    user_id: referredUser?.referred_by_id || -1,
                                },
                                data: {
                                    available_points:
                                        (referrer?.available_points || 0) +
                                        configuration.existing_user_points,
                                    referral_system_points_ledger: {
                                        create: {
                                            credited: true,
                                            debited: false,
                                            available_balance:
                                                referrer?.available_points ||
                                                0 +
                                                    configuration.existing_user_points,
                                            credited_amount:
                                                configuration.existing_user_points,
                                            user: {
                                                connect: {
                                                    id:
                                                        referredUser?.referred_by_id ||
                                                        -1,
                                                },
                                            },
                                        },
                                    },
                                },
                            })
                        logHistory({
                            entity_id: user_referral_details.id?.toString(),
                            ...EventList[EventType.REFERRAL_REWARD_CREDITED],
                            new_values: {
                                available_points:
                                    (referrer?.available_points || 0) +
                                    configuration.existing_user_points,
                                referral_system_points_ledger: {
                                    credited: true,
                                    debited: false,
                                    available_balance:
                                        referrer?.available_points ||
                                        0 + configuration.existing_user_points,
                                    credited_amount:
                                        configuration.existing_user_points,

                                    user_id: referredUser?.referred_by_id || -1,
                                },
                            },
                            user_id: user?.id || -1,
                            org_id: user?.organization_id || -1,
                            meta: {
                                ip_addr: '',
                                user_agent: '',
                                user_id: user?.id || -1,
                                user_name: user?.name || '',
                                user_org_id: user?.organization_id || -1,
                                creator_id: null,
                                creator_name: '',
                                creator_org_id: null,
                            },
                        })

                        return
                    }
                }
            } catch (error) {
                console.error(error)
            }
        })
    } catch (error) {
        console.error(error)
    }
}

//generates referral code with username as input and concatenates them with random 8 digit characters
export function generateReferralCode(username = 'TECHNICIAN'): string {
    const randomChars = generateRandomString(5) // Generate a random string of 8 characters
    const referralCode =
        username.substring(0, 3).toUpperCase().replace(/\s/g, '') + randomChars // Take only the first 5 characters, remove spaces, and capitalize every word
    return referralCode
}

export function generateRandomString(length = 5) {
    const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
    let randomString = ''
    for (let i = 0; i < length; i++) {
        const randomIndex = Math.floor(Math.random() * characters.length)
        randomString += characters.charAt(randomIndex)
    }
    return randomString
}
