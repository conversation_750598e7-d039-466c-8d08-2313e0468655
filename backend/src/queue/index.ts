import Queue from 'bull'

enum ONBOARDING_QUEUES {
    EMAIL = 'ONBOARDING:EMAIL_QUEUE',
    SMS = 'ONBOARDING:SMS_QUEUE',
    PUSH_NOTIFICATION = 'ONBOARDING:PUSH_NOTIFICATION_QUEUE',
    WHATSAPP = 'ONBOARDING:WHATSAPP_QUEUE',
    TMS_LEAD = 'ONBOARDING:TMS_LEAD_QUEUE',
    TRAINING_ASSESSMENT = 'TRAINING_ASSESSMENT_QUEUE',
    TRAINING_PERFORMANCE_RESULT_EXPORT = 'TRAINING_ASSESSMENT_QUEUE',
    EXPORT_ASSIGNMENT_RESULT = 'ONBOARDING:EXPORT_ASSIGNMENT_RESULT',
    CRON_JOB = 'ONBOARDING:CRON_JOB',
    SYNC_USER_CONTACTS_QUEUE = 'ONBOARDING:SYNC_USER_CONTACTS_QUEUE',
    CONTACTS_EXPORT_QUEUE = 'ONBOARDING:CONTACTS_EXPORT_QUEUE',
    POLICY_TRACKING_EXPORT_QUEUE = 'ONBOARDING:POLICY_TRACKING_EXPORT_QUEUE',
    ASSIGNMENT_EXPORT_QUEUE = 'ONBOARDING:ASSIGNMENT_EXPORT_QUEUE',
    EXPORT_SP_DATA_QUEUE = 'ONBOARDING:EXPORT_SP_DATA_QUEUE',
    USER_TTT_DATA_EXPORT_QUEUE = 'USER_TTT_DATA_EXPORT_QUEUE',
}

import { startContactsExportQueue } from '../services/contacts.service'
import { startPolicyTrackingExportQueue } from '../services/policy.service'
import { REDIS_URL } from '../utils/envVars'
import { startAssignmentConsumer } from './assignment-consumer'
import { startSQSConsumer } from './communication/sqs-consumer'
import {
    EmailData,
    PushNotificationData,
    SMSData,
    WhatsAppData,
} from './communication/types'
import { startSchedulerConsumer } from './scheduler/scheduler-consumer'
import { startSpDataExportQueue } from './service-provider-consumer'
import { startSyncContactsConsumer } from './sync-contacts-consumer'
import { TmsLeadInputData, startTmsConsumer } from './tms-consumer'
import {
    AssignTrainingData,
    startTrainingConsumer,
    startTrainingPerformanceResultsExportConsumer,
} from './training-consumer'
import { startExportTTTDataConsumer } from './tranfer-to-tms-consumer'

class Queues {
    private static defaultQueueSettings = {
        defaultJobOptions: {
            attempts: 3,
            removeOnComplete: {
                age: 24 * 3600, // Keep completed jobs for 24 hours
                count: 1000, // Keep last 1000 completed jobs
            },
            removeOnFail: {
                age: 7 * 24 * 3600, // Keep failed jobs for 7 days
                count: 5000, // Keep last 5000 failed jobs
            },
            backoff: {
                type: 'exponential',
                delay: 1000, // Start with 1 second delay
            },
        },
        settings: {
            lockDuration: 30000, // Lock duration for jobs (30 seconds)
            stalledInterval: 30000, // Check for stalled jobs every 30 seconds
            maxStalledCount: 2, // Number of times a job can be marked as stalled before being failed
            guardInterval: 5000, // How often to check for stalled jobs (5 seconds)
            drainDelay: 5, // How long to wait before draining the queue
        },
    }

    // communication system queues
    public static emailQueue = new Queue<EmailData>(
        ONBOARDING_QUEUES.EMAIL,
        REDIS_URL,
        this.defaultQueueSettings
    )
    public static whatsAppQueue = new Queue<WhatsAppData<any>>(
        ONBOARDING_QUEUES.WHATSAPP,
        REDIS_URL,
        this.defaultQueueSettings
    )
    public static smsQueue = new Queue<SMSData>(
        ONBOARDING_QUEUES.SMS,
        REDIS_URL,
        this.defaultQueueSettings
    )
    public static pushNotificationQueue = new Queue<PushNotificationData>(
        ONBOARDING_QUEUES.PUSH_NOTIFICATION,
        REDIS_URL,
        this.defaultQueueSettings
    )

    // tms com queues
    public static tmsLeadQueue = new Queue<TmsLeadInputData<any>>(
        ONBOARDING_QUEUES.TMS_LEAD,
        REDIS_URL,
        this.defaultQueueSettings
    )

    //training assessment
    public static trainingAssessmentQueue = new Queue<AssignTrainingData>(
        ONBOARDING_QUEUES.TRAINING_ASSESSMENT,
        REDIS_URL,
        this.defaultQueueSettings
    )
    //training assessment
    public static trainingPerformanceResultExport = new Queue<any>(
        ONBOARDING_QUEUES.TRAINING_PERFORMANCE_RESULT_EXPORT,
        REDIS_URL,
        this.defaultQueueSettings
    )

    //export assignment result
    public static exportAssignmentResultQueue = new Queue<any>(
        ONBOARDING_QUEUES.EXPORT_ASSIGNMENT_RESULT,
        REDIS_URL,
        this.defaultQueueSettings
    )

    //sync contacts queue
    public static syncUserContactsQueue = new Queue<any>(
        ONBOARDING_QUEUES.SYNC_USER_CONTACTS_QUEUE,
        REDIS_URL,
        this.defaultQueueSettings
    )

    //contacts export queue
    public static contactsExportQueue = new Queue<any>(
        ONBOARDING_QUEUES.CONTACTS_EXPORT_QUEUE,
        REDIS_URL,
        this.defaultQueueSettings
    )
    public static policyTrackingExportQueue = new Queue<any>(
        ONBOARDING_QUEUES.POLICY_TRACKING_EXPORT_QUEUE,
        REDIS_URL,
        this.defaultQueueSettings
    )

    //export assignment result
    public static exportSpDataQueue = new Queue<any>(
        ONBOARDING_QUEUES.EXPORT_SP_DATA_QUEUE,
        REDIS_URL,
        this.defaultQueueSettings
    )

    // export user ttt data queue
    public static exportUserTTTDataQueue = new Queue<any>(
        ONBOARDING_QUEUES.USER_TTT_DATA_EXPORT_QUEUE,
        REDIS_URL,
        this.defaultQueueSettings
    )

    //schedulers
    public static cronJobQueue = new Queue<any>(
        ONBOARDING_QUEUES.CRON_JOB,
        REDIS_URL,
        {
            ...this.defaultQueueSettings,
            defaultJobOptions: {
                ...this.defaultQueueSettings.defaultJobOptions,
                removeOnComplete: false, // Don't remove completed cron jobs
                removeOnFail: false, // Don't remove failed cron jobs
            },
        }
    )

    public static getAllQueues(): Queue.Queue[] {
        return [
            this.emailQueue,
            this.whatsAppQueue,
            this.smsQueue,
            this.pushNotificationQueue,
            this.tmsLeadQueue,
            this.trainingAssessmentQueue,
            this.exportAssignmentResultQueue,
            this.cronJobQueue,
            this.syncUserContactsQueue,
            this.contactsExportQueue,
            this.policyTrackingExportQueue,
            this.exportSpDataQueue,
            this.exportUserTTTDataQueue,
        ]
    }
}

export default Queues

// starting all consumers
startSQSConsumer()
startTmsConsumer()
startTrainingConsumer()
startAssignmentConsumer()
startSchedulerConsumer()
startTrainingPerformanceResultsExportConsumer()
startSyncContactsConsumer()
startContactsExportQueue()
startSpDataExportQueue()
startPolicyTrackingExportQueue()
startExportTTTDataConsumer()
