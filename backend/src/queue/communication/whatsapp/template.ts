export type WhatsappTemplateName =
    | 'signup_technician_onboarding'
    | 'v2_sign_up_signup_fu1'
    | 'v2_sign_up_signup_fu2'
    | 'v2_sign_up_signup_fu3'
    | 'v2_verification_completed'
    | 'v2_verification_failed'
    | 'v2_interview_schedule'
    | 'v2_interview_completed'
    | 'v2_successfully_onboarded'
    | 'sp_team_member'
    | 'onboarded_feedback_link'
    | 'referral_invite_link'

export const templates = [
    {
        url: 'https://campaigns.hellotars.com/send_single_whatsapp_notification/campaign_id/whatsapp__ZIDw',
        token: 'tars_2005d5ea25c577029991c3ddac445eb819daaecfec4559716f5c4894f5fc9404',
        template_name: 'signup_technician_onboarding' as WhatsappTemplateName,
    },
    {
        url: 'https://campaigns.hellotars.com/send_single_whatsapp_notification/campaign_id/whatsapp__0ld9',
        token: 'tars_a2ec13a7203d72eb743b4c3d5e3713bc19d7c9d307ae3797c0b2beda6afa667b',
        template_name: 'v2_sign_up_signup_fu1' as WhatsappTemplateName,
        note: 'When the User Did not do anything after signing up 24hr ',
    },
    {
        url: 'https://campaigns.hellotars.com/send_single_whatsapp_notification/campaign_id/whatsapp__0ld9',
        token: 'tars_a2ec13a7203d72eb743b4c3d5e3713bc19d7c9d307ae3797c0b2beda6afa667b',
        template_name: 'v2_sign_up_signup_fu2' as WhatsappTemplateName,
        note: 'When the User did not do anything after signing up 42hr ',
    },
    {
        url: 'https://campaigns.hellotars.com/send_single_whatsapp_notification/campaign_id/whatsapp__GOzi',
        token: 'tars_c334febb33ac4645040fb0b6c5f64222f0903307b907922aaedd7a2cb170a860',
        template_name: 'v2_sign_up_signup_fu3',
        note: 'When the User did not do anything after signing up 72hr',
    },
    {
        url: 'https://campaigns.hellotars.com/send_single_whatsapp_notification/campaign_id/whatsapp__rzZG',
        token: 'tars_d3cac04de9a48b42bbe76d22efaa342bfae2c9bd1fc87f8cce8c678a4689e113',
        template_name: 'v2_verification_completed',
        note: 'When the user document verification is completed ',
    },
    {
        url: 'https://campaigns.hellotars.com/send_single_whatsapp_notification/campaign_id/whatsapp__7WPF',
        token: 'tars_96295a77acd425e7a4dc024e3b4a6dc56f3ceb9c516cf932d1ae48abbc34cebf',
        template_name: 'v2_verification_failed',
        note: 'When the user document verification is failed',
    },
    {
        url: 'https://campaigns.hellotars.com/send_single_whatsapp_notification/campaign_id/whatsapp__onD1',
        token: 'tars_461b0399dab7aec84339ae149785bc05f5154965e35be680cdabe91c8bab57b8',
        template_name: 'v2_interview_schedule',
        note: 'When the Interview is scheduled ',
        parameters: {
            name: 'Name ',
            Date: 'Date ',
            Time: 'Time ',
            map: 'Map Location ',
        },
    },
    {
        url: 'https://campaigns.hellotars.com/send_single_whatsapp_notification/campaign_id/whatsapp__JwpH',
        token: 'tars_bcfcdcc739ca80e00d805a87ada64b88775886289a967fc98323b91719ee08f7',
        template_name: 'v2_interview_completed',
        note: 'When The Interview is completed ',
    },
    {
        url: 'https://campaigns.hellotars.com/send_single_whatsapp_notification/campaign_id/whatsapp__33C6',
        token: 'tars_90bea6abc0f2de987e391199b9dee9635592de6dc4bda4a47d23b081870e7f20',
        template_name: 'v2_interview_cleared',
        note: 'When the Interview is cleared    ',
    },
    {
        url: 'https://campaigns.hellotars.com/send_single_whatsapp_notification/campaign_id/whatsapp__rFvv',
        token: 'tars_48df5a16f0c3cc53d7d8f562782ece46eb9a8a031c907e273710a6e6891145c4',
        template_name: 'v2_successfully_onboarded',
        note: 'When the user is onboarded ',
    },
    {
        url: 'https://campaigns.hellotars.com/send_single_whatsapp_notification/campaign_id/whatsapp__vJ9u',
        token: 'tars_bee6468529de38addb2090a94152ac70fd29750596ab9d7fb9bfefe62fb89328',
        template_name: 'sp_team_member',
        note: 'When SP invites a team member ',
        parameters: {
            name: 'Team Member Name ',
            company_name: 'Company Name ',
            app_link: 'App Link ',
        },
    },
    {
        url: 'https://campaigns.hellotars.com/send_single_whatsapp_notification/campaign_id/whatsapp__a5ve',
        token: 'tars_10edc498b3aa67aafd9d305ada825ea684f427e9481cee3c70283ed0195ba440',
        template_name: 'onboarded_feedback_link',
        note: 'When the user is onboarded ',
        parameters: {
            name: 'User Name',
            link: 'Form Link',
        },
    },

    {
        url: 'https://campaigns.hellotars.com/send_single_whatsapp_notification/campaign_id/whatsapp__DnuY',
        token: 'tars_0d3abad3e065af2acbd97d7e2ad95bec2572b2f5621520a6bc0a27ad899ef45c',
        template_name: 'referral_invite_link',
        note: 'When a user refers a friend',
        parameters: {
            name: 'User Name',
            link: 'App link',
            referrer_name: 'Refferrer Name ',
        },
    },
]

export function getTemplateDataByName(name: string) {
    return templates.find((template) => template.template_name === name)
}
