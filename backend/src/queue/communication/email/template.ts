import fs from 'fs/promises'
import path from 'path'
import { EmailTemplate } from '../types'

export const readTemplate = async (temp_file_name: string) => {
    try {
        const file_content = await fs.readFile(
            path.join(__dirname, temp_file_name),
            'utf-8'
        )
        return file_content
    } catch (error) {
        console.log('Failed to read email template:', error)
        throw error
    }
}

export default async () => {
    const templates: Record<string, EmailTemplate> = {
        SIGNUP_TEMPLATE: {
            template: await readTemplate('signup.template.html'),
            variables: {
                name: '',
            },
        },
        FOLLOW_UP_COMPLETE_YOUR_PROFILE_24H: {
            template: await readTemplate(
                'follow-up-complete-your-profile-24h.template.html'
            ),
            variables: {
                name: '',
            },
        },
        FOLLOW_UP_COMPLETE_YOUR_PROFILE_48H: {
            template: await readTemplate(
                'follow-up-complete-your-profile-48h.template.html'
            ),
            variables: {
                name: '',
            },
        },
        FOLLOW_UP_COMPLETE_YOUR_PROFILE_72H: {
            template: await readTemplate(
                'follow-up-complete-your-profile-72h.template.html'
            ),
            variables: {
                name: '',
            },
        },
        DOCUMENT_SUBMITTED: {
            template: await readTemplate('document-submitted.template.html'),
            variables: {
                name: '',
            },
        },
        DOCUMENT_VERIFICATION_COMPLETED: {
            template: await readTemplate(
                'document-verification-completed.template.html'
            ),
        },
        DOCUMENT_VERIFICATION_FAILED: {
            template: await readTemplate(
                'document-verification-failed.template.html'
            ),
        },
        INTERVIEW_SCHEDULED: {
            template: await readTemplate('interview-scheduled.template.html'),
            variables: {
                name: '',
                date: '',
                time: '',
            },
        },
        INTERVIEW_SCHEDULED_REMINDER_24H_BEFORE: {
            template: await readTemplate(
                'interview-scheduled-reminder-24h-before.template.html'
            ),
            variables: {
                name: '',
            },
        },
        INTERVIEW_COMPLETED: {
            template: await readTemplate('interview-completed.template.html'),
            variables: {
                name: '',
            },
        },
        INTERVIEW_CLEARED: {
            template: await readTemplate('interview-cleared.template.html'),
            variables: {
                name: '',
            },
        },
        SUCCESSFULLY_ONBOARDED: {
            template: await readTemplate(
                'successfully-onboarded.template.html'
            ),
            variables: {
                name: '',
            },
        },
        DELETE_DATA_REQUEST: {
            template: await readTemplate('data-deletion.html'),
            variables: {
                name: '',
                email_phone: '',
                reason: '',
            },
        },
        EXPORT_ASSIGNMENT_RESULT: {
            template: await readTemplate('assignment-result-export.html'),
            variables: {
                date_range: '',
                admin_name: '',
                text: '',
                export_link: '',
            },
        },
        EXPORT_TRAINING_RESULT: {
            template: await readTemplate('training-result-export.html'),
            variables: {
                admin_name: '',
                export_link: '',
            },
        },
        EXPORT_CONTACTS_RESULT: {
            template: await readTemplate('contact-export.html'),
            variables: {
                admin_name: '',
                export_link: '',
            },
        },
        EXPORT_POLICY_TRACKING_RESULT: {
            template: await readTemplate('policy-tracking-export.html'),
            variables: {
                admin_name: '',
                export_link: '',
            },
        },
        EXPORT_SP_DATA: {
            template: await readTemplate('service-provider-export.html'),
            variables: {
                admin_name: '',
                text: '',
                export_link: '',
            },
        },
        EXPORT_USER_TTT_RESULT: {
            template: await readTemplate('user-ttt-result-export.html'),
            variables: {
                admin_name: '',
                export_link: '',
            },
        },
    }
    return templates
}
