import dayjs from 'dayjs'
import Queues from '.'
import DataProvider from '../data'
import { QueryExportUserForTttArgs } from '../graphql/__generated__/types'
import { getUserIdsSortedByTTTDate } from '../services/transfer.to.tms.service'
import { CryptoUtility } from '../utils/crypto'
import { downloadXLSXFromJson } from '../utils/xlsxDataDownloader'

const db = DataProvider.getDbInstance()

export function startExportTTTDataConsumer() {
    Queues.exportUserTTTDataQueue.process(async (job, done) => {
        try {
            const { args, ctx } = job.data as {
                args: QueryExportUserForTttArgs
                ctx: any
            }

            const users = await db.$transaction(async (tx) => {
                const userIds = await getUserIdsSortedByTTTDate(tx, {
                    ...args,
                    pagination: { take: 10000, skip: 0 },
                })
                const user = await tx.user.findMany({
                    where: {
                        id: {
                            in: userIds,
                        },
                    },
                    include: {
                        user_onboarding_data: true,
                        bank_details: {
                            select: {
                                encrypted: true,
                            },
                        },
                        designation: {
                            select: {
                                id: true,
                                name: true,
                                level: true,
                            },
                        },
                        location: true,
                    },
                })

                return user
            })

            users.forEach((data: any) => {
                if (
                    data.bank_details &&
                    data.bank_details.length > 0 &&
                    data.bank_details[0]?.encrypted
                ) {
                    try {
                        const bank_data = JSON.parse(
                            CryptoUtility.decrypt(
                                data.bank_details[0].encrypted
                            )
                        )
                        // Replace the array with the decrypted bank data object
                        data.bank_details = bank_data
                    } catch (error) {
                        console.error(
                            'Error decrypting bank details for user:',
                            data.id,
                            error
                        )
                        data.bank_details = null
                    }
                } else {
                    data.bank_details = null
                }
            })

            const userDataToSend = users.map((user: any) => {
                return {
                    Name: user?.name,
                    Phone: user?.phone,
                    Email: user?.email,
                    'Hiring Criteria':
                        user?.user_onboarding_data?.[0]?.hiring_criteria,
                    Designation: user?.designation,
                    'Salary Offered':
                        user?.user_onboarding_data?.[0]?.salary_offered,
                    Onboarded: user?.transfer_to_tms_status ? 'Yes' : 'No',
                    'Onboarding Date': (() => {
                        const meta = user?.user_onboarding_data?.[0]?.meta
                        if (
                            meta &&
                            typeof meta === 'object' &&
                            'transfer_to_tms_date' in meta
                        ) {
                            // meta is already an object with the property
                            return dayjs(
                                (meta as any).transfer_to_tms_date
                            ).format('DD-MM-YYYY')
                        }
                        return ''
                    })(),
                    'Assessed By': user?.user_onboarding_data?.[0]?.assessed_by,
                    'Date of Joining': dayjs(
                        user?.user_onboarding_data?.[0]?.date_of_joining
                    ).format('DD-MM-YYYY'),
                    'Other Remark': user?.user_onboarding_data?.[0]?.remark,
                    'Address As Per Aadhaar':
                        user?.user_onboarding_data?.[0]?.aadhar_address,
                    'Employee id': user?.user_onboarding_data?.[0]?.employee_id,
                    'Bank Name': user?.bank_details?.bank_name || '',
                    'Bank Account Number':
                        user?.bank_details?.account_number || '',
                    'IFSC Code': user?.bank_details?.ifsc || '',
                }
            })

            //sheets to be sent to xlsx
            const sheets = [
                {
                    data: userDataToSend,
                    sheetName: 'User Onboarded Report',
                },
            ]

            const fileName = `User_Onboarded_Report_${dayjs()
                .format('MMMM D, YYYY')
                .replace(/,/g, '')
                .replace(/\s/g, '_')}.xlsx`

            const s3Url = await downloadXLSXFromJson(sheets, fileName)

            //Adding in Email Queue
            await Queues.emailQueue.add({
                receiver: ctx?.admin?.email || '',
                subject: `User Onboarded Report`,
                template_name: 'EXPORT_USER_TTT_RESULT',
                variables: {
                    admin_name: ctx?.admin?.name,
                    export_link: s3Url || '',
                },
                user_id: ctx?.admin?.id,
                org_id: ctx?.admin?.organization_id,
            })

            done()
        } catch (error) {
            console.error(
                'Error Processing Export User TTT Data from EXPORT USER TTT DATA Queue',
                error
            )
        }
    })
}
