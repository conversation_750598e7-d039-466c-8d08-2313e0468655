import { Prisma } from '@prisma/client'
import { QueryGetUserForTttArgs } from '../graphql/__generated__/types'

export const getUserIdsSortedByTTTDate = async (
    tx: Prisma.TransactionClient,
    args: QueryGetUserForTttArgs
): Promise<number[]> => {
    const conditions: string[] = []
    const params: any[] = []

    // Search filter
    if (args?.search) {
        conditions.push(
            `(u.name ILIKE $${params.length + 1} OR u.email ILIKE $${
                params.length + 1
            } OR u.phone ILIKE $${params.length + 1})`
        )
        params.push(`%${args.search}%`)
    }

    // Transfer to TMS filter
    if (args?.filter?.transfer_to_tms_status != undefined) {
        conditions.push(`u.transfer_to_tms_status = $${params.length + 1}`)
        params.push(args.filter.transfer_to_tms_status)
    }

    // Date range filter
    if (args?.filter?.startDate && args?.filter?.endDate) {
        conditions.push(`EXISTS (
      SELECT 1 FROM user_onboarding_data ud
      WHERE ud.user_id = u.id
      AND DATE((ud.meta ->> 'transfer_to_tms_date')::timestamp) BETWEEN DATE($${
          params.length + 1
      }::timestamp) AND DATE($${params.length + 2}::timestamp)
    )`)
        params.push(args?.filter?.startDate, args?.filter?.endDate)
    }
    const whereClause =
        conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : ''

    // Dynamic ORDER BY logic
    const orderByClause =
        args?.filter?.transfer_to_tms_status == true
            ? `ORDER BY (
                        SELECT (ud.meta ->> 'transfer_to_tms_date')::timestamp
                        FROM "user_onboarding_data" ud
                        WHERE ud.user_id = u.id
                        ORDER BY (ud.meta ->> 'transfer_to_tms_date')::timestamp DESC
                        LIMIT 1
                        ) ASC`
            : `ORDER BY u.created_at DESC`

    const query = `
    SELECT u.*
    FROM "user" u
    ${whereClause}
    ${orderByClause}
    LIMIT ${args.pagination?.take ?? 10}
        OFFSET ${(args.pagination?.take ?? 10) * (args.pagination?.skip ?? 0)};
    `
    const userIdRows: any[] = await tx.$queryRawUnsafe(query, ...params)

    const userIds = userIdRows.map((row) => row.id)
    return userIds
}
