import { <PERSON>risma, TeamMemberInvitationStatus } from '@prisma/client'

import { GraphQLError } from 'graphql'
import isEmpty from 'is-empty'
import DataProvider from '../data'
import { SendOtpType, UserRole } from '../graphql/__generated__/types'
import Queues from '../queue'
import { MASTER_ORG_ID } from '../utils/constants'
import { IS_PROD, tokenExpiryData } from '../utils/envVars'
import { EventList, EventType } from '../utils/eventTypes'
import { generateJwt } from '../utils/jwt'
import { TmsHelper } from '../utils/tms.user'
import { Context } from '../utils/types'
import {
    awardReferralPoints,
    createUserReferral,
    updateUserOnboardingTimestamp,
    validateReferralCode,
} from '../utils/user'
import {
    convertToSeconds,
    userLocationAsPerOnboardingStage,
} from '../utils/utils'
import { validateUserSignup } from '../utils/validators/userAuth'
import { verifyOTP } from '../utils/verifyOtp'
import { logHistory } from './history.service'

const db = DataProvider.getDbInstance()
const redis = DataProvider.getRedisInstance()

export const signUp = async (data: {
    phone: string
    otp: string
    meta?: any
    referral_code?: string
    ctx: Context
}) => {
    try {
        const { phone, otp, meta, referral_code, ctx } = data || {}

        if (referral_code) {
            await validateReferralCode(referral_code)
        }
        const otp_verify_result = await verifyOTP(
            'phone_verify_' + phone,
            otp,
            5
        )

        if (!otp_verify_result.result) {
            throw new GraphQLError(otp_verify_result.message)
        }

        const validation = validateUserSignup(data)
        if (validation.error) {
            throw new GraphQLError(
                validation.error.details
                    .map((detail) => detail.message)
                    .join('\n')
            )
        } else {
            //call the function here if true then update the meta and assign a type
            const hasInvitation = await checkIfUserHasInvitation(phone)
            const userCreate: Prisma.userCreateInput = {
                phone,
                updated_at: new Date(),
                onboarding_stage: 'SIGN_UP', //this needs to be changed
                organization: { connect: { id: MASTER_ORG_ID } }, //are we gonna have this mast org id still ?🔴
                meta: {
                    ...meta,
                    app_sign_up_date: new Date().toISOString(),
                    last_location: '/register-yourself',
                },
            }
            if (hasInvitation !== false) {
                userCreate.user_type = UserRole.ServiceProvider
                userCreate.organization = {
                    connect: { id: hasInvitation.organization_id }, //this is to assign user to the org of sp
                }
                userCreate.meta = {
                    ...meta,
                    is_invited: true,
                    last_location:
                        '/sp/registration-journey/tm-contact-details', //TODO: change it with the actual route
                }
                userCreate.name = hasInvitation.name
            }
            const new_user = await db.user.create({
                data: userCreate,
            })
            if (hasInvitation !== false) {
                await db.team_member_invitation.update({
                    where: {
                        id: hasInvitation.invitation_id,
                    },
                    data: {
                        invited_user_id: new_user.id,
                        status: TeamMemberInvitationStatus.PENDING,
                    },
                })
            }
            await TmsHelper.processUserForTmsLead(new_user.id, true)
            logHistory({
                entity_id: new_user.id?.toString(),
                ...EventList[EventType.USER_SIGNED_UP],
                creator_type: 'USER',
                new_values: data,
                user_id: new_user.id,
                org_id: new_user.organization_id,
                creator_id: new_user.id,
                meta: {
                    ip_addr: ctx?.ipAddr || '',
                    user_agent: ctx?.userAgent || '',
                    user_id: new_user.id,
                    user_name: new_user.name,
                    user_org_id: new_user.organization_id,
                    creator_id: new_user.id,
                    creator_name: new_user.name,
                    creator_org_id: new_user.organization_id,
                },
            })
            //Handle Referrals+
            if (referral_code) {
                await createUserReferral(new_user.id, referral_code)
                await awardReferralPoints(new_user)
            }
            //Handle UserOnboardingTimestamp
            await updateUserOnboardingTimestamp(new_user)

            const token = generateJwt(
                {
                    name: new_user?.name || '', //TODO: on signup the name is not set in the token cuz of this user name is not visible
                    phone: new_user.phone,
                    issuer: 'rojgaar-backend',
                    user_type: 'user',
                },
                tokenExpiryData.USER.ANDROID
            )
            const key = `user_${new_user.phone}`

            if (new_user?.phone) {
                await (
                    await redis
                ).setEx(
                    key,
                    convertToSeconds(tokenExpiryData.USER.ANDROID),
                    JSON.stringify(new_user)
                )
            }

            if (IS_PROD && new_user.phone) {
                if (new_user?.email) {
                    Queues.emailQueue.add({
                        receiver: new_user.email || '',
                        subject:
                            'Welcome to Wify - Complete Your Profile to Begin!',
                        template_name: 'SIGNUP_TEMPLATE',
                        variables: {
                            name: 'User',
                        },
                        user_id: new_user.id,
                        org_id: new_user.organization_id,
                    })
                }
            }

            return {
                result: true,
                token,
                message: 'Signed up successfully',
                user_type: '', //new user will have an empty user type
                registration_type: SendOtpType.SignUp,
                meta: new_user.meta as any,
                is_invited: hasInvitation !== false ? true : false,
            }
        }
    } catch (error) {
        throw new GraphQLError(error)
    }
}

export const signIn = async (
    phone: string,
    otp: string,
    user_type: string,
    meta: any,
    ctx?: Context
) => {
    try {
        //DOUBT: suppose a user is create by admin SP/Technician and invitation exist against the same user then what ?
        if (!phone && !otp) {
            throw new GraphQLError('Please provide phone and otp to login')
        }

        const verify_result = await verifyOTP('phone_verify_' + phone, otp, 5)

        if (!verify_result.result) {
            throw new GraphQLError(verify_result.message)
        }

        let user = await db.user.findFirst({
            where: {
                phone,
            },
        })

        if (!user) {
            throw new GraphQLError('Something  went wrong')
        }

        const parsedMeta =
            typeof user?.meta === 'string'
                ? JSON.parse(user?.meta || '{}')
                : user?.meta

        const last_location = parsedMeta?.last_location

        if (isEmpty(last_location)) {
            const user_type = user?.user_type
            const user_meta_data = user?.meta || ({} as any)
            if (user_type === 'TECHNICIAN') {
                const last_location = await userLocationAsPerOnboardingStage(
                    user.onboarding_stage
                )

                const updatedUser = await db.user.update({
                    where: {
                        id: user.id,
                    },
                    data: {
                        meta: {
                            ...user_meta_data,
                            ...meta,
                            last_location,
                        },
                    },
                })
                user = updatedUser
            }
            if (user_type === UserRole.ServiceProvider) {
                const last_location =
                    '/sp/registration-journey/journey-completed'
                const updatedUser = await db.user.update({
                    where: {
                        id: user.id,
                    },
                    data: {
                        meta: {
                            ...user_meta_data,
                            ...meta,
                            last_location,
                        },
                    },
                })
                user = updatedUser
            }
        }

        //First gets latest meta after location updates
        const user_updated_meta = user?.meta || ({} as any)
        const parsedUpdatedMeta =
            typeof user_updated_meta === 'string'
                ? JSON.parse(user_updated_meta || '{}')
                : user_updated_meta

        const has_agreed_to_privacy_policy =
            parsedUpdatedMeta?.has_agreed_to_privacy_policy

        const app_sign_up_date = parsedUpdatedMeta?.app_sign_up_date

        if (!app_sign_up_date || isEmpty(has_agreed_to_privacy_policy)) {
            user = await db.user.update({
                where: { id: user.id },
                data: {
                    meta: {
                        ...user_updated_meta,
                        ...meta,
                        app_sign_up_date:
                            app_sign_up_date || new Date().toISOString(),
                    },
                },
            })
        }

        const token = generateJwt(
            {
                phone: user.phone,
                name: user.name,
                issuer: 'rojgaar-backend',
                user_type: 'user',
                user_id: user.id,
            },
            tokenExpiryData.USER.ANDROID
        )

        const key = `user_${user.phone}`

        if (user?.phone) {
            await (
                await redis
            ).setEx(
                key,
                convertToSeconds(tokenExpiryData.USER.ANDROID),
                JSON.stringify(user)
            )
        }
        logHistory({
            active: false,
            entity_id: user.id?.toString(),
            ...EventList[EventType.USER_SIGNED_IN],
            creator_type: 'USER',
            new_values: { phone, user_type },
            user_id: user.id,
            org_id: user.organization_id,
            creator_id: user.id,
            meta: {
                ip_addr: ctx?.ipAddr || '',
                user_agent: ctx?.userAgent || '',
                user_id: user.id,
                user_name: user.name,
                user_org_id: user.organization_id,
                creator_id: user.id,
                creator_name: user.name,
                creator_org_id: user.organization_id,
            },
        })

        return {
            result: true,
            token,
            message: 'Signed In successfully',
            user_type,
            registration_type: SendOtpType.Login,
            meta: user.meta,
            is_invited: user_updated_meta?.is_invited ? true : false,
        }
    } catch (error) {
        throw new GraphQLError(error)
    }
}

const checkIfUserHasInvitation = async (
    phone: string
): Promise<
    | {
          organization_id: number
          invitation_id: string
          name: string
      }
    | false
> => {
    const invitation = await db.team_member_invitation.findFirst({
        where: {
            contact: {
                phone: phone,
            },
            status: TeamMemberInvitationStatus.INVITED,
        },
        select: {
            id: true,
            contact: {
                select: {
                    name: true,
                },
            },
            inviter: {
                select: {
                    organization_id: true,
                },
            },
        },
    })
    if (invitation) {
        return {
            organization_id: invitation.inviter.organization_id,
            invitation_id: invitation.id,
            name: invitation.contact?.name || '',
        }
    }
    return false
}
