import { Prisma } from '@prisma/client'
import { GraphQLError } from 'graphql'
import DataProvider from '../../data'
import { validateAdmin } from '../../utils/authValidators'
import { MASTER_ORG_ID } from '../../utils/constants'
import { Context } from '../../utils/types'

const db = DataProvider.getDbInstance()
const redis = DataProvider.getRedisInstance()

interface RolePermission {
    feature_id: number
    permission_for_user?: Prisma.InputJsonValue //{create:true,update:true,read:true,delete:true}
    permission_for_sp?: Prisma.InputJsonValue
}
interface CreateAdminRoleArgsType {
    data: {
        name: string
        description?: string
        has_admin_privileges?: boolean
        can_reject?: boolean
        can_ban?: boolean
        can_accept?: boolean
        can_download: boolean
        creator_id?: number
        role_permission_array?: RolePermission[]
    }
}

const createAdminRole = async (
    _: any,
    args: CreateAdminRoleArgsType,
    ctx: Context
) => {
    try {
        const admin = validateAdmin(ctx)

        const existingRoleName = await db.admin_role.findFirst({
            where: {
                name: args?.data?.name,
            },
            select: {
                admin_role_id: true,
            },
        })
        if (existingRoleName) {
            throw new GraphQLError('A role with the same name exists')
        }
        //create admin role
        const role = await db.admin_role.create({
            data: {
                name: args.data.name,
                description: args.data.description,
                has_admin_privileges: args.data.has_admin_privileges,
                can_reject: args.data.can_reject,
                can_ban: args.data.can_ban,
                can_accept: args.data.can_accept,
                can_download: args.data.can_download,
                creator_id: admin?.id || MASTER_ORG_ID,
                created_at: new Date().toISOString(),
            },
        })

        //create admin_role_permission
        if (args.data.role_permission_array?.length) {
            const featureIds = args.data.role_permission_array.map(
                (item) => item.feature_id
            )

            const features = await db.admin_protected_feature.findMany({
                where: {
                    feature_id: { in: featureIds },
                    is_active: true,
                },
                select: { feature_id: true },
            })

            const validFeatureIds = new Set(features.map((f) => f.feature_id))

            const rolePermissionsData = args.data.role_permission_array.map(
                (item) => {
                    if (!validFeatureIds.has(item.feature_id)) {
                        throw new GraphQLError(
                            `Feature with id ${item.feature_id} not found or inactive`
                        )
                    }

                    return {
                        admin_role_id: role.admin_role_id,
                        feature_id: item.feature_id,
                        permission_for_user: item.permission_for_user,
                        permission_for_sp: item.permission_for_sp,
                    }
                }
            )

            await db.admin_role_permission.createMany({
                data: rolePermissionsData,
            })
        }

        return {
            result: true,
            message: 'Role created successfully',
        }
    } catch (error) {
        throw new GraphQLError(error)
    }
}

interface ExistingRolePermission {
    id: number
    feature_id: number
    permission_for_user?: Prisma.InputJsonValue
    permission_for_sp?: Prisma.InputJsonValue
}
interface UpdateAdminRoleArgsType {
    data: {
        admin_role_id: number
        name?: string
        description?: string
        has_admin_privileges?: boolean
        can_reject?: boolean
        can_ban?: boolean
        can_accept?: boolean
        can_download?: boolean
        creator_id?: number
        admin_role_permission?: ExistingRolePermission[]
    }
}

const updateAdminRole = async (
    _: any,
    args: UpdateAdminRoleArgsType,
    ctx: Context
) => {
    try {
        const admin = validateAdmin(ctx)
        const cacheKey = `admin_permissions:${admin.admin_role_id}`

        const existingRole = await db.admin_role.findFirst({
            where: {
                admin_role_id: args.data.admin_role_id,
            },
            select: {
                name: true,
                admin_role_id: true,
            },
        })
        if (!existingRole) {
            throw new GraphQLError('Role does not exist')
        }
        if (args?.data?.name !== existingRole.name) {
            const existingRoleName = await db.admin_role.findFirst({
                where: {
                    name: args?.data?.name,
                },
                select: {
                    admin_role_id: true,
                },
            })
            if (existingRoleName) {
                throw new GraphQLError('A role with the same name exists')
            }
        }
        //Check before assigning an existing root admin a different role , if at least one root admin exists
        if (
            +existingRole.admin_role_id === 1 &&
            +args.data.admin_role_id !== 1
        ) {
            const rootAdminCount = await db.admin.count({
                where: {
                    admin_role_id: 1,
                },
            })
            if (rootAdminCount === 1) {
                throw new GraphQLError(
                    'This operation is not allowed: No Other Root Admin Exists.'
                )
            }
        }

        //update role details
        const { admin_role_permission, ...adminRoleData } = args.data
        await db.admin_role.update({
            where: {
                admin_role_id: args.data.admin_role_id,
            },
            data: {
                name: adminRoleData.name,
                description: adminRoleData.description,
                has_admin_privileges: adminRoleData.has_admin_privileges,
                can_reject: adminRoleData.can_reject,
                can_ban: adminRoleData.can_ban,
                can_accept: adminRoleData.can_accept,
                can_download: adminRoleData.can_download,
                updated_by: admin.id || MASTER_ORG_ID,
                updated_at: new Date().toISOString(),
            },
        })
        //delete all records with admin role id args.data.admin role id ,
        await db.admin_role_permission.deleteMany({
            where: {
                admin_role_id: args.data.admin_role_id,
            },
        })
        //update role permission details
        if (admin_role_permission?.length) {
            await db.admin_role_permission.createMany({
                data: admin_role_permission
                    .filter((role) => {
                        const isEmptyPermission = (perm: any) =>
                            !perm ||
                            Object.values(perm).every((val) => val === false)

                        // If both permission objects are fully false or null/undefined, skip
                        if (
                            isEmptyPermission(role.permission_for_sp) &&
                            isEmptyPermission(role.permission_for_user)
                        ) {
                            return false
                        }
                        return true
                    })
                    // eslint-disable-next-line @typescript-eslint/no-unused-vars
                    .map(({ id, ...rest }) => ({
                        admin_role_id: args.data.admin_role_id,
                        ...rest,
                    })),
                skipDuplicates: true,
            })
        }

        //delete cached role permissions for this role
        await (await redis).del(cacheKey)

        return {
            result: true,
            message: 'Role updated successfully',
        }
    } catch (error) {
        throw new GraphQLError(error)
    }
}

const deleteAdminRole = async (
    _: any,
    args: { admin_role_id: number },
    ctx: Context
) => {
    try {
        validateAdmin(ctx)
        //?:can i delete a role if it is assigned to an admin user

        const existingRole = await db.admin_role.findFirst({
            where: {
                admin_role_id: args.admin_role_id,
            },
        })
        if (!existingRole) {
            throw new GraphQLError('Role does not exist')
        }
        const existingAdmin = await db.admin.findFirst({
            where: {
                admin_role_id: args.admin_role_id,
            },
            select: {
                id: true,
            },
        })
        if (existingAdmin) {
            throw new GraphQLError(
                'Cannot delete a role linked to an admin user'
            )
        }

        await db.admin_role_permission.deleteMany({
            where: {
                admin_role_id: args.admin_role_id,
            },
        })
        await db.admin_role.delete({
            where: {
                admin_role_id: args.admin_role_id,
            },
        })

        return {
            result: true,
            message: 'Role deleted successfully',
        }
    } catch (error) {
        throw new GraphQLError(error)
    }
}

const assignRoleToAdmin = async (
    _: any,
    args: { admin_id: number; admin_role_id: number },
    ctx: Context
) => {
    try {
        //NOTE: ROLE ID 1 IS ROOT ADMIN ALWAYS
        validateAdmin(ctx)
        const existingRole = await db.admin_role.findFirst({
            where: {
                admin_role_id: args.admin_role_id,
            },
        })
        if (!existingRole) {
            throw new GraphQLError('Role does not exist')
        }
        const existingAdmin = await db.admin.findFirst({
            where: {
                id: args.admin_id,
            },
        })
        if (!existingAdmin) {
            throw new GraphQLError('Admin does not exist')
        }
        await db.admin.update({
            where: {
                id: args.admin_id,
            },
            data: {
                admin_role_id: args.admin_role_id,
            },
        })

        return {
            result: true,
            message: 'Role assigned successfully',
        }
    } catch (error) {
        throw new GraphQLError(error)
    }
}
const getAllAdminRoles = async (_: any, args: null, ctx: Context) => {
    try {
        validateAdmin(ctx)

        const adminRoleData = await db.admin_role.findMany({
            include: {
                admin_role_permission: {
                    include: {
                        admin_protected_feature: true,
                    },
                },
            },
            orderBy: {
                admin_role_id: 'asc',
            },
        })
        if (!adminRoleData.length) {
            throw new GraphQLError('No roles found')
        }
        return {
            result: true,
            message: 'Role fetched successfully',
            data: adminRoleData,
        }
    } catch (error) {
        throw new GraphQLError(error)
    }
}
const getAdminRoleByRoleId = async (
    _: any,
    args: {
        admin_role_id: number
    },
    ctx: Context
) => {
    try {
        validateAdmin(ctx)

        const adminRoleData = await db.admin_role.findUnique({
            where: {
                admin_role_id: args.admin_role_id,
            },
            include: {
                admin_role_permission: {
                    include: {
                        admin_protected_feature: true,
                    },
                },
            },
        })
        if (!adminRoleData) {
            throw new GraphQLError('No role found with the given Role Id ')
        }
        return {
            result: true,
            message: 'Role fetched successfully',
            data: adminRoleData,
        }
    } catch (error) {
        throw new GraphQLError(error)
    }
}

type AdminModuleWithSubmodules = AdminModule & {
    submodules?: AdminProtectedFeature[]
}

type AdminModule = {
    module_id: number
    module_name: string
    is_published: boolean
    is_active: boolean
    [key: string]: any
}

type AdminProtectedFeature = {
    feature_id: number
    is_module: boolean
    parent_module_id: number | null
    module_id: number | null
    parent_module_name: string | null
    module_name: string | null
    sub_module_name: string | null
    updated_at: Date | null
    [key: string]: any
}
const getUnpublishedModules = async (_: any, args: null, ctx: Context) => {
    try {
        validateAdmin(ctx)

        const unpublishedModules: AdminModuleWithSubmodules[] =
            await db.admin_module.findMany({
                where: {
                    is_published: false,
                    is_active: true,
                },
            })
        const unPublishedSubmodules = await db.admin_protected_feature.findMany(
            {
                where: {
                    module_id: {
                        in: unpublishedModules.map(
                            (module) => module.module_id
                        ),
                    },
                    is_module: false,
                    sub_module_name: {
                        not: null,
                    },
                },
            }
        )
        unpublishedModules.forEach((module) => {
            module.submodules = unPublishedSubmodules.filter(
                (submodule) => submodule.module_id === module.module_id
            )
        })

        return {
            result: true,
            message: 'Unpublished modules fetched successfully',
            data: unpublishedModules ?? [],
        }
    } catch (error) {
        throw new GraphQLError(error)
    }
}

const getUnpublishedModuleById = async (
    _: any,
    args: { module_id: number },
    ctx: Context
) => {
    try {
        validateAdmin(ctx)

        const unpublishedModule = await db.admin_module.findFirst({
            where: {
                module_id: args.module_id,
                is_published: false,
                is_active: true,
            },
        })

        const submodules = await db.admin_protected_feature.findMany({
            where: {
                module_id: unpublishedModule?.module_id,
                sub_module_name: {
                    not: null,
                },
            },
        })

        return {
            result: true,
            message: 'Unpublished module fetched successfully',
            data: unpublishedModule
                ? { ...unpublishedModule, submodules }
                : null,
        }
    } catch (error) {
        throw new GraphQLError(error)
    }
}

interface RolePermissionData {
    admin_role_id: number
    feature_id?: number
    is_module?: boolean
    permission_for_user?: Prisma.InputJsonValue
    permission_for_sp?: Prisma.InputJsonValue
}
const publishModule = async (
    _: any,
    args: { module_id: number; role_permission: RolePermissionData[] },
    ctx: Context
) => {
    try {
        const admin = validateAdmin(ctx)
        const module = await db.admin_module.findFirst({
            where: {
                module_id: args.module_id,
                is_active: true,
            },
        })
        const moduleFeatureDetails = await db.admin_protected_feature.findFirst(
            {
                where: {
                    module_id: module?.module_id,
                    is_module: true,
                },
                select: {
                    feature_id: true,
                    module_id: true,
                },
            }
        )
        if (!module) {
            throw new GraphQLError('Module does not exist')
        }
        if (module.is_published) {
            throw new GraphQLError('Module already published')
        }
        //publish the module
        await db.admin_module.update({
            where: {
                module_id: args.module_id,
            },
            data: {
                is_published: true,
                published_at: new Date().toISOString(),
                published_by: admin.id,
            },
        })
        //allot permissions to selected roles
        await db.admin_role_permission.createMany({
            data: args.role_permission.map((rolePermission) => {
                if (
                    !rolePermission.feature_id &&
                    module?.module_id === moduleFeatureDetails?.module_id
                ) {
                    return {
                        ...rolePermission,
                        feature_id: moduleFeatureDetails?.feature_id ?? 0,
                    }
                }
                return {
                    ...rolePermission,
                    feature_id: rolePermission.feature_id ?? 0,
                }
            }),
        })

        return {
            result: true,
            message: 'Module published successfully',
        }
    } catch (error) {
        throw new GraphQLError(error)
    }
}

interface adminModule {
    module_name: string
    is_published: boolean
    icon_name: string
    visible_in_sp: boolean
    visible_in_users: boolean
}
interface adminProtectedFeature {
    module_name: string
    is_module: boolean
    sub_module_name: string
    sub_module_url_user: string
    sub_module_url_sp: string
}
const createNewModule = async (
    _: any,
    args: { module: adminModule; features: adminProtectedFeature[] },
    ctx: Context
) => {
    try {
        const admin = validateAdmin(ctx)

        const existingModule = await db.admin_module.findFirst({
            where: {
                module_name: args.module.module_name,
            },
        })
        if (existingModule) {
            throw new GraphQLError('Module already exists')
        }
        if (args.features.length < 1) {
            throw new GraphQLError('At least one feature is required')
        }

        if (!args.features.some((feature) => feature.is_module)) {
            throw new GraphQLError(
                'One feature page should be an entry point to module'
            )
        }
        if (args.features.filter((feature) => feature.is_module).length > 1) {
            throw new GraphQLError(
                'Only one feature page should be an entry point to module'
            )
        }

        //create module
        const newModule = await db.admin_module.create({
            data: {
                parent_module_id: 1,
                parent_module_name: 'Dashboard',
                module_name: args.module.module_name,
                is_published: false,
                is_active: true,
                icon_name: args.module.icon_name,
                visible_in_sp: args.module.visible_in_sp,
                visible_in_users: args.module.visible_in_users,
                created_by: admin.id,
                created_at: new Date().toISOString(),
            },
        })

        //create feature pages
        await db.admin_protected_feature.createMany({
            data: args.features.map((feature) => ({
                parent_module_id: 1,
                parent_module_name: 'Dashboard', //default according to App Routes setup in frontend
                module_id: newModule.module_id,
                module_name: newModule.module_name,
                is_module: feature.is_module,
                sub_module_name: feature.sub_module_name,
                sub_module_url_user: feature.sub_module_url_user,
                sub_module_url_sp: feature.sub_module_url_sp,
                is_active: true,
                created_by: admin.id,
                created_at: new Date().toISOString(),
            })),
        })

        return {
            result: true,
            message: 'Module created successfully',
        }
    } catch (error) {
        throw new GraphQLError(error)
    }
}

const createNewPage = async (
    _: any,
    args: { page: adminProtectedFeature[]; module_id: number },
    ctx: Context
) => {
    try {
        const admin = validateAdmin(ctx)

        if (args.page.length < 1) {
            throw new GraphQLError('At least one feature is required')
        }

        const moduleData = await db.admin_module.findUnique({
            where: {
                module_id: args.module_id,
            },
            select: {
                module_id: true,
                module_name: true,
            },
        })
        if (!moduleData) {
            throw new GraphQLError('Module does not exist')
        }
        //create feature pages
        await db.admin_protected_feature.createMany({
            data: args.page.map((feature) => ({
                parent_module_id: 1,
                parent_module_name: 'Dashboard', //default according to App Routes setup in frontend
                module_id: moduleData?.module_id,
                module_name: moduleData?.module_name,
                is_module: feature.is_module,
                sub_module_name: feature.sub_module_name,
                sub_module_url_user: feature.sub_module_url_user,
                sub_module_url_sp: feature.sub_module_url_sp,
                is_active: true,
                created_by: admin.id,
                created_at: new Date().toISOString(),
            })),
        })

        // Get the feature IDs of the newly created pages
        const newFeatures = await db.admin_protected_feature.findMany({
            where: {
                module_id: moduleData.module_id,
                sub_module_name: {
                    in: args.page.map((page) => page.sub_module_name),
                },
                created_by: admin.id,
            },
            select: {
                feature_id: true,
                sub_module_name: true,
            },
        })

        // Add permissions for admin role 1 (root admin)
        if (newFeatures.length > 0) {
            await db.admin_role_permission.createMany({
                data: newFeatures.map((feature) => ({
                    admin_role_id: 1, // Root admin role ID
                    feature_id: feature.feature_id,
                    permission_for_user: {
                        create: true,
                        read: true,
                        update: true,
                        delete: true,
                    },
                    permission_for_sp: {
                        create: true,
                        read: true,
                        update: true,
                        delete: true,
                    },
                })),
            })
        }

        return {
            result: true,
            message: 'Feature page created successfully',
        }
    } catch (error) {
        throw new GraphQLError(error)
    }
}

const getSubmodules = async (
    _: any,
    args: { moduleId: number },
    ctx: Context
) => {
    try {
        validateAdmin(ctx)
        // Fetch submodules for the given module ID
        const submodules = await db.admin_protected_feature.findMany({
            where: {
                module_id: args.moduleId,
                is_active: true,
            },
            select: {
                feature_id: true,
                sub_module_name: true,
                sub_module_url_user: true,
                sub_module_url_sp: true,
            },
        })
        // Transform to match the expected return type
        return submodules.map((submodule) => ({
            submodule_id: submodule.feature_id,
            submodule_name: submodule.sub_module_name,
            submodule_url_user: submodule.sub_module_url_user,
            submodule_url_sp: submodule.sub_module_url_sp,
        }))
    } catch (error) {
        throw new GraphQLError(`${error}`)
    }
}

interface SectionInput {
    section_id: string
    section_name: string
    sub_module_name: string
    section_url_user?: string
    section_url_sp?: string
}

const createNewSection = async (
    _: any,
    args: {
        moduleId: number
        submoduleId: number
        sections: Array<SectionInput>
    },
    ctx: Context
) => {
    try {
        const admin = validateAdmin(ctx)
        if (admin) {
            // Check if module exists
            const module = await db.admin_module.findUnique({
                where: { module_id: args.moduleId },
            })

            if (!module) {
                throw new GraphQLError('Module not found')
            }

            // Check if submodule exists
            const submodule = await db.admin_protected_feature.findFirst({
                where: {
                    feature_id: args.submoduleId,
                    module_id: args.moduleId,
                },
            })

            if (!submodule) {
                throw new GraphQLError('Submodule not found')
            }

            // Create sections
            for (const section of args.sections) {
                await db.admin_protected_feature.create({
                    data: {
                        parent_module_id: 1,
                        parent_module_name: 'Dashboard',
                        module_id: args.moduleId,
                        module_name: submodule.module_name,
                        is_module: false,
                        sub_module_name: section.section_name,
                        sub_module_url_user: section.section_url_user || null,
                        sub_module_url_sp: section.section_url_sp || null,
                        section_id: section.section_id,
                        is_active: true,
                        created_by: admin.id,
                        created_at: new Date().toISOString(),
                    },
                })

                // Add permissions for admin role 1 (root admin)
                const newSections = await db.admin_protected_feature.findMany({
                    where: {
                        module_id: args.moduleId,
                        section_id: section.section_id,
                        created_by: admin.id,
                    },
                    select: {
                        feature_id: true,
                    },
                })

                if (newSections.length > 0) {
                    await db.admin_role_permission.createMany({
                        data: newSections.map((section) => ({
                            admin_role_id: 1, // Root admin role ID
                            feature_id: section.feature_id,
                            permission_for_user: {
                                create: true,
                                read: true,
                                update: true,
                                delete: true,
                            },
                            permission_for_sp: {
                                create: true,
                                read: true,
                                update: true,
                                delete: true,
                            },
                        })),
                    })
                }
            }

            return {
                result: true,
                message: 'Sections created successfully',
            }
        } else {
            throw new GraphQLError(
                'Only authenticated user could access this graph'
            )
        }
    } catch (error) {
        return {
            result: false,
            message: `Error creating sections: ${error}`,
        }
    }
}

const adminPermissionResolver = {
    Mutation: {
        createAdminRole,
        updateAdminRole,
        deleteAdminRole,
        assignRoleToAdmin,
        publishModule,
        createNewModule,
        createNewPage,
        createNewSection,
    },
    Query: {
        getAllAdminRoles,
        getUnpublishedModules,
        getUnpublishedModuleById,
        getAdminRoleByRoleId,
        getSubmodules,
    },
}

export default adminPermissionResolver
