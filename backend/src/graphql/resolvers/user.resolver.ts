import {
    Gender,
    OnboardingStage,
    Prisma,
    PrismaPromise,
    preferred_language,
    user,
} from '@prisma/client'
import { GraphQLError } from 'graphql'
import isEmpty from 'is-empty'
import DataProvider from '../../data'
import { pubsub } from '../../index'
import Queues from '../../queue'
import {
    HistoryRecord,
    logBulkHistory,
    logHistory,
} from '../../services/history.service'
import {
    checkUserDocumentsAndSendNotification,
    updateBellNotificationOfUser,
} from '../../services/notification.service'
import { validateAdmin } from '../../utils/authValidators'
import { MASTER_ORG_ID } from '../../utils/constants'
import { EventList, EventType } from '../../utils/eventTypes'
import { generateJwt } from '../../utils/jwt'
import { TmsHelper } from '../../utils/tms.user'
import { Context } from '../../utils/types'
import { convertToSeconds, removeEmptyData } from '../../utils/utils'
import {
    FulfillmentType,
    Pagination,
    UserOnboardingDataUpdateInput,
    UserResolvers,
    UserRole,
} from '../__generated__/types'
import { IS_PROD, MAIN_ORG_DATA, tokenExpiryData } from './../../utils/envVars'
import {
    awardReferralPoints,
    createUserReferral,
    createUserTimestamp,
    updateUserOnboardingTimestamp,
    validateReferralCode,
} from './../../utils/user'
import { UserMetaType } from './fcm.resolver'

const db = DataProvider.getDbInstance()
const redis = DataProvider.getRedisInstance()

/**
 * This resolvers return details of the authenticated user
 * @param _ root
 * @param __ args
 * @param ctx Context
 * @returns User
 */
const singleUserResolver = async (_: unknown, __: unknown, ctx: Context) => {
    pubsub.publish(`SHOW_TTT_MODAL_${ctx.user?.id}`, true)

    try {
        const { user } = ctx

        const userData = await db.user.findFirst({
            where: {
                id: user?.id || -1,
            },
        })
        if (!userData || !user) {
            throw new GraphQLError('user not valid')
        }
        if (user) {
            return {
                data: userData,
                message: 'User data fetched successfully',
                result: true,
            }
        } else {
            throw new GraphQLError(
                'The user is not valid. You cannot access this data'
            )
        }
    } catch (error) {
        throw new GraphQLError(error)
    }
}

/**
 * Used for updating user
 * @param _ root
 * @param args Arguments
 * @param ctx Context
 * @returns updated user
 */
const updateUserResolver = async (
    _: unknown,
    args: any,
    ctx: { user: any; ipAddr?: string; userAgent?: string }
) => {
    try {
        const { user } = ctx

        const { work_address, pincode, landmark, state, city } = args.data
        const new_location_values = {
            work_address: work_address,
            pincode: pincode,
            landmark: landmark,
            state: state,
            country: 'India',
            city: city,
        }
        //TODO :  add validation
        if (args?.data?.email === '') {
            args.data.email = null
        }

        const existingUser = await db.user.findFirst({
            where: {
                id: user.id,
            },
        })

        //i know this is very bad.... but will fix it once we have time
        args.data = {
            ...args.data,
            ...(args?.data?.meta
                ? {
                      meta: {
                          ...(typeof existingUser?.meta === 'object'
                              ? existingUser?.meta
                              : {}),
                          ...user?.meta,
                          ...args?.data?.meta,
                      },
                  }
                : {}),
        }

        let locationId
        let newLocationId
        if (user) {
            if (args?.data?.location_id == 0) {
                const { work_address, pincode, landmark, state, city } =
                    args.data
                const location = await db.location.create({
                    data: {
                        work_address: work_address,
                        pincode: pincode,
                        landmark: landmark,
                        state: state,
                        country: 'India',
                        city: city,
                    },
                })
                locationId = location.id
                newLocationId = location.id
                delete args.data.work_address
                delete args.data.pincode
                delete args.data.landmark
                delete args.data.state
                delete args.data.city
                const updateUser = await db.user.update({
                    where: {
                        phone: user.phone,
                    },
                    data: {
                        tms_lead_is_updated: false,
                        ...args?.data,
                        location_id: locationId,
                    },
                })
                //notify user
                if (
                    args.data.onboarding_stage === OnboardingStage.ONBOARD &&
                    IS_PROD
                ) {
                    if (updateUser.email) {
                        ;``
                        Queues.emailQueue.add({
                            receiver: updateUser.email,
                            subject: 'Congratulations! Welcome Onboard!',
                            template_name: 'SUCCESSFULLY_ONBOARDED',
                            variables: {
                                name: updateUser.name,
                            },
                            user_id: user.id,
                            org_id: user.organization_id,
                        })
                    }
                    const token = (updateUser?.meta as UserMetaType)?.fcm_token
                    if (token) {
                        Queues.pushNotificationQueue.add({
                            notification: {
                                title: 'Successfully Onboarded',
                                body: 'Congratulations!! You have been successfully onboarded with us.',
                            },
                            token,
                            meta: {
                                user_id: user.id,
                                user_name: user.name,
                                user_org_id: user.organization_id,
                            },
                        })
                    }
                    updateUser.phone &&
                        Queues.whatsAppQueue.add({
                            template_name: 'v2_successfully_onboarded',
                            wa_number: updateUser.phone,
                            variables: {
                                'parameters[0]': updateUser.name,
                            },
                            meta: {
                                user_id: user.id,
                                user_name: user.name,
                                user_org_id: user.organization_id,
                            },
                        })
                }

                if (
                    args.data.onboarding_stage ===
                        OnboardingStage.VERIFICATION &&
                    IS_PROD &&
                    updateUser.email
                ) {
                    Queues.emailQueue.add({
                        receiver: updateUser.email,
                        subject: 'Document Verification Update - Stay Tuned!',
                        template_name: 'DOCUMENT_SUBMITTED',
                        variables: {
                            name: updateUser.name,
                        },
                        user_id: user.id,
                        org_id: user.organization_id,
                    })
                }

                const logHistoryArray: HistoryRecord[] = [
                    {
                        entity_id: user.id?.toString(),
                        ...EventList[EventType.USER_UPDATED],
                        creator_type: 'USER' as const,
                        new_values: {
                            tms_lead_is_updated: false,
                            ...args?.data,
                            location_id: locationId,
                        },
                        user_id: user.id || -1,
                        org_id: user.organization_id || -1,
                        creator_id: user.id,
                        meta: {
                            ip_addr: ctx?.ipAddr || '',
                            user_agent: ctx?.userAgent || '',
                            user_id: user.id || -1,
                            user_name: user.name || '',
                            user_org_id: user.organization_id || -1,
                            creator_id: user.id,
                            creator_name: user.name,
                            creator_org_id: user.organization_id,
                        },
                    },
                    {
                        entity_id: newLocationId?.toString(),
                        ...EventList[EventType.USER_LOCATION_UPDATED],
                        creator_type:
                            args?.data?.location_id == 0
                                ? ('USER' as const)
                                : undefined,
                        new_values: new_location_values,
                        user_id: user.id || -1,
                        org_id: user.organization_id || -1,
                        creator_id:
                            args?.data?.location_id == 0 ? user.id : null,
                        meta: {
                            ip_addr: ctx?.ipAddr || '',
                            user_agent: ctx?.userAgent || '',
                            user_id: user.id || -1,
                            user_name: user.name || '',
                            user_org_id: user.organization_id || -1,
                            creator_id:
                                args?.data?.location_id == 0 ? user.id : null,
                            creator_name:
                                args?.data?.location_id == 0 ? user.name : null,
                            creator_org_id:
                                args?.data?.location_id == 0
                                    ? user.organization_id
                                    : null,
                        },
                    },
                ]

                logBulkHistory(logHistoryArray)

                return {
                    data: updateUser,
                    result: true,
                    message: 'Updated successfully',
                }
            } else if (
                args?.data?.location_id != null ||
                args?.data?.location_id != undefined
            ) {
                const location = await db.location.update({
                    where: {
                        id: args.data.location_id,
                    },
                    data: {
                        work_address: args.data.work_address,
                        pincode: args.data.pincode,
                        landmark: args.data.landmark,
                        state: args.data.state,
                        country: 'India',
                        city: args.data.city,
                    },
                })
                newLocationId = location.id
            }
            delete args.data.work_address
            delete args.data.pincode
            delete args.data.landmark
            delete args.data.state
            delete args.data.city
            const updateUser = await db.user.update({
                where: {
                    phone: user.phone,
                },
                data: {
                    tms_lead_is_updated: false,
                    ...args?.data,
                },
            })

            const key = `user_${user.phone}`
            //here update the cache in redis
            await (
                await redis
            ).setEx(
                key,
                convertToSeconds(tokenExpiryData.USER.ANDROID),
                JSON.stringify(updateUser)
            )

            if (args.data.onboarding_stage === OnboardingStage.DASHBOARD) {
                await checkUserDocumentsAndSendNotification(updateUser)
                await updateBellNotificationOfUser(updateUser.id)
            }

            await updateUserOnboardingTimestamp(updateUser)
            await createUserTimestamp({ user_id: updateUser.id })
            if (args.data.onboarding_stage === OnboardingStage.VERIFICATION) {
                if (IS_PROD) {
                    if (updateUser.email) {
                        Queues.emailQueue.add({
                            receiver: updateUser.email,
                            subject:
                                'Document Verification Update - Stay Tuned!',
                            template_name: 'DOCUMENT_SUBMITTED',
                            variables: {
                                name: updateUser.name,
                            },
                            user_id: user.id,
                            org_id: user.organization_id,
                        })
                    }
                }
            }
            const logHistoryArray: HistoryRecord[] = [
                {
                    entity_id: user.id?.toString(),
                    ...EventList[EventType.USER_UPDATED],
                    creator_type: 'USER' as const,
                    new_values: {
                        tms_lead_is_updated: false,
                        ...args?.data,
                    },
                    user_id: user.id || -1,
                    org_id: user.organization_id || -1,
                    creator_id: user.id,
                    meta: {
                        ip_addr: ctx?.ipAddr || '',
                        user_agent: ctx?.userAgent || '',
                        user_id: user.id || -1,
                        user_name: user.name || '',
                        user_org_id: user.organization_id || -1,
                        creator_id: user.id,
                        creator_name: user.name,
                        creator_org_id: user.organization_id,
                    },
                },
                {
                    entity_id: newLocationId?.toString(),
                    ...EventList[EventType.USER_LOCATION_UPDATED],
                    creator_type:
                        args?.data?.location_id == 0
                            ? ('USER' as const)
                            : undefined,
                    new_values: new_location_values,
                    user_id: user.id || -1,
                    org_id: user.organization_id || -1,
                    creator_id: args?.data?.location_id == 0 ? user.id : null,
                    meta: {
                        ip_addr: ctx?.ipAddr || '',
                        user_agent: ctx?.userAgent || '',
                        user_id: user.id || -1,
                        user_name: user.name || '',
                        user_org_id: user.organization_id || -1,
                        creator_id:
                            args?.data?.location_id == 0 ? user.id : null,
                        creator_name:
                            args?.data?.location_id == 0 ? user.name : null,
                        creator_org_id:
                            args?.data?.location_id == 0
                                ? user.organization_id
                                : null,
                    },
                },
            ]

            logBulkHistory(logHistoryArray)
            await awardReferralPoints(updateUser)

            await TmsHelper.processUserForTmsLead(updateUser.id)

            return {
                data: updateUser,
                result: true,
                message: 'Updated successfully',
            }
        } else {
            throw new GraphQLError('Something went wrong')
        }
    } catch (error) {
        if (error.code === 'P2002' && error.meta?.target.includes('email')) {
            throw new GraphQLError(
                'Email Already Exists, Please Use Different Email Address'
            )
        } else {
            throw new GraphQLError(error)
        }
    }
}

/**
 * verify users phone number
 * @param _ root
 * @param args arguments
 * @param ctx Context
 * @returns returns the result after updating
 */
const verifyPhoneNumber = async (
    _: any,
    args: { otp: string },
    ctx: Context
) => {
    const { user } = ctx

    try {
        const str_data = await (await redis).get(`phone_verify_${user.phone}`)

        if (!str_data) {
            return {
                result: false,
                message: 'OTP expired',
            }
        }

        let data = JSON.parse(str_data)

        if (data.attempts > 4) {
            return {
                result: false,
                message: 'Maximum attempts reached',
            }
        }

        if (data.OTP !== args.otp) {
            data = {
                ...data,
                attempts: data.attempts + 1,
            }

            await (
                await redis
            ).set(`phone_verify_${user.phone}`, JSON.stringify(data))

            return {
                result: false,
                message: 'Invalid OTP, Please try again',
            }
        }

        const updatedUser = await db.user.update({
            data: {
                phoneVerified: true,
                tms_lead_is_updated: false,
                onboarding_stage: 'GENERAL_DETAILS',
            },
            where: {
                id: user.id,
            },
        })
        logHistory({
            entity_id: updatedUser.id?.toString(),
            ...EventList[EventType.USER_UPDATED],
            creator_type: ctx.admin ? 'ADMIN' : 'USER',
            new_values: {
                phoneVerified: true,
                tms_lead_is_updated: false,
                onboarding_stage: 'GENERAL_DETAILS',
            },
            user_id: updatedUser.id || -1,
            org_id: updatedUser.organization_id || -1,
            creator_id: ctx.admin ? ctx.admin?.id : ctx.user?.id,
            meta: {
                ip_addr: ctx?.ipAddr || '',
                user_agent: ctx?.userAgent || '',
                user_id: updatedUser.id || -1,
                user_name: updatedUser.name || '',
                user_org_id: updatedUser.organization_id || -1,
                creator_id: ctx.admin ? ctx.admin?.id : ctx.user?.id,
                creator_name: ctx.admin ? ctx.admin?.name : ctx.user?.name,
                creator_org_id: ctx.admin
                    ? ctx.admin?.active_org_id
                    : ctx.user?.organization_id,
            },
        })
        await updateUserOnboardingTimestamp(updatedUser)

        return {
            result: true,
            message: 'Updated successfully',
        }
    } catch (err) {
        throw new GraphQLError('There was some issue')
    }
}

const resolveUsersOrg = async (root: { organization_id: number }) => {
    try {
        const orgInfo = await db.organization.findFirst({
            where: {
                id: root.organization_id,
            },
        })

        return orgInfo
    } catch (error) {
        throw new GraphQLError(
            'There was some error in fetching org info for user'
        )
    }
}

/**
 * Returns the role of the root user
 * @param root Root user object
 * @returns the role of the root user
 */
const resolveUserRole = async (root: { id: number }) => {
    try {
        const roleInfos = await db.role_map.findMany({
            where: {
                user_id: root.id,
            },
            include: {
                role: {
                    select: {
                        name: true,
                        permission: true,
                    },
                },
            },
        })

        return roleInfos.map((info) => info.role)
    } catch (error) {
        throw new GraphQLError(
            'There was some issue during resolving user role'
        )
    }
}

/**
 * Deletes the user
 * @param _ root
 * @param __ args
 * @param ctx Context
 * @returns deleted operation result
 */
const deleteUserResolver = async (
    _: unknown,
    __: unknown,
    ctx: {
        user: { phone: string }
        ipAddr?: string
        userAgent?: string
        admin: any
    }
) => {
    try {
        const { user } = ctx
        if (!user) {
            throw new GraphQLError('user not valid')
        }
        const deactivateUser = await db.user.update({
            where: {
                phone: user.phone,
            },
            data: {
                tms_lead_is_updated: false,
                isActive: false,
            },
            select: {
                id: true,
                name: true,
                organization_id: true,
            },
        })
        if (deactivateUser) {
            logHistory({
                entity_id: deactivateUser.id?.toString(),
                ...EventList[EventType.USER_DELETED],
                creator_type: 'USER',
                new_values: {
                    tms_lead_is_updated: false,
                    isActive: false,
                },
                user_id: deactivateUser.id,
                org_id: deactivateUser?.organization_id || -1,
                creator_id: ctx.admin ? ctx.admin?.id : deactivateUser.id,
                meta: {
                    ip_addr: ctx?.ipAddr || '',
                    user_agent: ctx?.userAgent || '',
                    user_id: deactivateUser?.id || -1,
                    user_name: deactivateUser?.name || '',
                    user_org_id: deactivateUser?.organization_id || -1,
                    creator_id: ctx.admin?.id,
                    creator_name: ctx.admin?.name,
                    creator_org_id: ctx.admin?.active_org_id,
                },
            })
            return {
                result: true,
                message: 'User Deactivated successfully',
            }
        }
        return {
            result: false,
            message: 'Something went wrong, please try again later',
        }
    } catch (error) {
        throw new GraphQLError(error)
    }
}

const userDetailsForAdmin = async (
    _: any,
    args: { user_id: number },
    ctx: Context
) => {
    validateAdmin(ctx)

    try {
        const user_detail = await db.user.findFirst({
            where: {
                id: args.user_id,
            },
            include: {
                organization: true,
                designation: {
                    select: {
                        id: true,
                        name: true,
                        level: true,
                        is_active: true,
                        is_deleted: true,
                        created_at: true,
                        updated_at: true,
                        designation_expertise_map: {
                            select: {
                                id: false,
                                designation_id: false,
                                expertise_id: false,
                                expertise: {
                                    select: {
                                        id: true,
                                        name: true,
                                        icon: true,
                                    },
                                },
                            },
                        },
                        admin: {
                            select: {
                                id: true,
                                name: true,
                                type: true,
                            },
                        },
                    },
                },
                user_onboarding_data: {
                    select: {
                        date_of_joining: true,
                        fathers_name: true,
                        martial_status: true,
                        mothers_name: true,
                        salary_offered: true,
                        salary_vp: true,
                    },
                },
            },
        })
        return user_detail
    } catch (error) {
        throw new GraphQLError(error)
    }
}

/**
 * Returns the result of the root user
 * @param root User
 * @returns expertise of the root user
 */
const resolveExpertise = async (root: user) => {
    try {
        const expertise = await db.expertise.findMany({
            where: {
                expertise_user_map: {
                    some: {
                        user_id: root.id,
                    },
                },
            },
        })

        return expertise
    } catch (error) {
        throw new GraphQLError(error)
    }
}

/**
 * Resolves the location of the root user
 * @param root  Root user
 * @returns returns the location object of the root user
 */
const resolveUserLocation = async (root: user) => {
    try {
        if (!root.location_id) {
            return null
        }

        const location = await db.location.findFirst({
            where: {
                id: root.location_id,
            },
        })

        return location
    } catch (err) {
        throw new GraphQLError(err)
    }
}

interface UpdateUserAdminType {
    name: string
    source: string
    poc: string
    gender: Gender
    phone: string
    email: string
    location_id: number
    photoUrl: string
    onboarding_stage: OnboardingStage
    work_address: string
    skills: [number]
    pincode: string
    state: string
    district_taluka?: string
    landmark: string
    city: string
    remark: string
    user_state: string
    fulfillment_type: FulfillmentType
    designation: String
    user_onboarding_data: UserOnboardingDataUpdateInput
    meta: any
}

interface UpdateData {
    name: string
    source: string
    poc: string
    email: string
    phone: string
    gender: Gender
    onboarding_stage: OnboardingStage
    remark: string
    user_state: string
    fulfillment_type: FulfillmentType
    updated_at: Date
    location?: {
        upsert: {
            create: {
                work_address: string
                district_taluka?: string
                pincode: string
                landmark: string
                state: string
                country: string
                city: string
            }
            update: {
                work_address: string
                district_taluka?: string
                pincode: string
                landmark: string
                state: string
                country: string
                city: string
            }
        }
    }
}

/**
 * Update user as admin or service provider
 * @param _ root
 * @param args argument
 * @param ctx Context
 * @returns the updated user info
 */
const updateUserAdmin = async (
    _: any,
    args: { user_id: number; data: UpdateUserAdminType },
    ctx: Context
) => {
    const admin = validateAdmin(ctx)
    let onboarding_data_count

    // TODO: Check weather the admin is allowed to update the user or not
    // as it's very unsafe right now (any admin can update any user)
    try {
        const updateData: UpdateData = {
            name: args.data.name,
            source: args.data.source,
            poc: args.data.poc,
            email: args.data.email,
            phone: args.data.phone,
            gender: args.data.gender,
            onboarding_stage: args.data.onboarding_stage,
            remark: args.data.remark,
            user_state: args.data.user_state,
            fulfillment_type: args.data.fulfillment_type,
            updated_at: new Date(),
        }

        const updated_meta = {
            ...args?.data?.meta,
            last_edited_by_id: ctx?.admin?.id,
        }

        if (args.data.work_address) {
            updateData.location = {
                upsert: {
                    create: {
                        work_address: args.data.work_address,
                        pincode: args.data.pincode,
                        landmark: args.data.landmark,
                        state: args.data.state,
                        country: 'India',
                        city: args.data.city,
                        district_taluka: args.data?.district_taluka,
                    },
                    update: {
                        work_address: args.data.work_address,
                        pincode: args.data.pincode,
                        landmark: args.data.landmark,
                        state: args.data.state,
                        country: 'India',
                        city: args.data.city,
                        district_taluka: args.data?.district_taluka,
                    },
                },
            }
        }
        //remove Empty data
        const userUpdatedData = removeEmptyData(updateData)

        const existingUser = await db.user.findFirst({
            where: {
                id: args?.user_id,
            },
        })
        const existingExpertise = await db.user_expertise_map.findMany({
            where: {
                user_id: args.user_id,
            },
            select: {
                expertise: {
                    select: {
                        id: true,
                        name: true,
                    },
                },
            },
        })
        const transactionItems: Array<PrismaPromise<any>> = [
            db.user.update({
                where: {
                    id: args.user_id,
                },
                data: {
                    tms_lead_is_updated: false,
                    meta: {
                        ...(typeof existingUser?.meta === 'object'
                            ? existingUser?.meta
                            : {}),
                        ...(typeof updated_meta === 'object'
                            ? updated_meta
                            : {}),
                    },
                    ...userUpdatedData,
                },
            }),
        ]

        if (args?.data?.designation) {
            transactionItems.push(
                db.user.update({
                    where: {
                        id: args.user_id,
                    },
                    data: {
                        designation: {
                            connect: { id: args.data.designation as string },
                        },
                    },
                })
            )
        }

        if (args?.data?.skills?.length > 0) {
            transactionItems.push(
                db.user_expertise_map.deleteMany({
                    where: {
                        user_id: args.user_id,
                    },
                })
            )
            transactionItems.push(
                db.user_expertise_map.createMany({
                    data: args.data.skills.map((id) => ({
                        expertise_id: id,
                        user_id: args.user_id,
                    })),
                    skipDuplicates: true,
                })
            )
        }

        if (!isEmpty(args?.data?.user_onboarding_data)) {
            onboarding_data_count = await db.user_onboarding_data.count({
                where: {
                    user_id: args.user_id,
                },
            })

            if (onboarding_data_count == 0) {
                const create_query = db.user_onboarding_data.create({
                    data: {
                        ...args.data.user_onboarding_data,
                        user_id: args.user_id,
                    },
                })

                transactionItems.push(create_query)
            } else {
                const update_query = db.user_onboarding_data.update({
                    data: {
                        ...args.data.user_onboarding_data,
                    },
                    where: {
                        user_id: args.user_id,
                    },
                })
                transactionItems.push(update_query)
            }
        }

        const [user, , , onboarding_data]: any = await db.$transaction(
            transactionItems
        )

        //update redis as the updated ctx is needed
        const key = `user_${existingUser?.phone}`
        // here update the cache in redis
        await (
            await redis
        ).setEx(
            key,
            convertToSeconds(tokenExpiryData.USER.ANDROID),
            JSON.stringify(user)
        )
        await TmsHelper.processUserForTmsLead(args.user_id)

        const logHistoryArray = [
            {
                entity_id: user.id?.toString(),
                ...EventList[EventType.USER_UPDATED],
                creator_type: 'ADMIN' as const,
                new_values: {
                    tms_lead_is_updated: false,
                    meta: {
                        ...(typeof existingUser?.meta === 'object'
                            ? existingUser?.meta
                            : {}),
                        ...(typeof updated_meta === 'object'
                            ? updated_meta
                            : {}),
                    },
                    ...userUpdatedData,
                    designation: !args?.data?.designation
                        ? ''
                        : args.data.designation,
                },
                user_id: user.id || -1,
                org_id: user.organization_id || -1,
                creator_id: admin.id,
                meta: {
                    ip_addr: ctx?.ipAddr || '',
                    user_agent: ctx?.userAgent || '',
                    user_id: user.id || -1,
                    user_name: user.name || '',
                    user_org_id: user.organization_id || -1,
                    creator_id: admin.id,
                    creator_name: admin.name,
                    creator_org_id: admin.active_org_id,
                },
            },

            {
                entity_id: onboarding_data?.id?.toString(),
                ...(onboarding_data_count === 0
                    ? EventList[EventType.USER_ONBOARDING_CREATED]
                    : EventList[EventType.USER_ONBOARDING_UPDATED]),
                creator_type: 'ADMIN' as const,
                new_values: {
                    ...args.data.user_onboarding_data,
                },
                user_id: user.id || -1,
                org_id: user.organization_id || -1,
                creator_id: admin.id,
                meta: {
                    ip_addr: ctx?.ipAddr || '',
                    user_agent: ctx?.userAgent || '',
                    user_id: user.id || -1,
                    user_name: user.name || '',
                    user_org_id: user.organization_id || -1,
                    creator_id: admin.id,
                    creator_name: admin.name,
                    creator_org_id: admin.active_org_id,
                },
            },
        ]
        if (existingExpertise?.length) {
            logHistory({
                entity_id: user?.id?.toString(),
                ...EventList[EventType.EXPERTISE_DELETED],
                creator_type: ctx.admin ? 'ADMIN' : 'USER',
                new_values: existingExpertise,
                user_id: user?.id || -1,
                org_id: user?.organization_id || -1,
                creator_id: ctx.admin ? ctx.admin?.id : ctx.user?.id,
                meta: {
                    ip_addr: ctx?.ipAddr || '',
                    user_agent: ctx?.userAgent || '',
                    user_id: user?.id || -1,
                    user_name: user?.name || '',
                    user_org_id: user?.organization_id || -1,
                    creator_id: ctx.admin?.id,
                    creator_name: ctx.admin?.name,
                    creator_org_id: ctx.admin?.active_org_id,
                },
            })
        }
        if (args?.data?.skills?.length > 0) {
            logHistoryArray.push(
                ...args.data.skills.map((id) => ({
                    entity_id: id?.toString(),
                    ...EventList[EventType.EXPERTISE_CREATED],
                    creator_type: 'ADMIN' as const,
                    new_values: {
                        expertise_id: id,
                        user_id: user.id,
                    },
                    user_id: user.id || -1,
                    org_id: user.organization_id || -1,
                    creator_id: admin.id,
                    meta: {
                        ip_addr: ctx?.ipAddr || '',
                        user_agent: ctx?.userAgent || '',
                        user_id: user.id || -1,
                        user_name: user.name || '',
                        user_org_id: user.organization_id || -1,
                        creator_id: admin.id,
                        creator_name: admin.name,
                        creator_org_id: admin.active_org_id,
                    },
                }))
            )
        }
        if (
            args?.data?.user_state === 'Reject' ||
            args?.data?.user_state === 'Ban'
        ) {
            logHistoryArray.push({
                entity_id: user.id?.toString(),
                ...EventList[
                    args?.data?.user_state === 'Reject'
                        ? EventType.USER_REJECTED
                        : EventType.USER_BANNED
                ],
                creator_type: 'ADMIN' as const,
                new_values: {
                    user_state: args?.data?.user_state,
                },
                user_id: user.id || -1,
                org_id: user.organization_id || -1,
                creator_id: admin.id,
                meta: {
                    ip_addr: ctx?.ipAddr || '',
                    user_agent: ctx?.userAgent || '',
                    user_id: user.id || -1,
                    user_name: user.name || '',
                    user_org_id: user.organization_id || -1,
                    creator_id: admin.id,
                    creator_name: admin.name,
                    creator_org_id: admin.active_org_id,
                },
            })
        }
        logBulkHistory(logHistoryArray)

        await updateUserOnboardingTimestamp(user)
        await createUserTimestamp({ user_id: user.id, admin_id: admin.id })
        await awardReferralPoints(user)
        return user
    } catch (error) {
        if (error.code === 'P2002' && error.meta?.target.includes('email')) {
            throw new GraphQLError(
                'Email Already Exists, Please Use Different Email Address'
            )
        } else {
            throw new GraphQLError(error)
        }
    }
}

const getRejectandBanUser = async (
    _: any,
    args: { search: string; pagination: Pagination },
    ctx: Context
) => {
    try {
        const admin = validateAdmin(ctx)
        const { search, pagination } = args
        const skip = pagination?.skip || 0
        const take = pagination?.take || 10

        const user_where: Prisma.userWhereInput = {
            user_state: {
                in: ['Reject', 'Ban'],
            },
            organization_id: admin.active_org_id || -1,
            OR: [
                {
                    name: {
                        contains: search,
                        mode: 'insensitive',
                    },
                },
                {
                    email: {
                        contains: search,
                        mode: 'insensitive',
                    },
                },
                {
                    phone: {
                        contains: search,
                    },
                },
                {
                    remark: {
                        contains: search,
                        mode: 'insensitive',
                    },
                },
            ],
        }
        const users = await db.user.findMany({
            where: user_where,
            take: take,
            skip: take * skip,
        })

        const total_count = await db.user.count({
            where: user_where,
        })
        return {
            data: users,
            total_count: total_count,
        }
    } catch (error) {
        throw new GraphQLError(error)
    }
}

const updateRejectandBanUser = async (
    _: any,
    args: { user_id: number },
    ctx: Context
) => {
    try {
        const admin = validateAdmin(ctx)
        const user = await db.user.update({
            where: {
                id: args.user_id,
            },
            data: {
                tms_lead_is_updated: false,
                user_state: null,
                remark: null,
            },
        })
        logHistory({
            entity_id: user.id?.toString(),
            ...EventList[EventType.USER_ACCEPTED],
            creator_type: 'ADMIN',
            new_values: {
                tms_lead_is_updated: false,
                user_state: null,
                remark: null,
            },
            user_id: user.id || -1,
            org_id: user.organization_id || -1,
            creator_id: admin.id,
            meta: {
                ip_addr: ctx?.ipAddr || '',
                user_agent: ctx?.userAgent || '',
                user_id: user.id || -1,
                user_name: user.name || '',
                user_org_id: user.organization_id || -1,
                creator_id: admin.id,
                creator_name: admin.name,
                creator_org_id: admin.active_org_id,
            },
        })
        await createUserTimestamp({ user_id: user.id, admin_id: admin.id })

        return {
            data: user,
            message: 'User updated successfully',
            result: true,
        }
    } catch (error) {
        throw new GraphQLError(error)
    }
}

interface UsersForAdminArg {
    batch: number
    filter?: {
        searchTerm?: string
        role?: number
        gender?: Gender
        isActive?: boolean
    }
}

/**
 *  how this works
 *
 *  able to search on few fields
 *  able to short it according to
 *  role
 *  gender
 *  isActive
 *  on boarding stage
 * Used for fetching users that a admin is allowed to update
 * @param _ root
 * @param args Arguments
 * @param ctx Context
 * @returns the user for admin
 */
const getUsersForAdmin = async (
    _: any,
    args: UsersForAdminArg,
    ctx: Context
) => {
    const admin = validateAdmin(ctx)

    try {
        return await db.user.findMany({
            where: {
                organization_id: admin.active_org_id || -1,
                ...(args.filter
                    ? {
                          OR: [
                              {
                                  name: {
                                      contains: args.filter.searchTerm,
                                      mode: 'insensitive',
                                  },
                              },
                              {
                                  email: {
                                      contains: args.filter.searchTerm,
                                      mode: 'insensitive',
                                  },
                              },
                              {
                                  phone: {
                                      contains: args.filter.searchTerm,
                                      mode: 'insensitive',
                                  },
                              },
                              { gender: args.filter.gender },
                              { isActive: args.filter.isActive },
                              {
                                  role_map: {
                                      some: {
                                          id: args.filter.role,
                                      },
                                  },
                              },
                          ],
                      }
                    : {}),
            },
            orderBy: [
                {
                    created_at: 'desc',
                },
            ],
        })
    } catch (error) {
        throw new GraphQLError(error)
    }
}
interface WorkExperienceType {
    organization: string
    start_date: Date
    end_date: Date
    self_employed: boolean
    workexp_location: string
    workexp_city: string
    workexp_state: string
    workexp_pincode: string
}

interface WorkExperienceType {
    organization: string
    start_date: Date
    end_date: Date
    self_employed: boolean
    workexp_location: string
    workexp_city: string
    workexp_state: string
    workexp_pincode: string
}

interface WorkExperienceType {
    organization: string
    start_date: Date
    end_date: Date
    self_employed: boolean
    workexp_location: string
    workexp_city: string
    workexp_state: string
    workexp_pincode: string
}

interface WorkExperienceType {
    organization: string
    start_date: Date
    end_date: Date
    self_employed: boolean
    workexp_location: string
    workexp_city: string
    workexp_state: string
    workexp_pincode: string
}

interface BulkUserType {
    email: string
    org_id: number
    name: string
    phone: string
    source: string
    poc: string
    gender: Gender
    work_address: string
    city: string
    state: string
    pincode: string
    skills: [number]
    work_experience: [WorkExperienceType]
}

const createUserBulk = async (
    _: any,
    args: { data: Array<BulkUserType> },
    ctx: Context
) => {
    const admin = validateAdmin(ctx)
    console.log(ctx, 'CTX')
    const data = args.data || []

    if (isEmpty(data)) {
        throw new GraphQLError('Please provide valid data')
    }

    try {
        const existingUsers = await db.user.findMany({
            where: {
                phone: {
                    in: args.data.map((m) => m.phone),
                },
                organization_id: admin.active_org_id || -1,
            },
        })
        if (existingUsers.length > 0) {
            throw new GraphQLError('User already present')
        }

        const transaction = data.map((user) => {
            const userCreateInput: Prisma.userCreateInput = {
                name: user.name,
                phone: user.phone,
                source: user.source,
                poc: user.poc || '',
                organization: {
                    connect: {
                        id: user.org_id || admin.active_org_id || -1,
                    },
                },
                // creator: {
                //     connect: {
                //         id: admin.id,
                //     },
                // },
                gender: user.gender,
                onboarding_stage: OnboardingStage.DASHBOARD,
                updated_at: new Date(),
                user_type:
                    user.org_id && user.org_id !== 1
                        ? UserRole.ServiceProvider
                        : UserRole.Technician,
                meta: {
                    creator_id: admin.id,
                    creator_name: admin.name,
                    created_at: new Date().toString(),
                },
            }
            if (user.skills?.length > 0) {
                userCreateInput.user_expertise_map = {
                    create: user.skills.map((id) => ({
                        expertise_id: id,
                    })),
                }
            }

            return db.user.create({
                data: userCreateInput,
            })
        })

        const users = await db.$transaction(transaction)

        if (users.length > 0) {
            for (let i = 0; i < users.length; i += 10) {
                const batch = users.slice(i, i + 10)

                await Promise.all(
                    batch.map(async (user) => {
                        TmsHelper.processUserForTmsLead(user.id, true)
                        logHistory({
                            entity_id: user.id?.toString(),
                            ...EventList[EventType.USER_CREATED],
                            creator_type: 'ADMIN',
                            new_values: data,
                            user_id: user.id,
                            org_id: user.organization_id,
                            creator_id: admin.id,
                            meta: {
                                ip_addr: ctx?.ipAddr || '',
                                user_agent: ctx?.userAgent || '',
                                user_id: user.id,
                                user_name: user.name,
                                user_org_id: user.organization_id,
                                creator_name: admin.name,
                                creator_id: admin.id,
                                creator_org_id: admin.active_org_id || -1,
                            },
                        })
                    })
                )
            }
        }

        return {
            __typename: 'BulkAdditionUserAdded',
            message: 'All users added successfully',
            totalAddedUsers: users.length,
        }
    } catch (error) {
        throw new GraphQLError(error)
    }
}

const getUserTimeline = async (
    _: any,
    args: { user_id: number },
    ctx: Context
) => {
    try {
        const admin = validateAdmin(ctx)

        const timeline = await db.user_logs.findMany({
            where: {
                user_id: args?.user_id,
                user: {
                    organization_id: admin.active_org_id || -1,
                },
            },
            include: {
                admin: {
                    select: {
                        name: true,
                    },
                },
                user: {
                    select: {
                        name: true,
                    },
                },
            },
            orderBy: {
                created_at: 'asc',
            },
        })

        return timeline
    } catch (error) {
        throw new GraphQLError(error)
    }
}

const getAllUsersCount = async (_: any, __: any, ctx: Context) => {
    try {
        const admin = validateAdmin(ctx)

        const userCount = await db.user.count({
            where: {
                organization_id: admin.active_org_id || -1,
            },
        })
        return userCount
    } catch (error) {
        throw new GraphQLError(error)
    }
}

const generateTokenFrAdmin = async (
    _: unknown,
    args: { id: number },
    ctx: Context
) => {
    const admin = validateAdmin(ctx)

    if (!admin) {
        throw new GraphQLError('user not valid')
    }
    const querieduser = await db.user.findFirst({
        where: {
            id: args.id,
        },
    })
    const token = generateJwt(
        {
            phone: querieduser?.phone,
            name: querieduser?.name,
            issuer: 'rojgaar-backend',
        },
        '365d'
    )
    return {
        result: true,
        token,
        message: 'Signed In successfully',
    }
}

const updateUserMeta = async (_: any, args: any, ctx: Context) => {
    try {
        const { user } = ctx

        const { data } = args

        const userData = await db.user.findFirst({
            where: {
                id: user.id,
            },
        })

        const meta = {
            ...(userData?.meta as any),
            ...data,
        }

        await db.user.update({
            where: {
                id: user?.id,
            },
            data: {
                tms_lead_is_updated: false,
                meta: meta,
            },
        })
        logHistory({
            entity_id: user.id?.toString(),
            ...EventList[EventType.USER_UPDATED],
            creator_type: ctx.admin ? 'ADMIN' : 'USER',
            new_values: {
                tms_lead_is_updated: false,
                meta: meta,
            },
            user_id: user.id || -1,
            org_id: user.organization_id || -1,
            creator_id: ctx.admin ? ctx.admin?.id : user.id,
            meta: {
                ip_addr: ctx?.ipAddr || '',
                user_agent: ctx?.userAgent || '',
                user_id: user.id || -1,
                user_name: user.name || '',
                user_org_id: user.organization_id || -1,
                creator_id: ctx.admin ? ctx.admin?.id : user.id,
                creator_name: ctx.admin ? ctx.admin?.name : user.name,
                creator_org_id: ctx.admin
                    ? ctx.admin?.active_org_id
                    : user.organization_id,
            },
        })
    } catch (error) {
        throw new GraphQLError(error)
    }
}

interface ReferralType {
    data: {
        id: number
        referral_code: string
    }
}

const referralCodeValidate = async (_: any, args: ReferralType) => {
    try {
        const { id, referral_code } = args.data
        if (!referral_code) {
            throw new GraphQLError('Referral code is missing')
        }
        const user = await db.user.findFirst({
            where: {
                id: id,
            },
            select: {
                phone: true,
                name: true,
                updated_at: true,
                onboarding_stage: true,
                organization_id: true,
                meta: true,
            },
        })

        if (!user) {
            throw new GraphQLError('User not found')
        }

        await validateReferralCode(referral_code)
        await createUserReferral(id, referral_code)
        await awardReferralPoints(user as user)
        return {
            validated: true,
            message: 'Referral code successfully validated',
        }
    } catch (error) {
        return {
            validated: false,
            message: error.message,
        }
    }
}

const updateUserPreferredLanguage = async (
    _: any,
    args: { data: { preferred_language: preferred_language } },
    ctx: Context
) => {
    try {
        await db.user.update({
            where: {
                id: ctx.user?.id,
            },
            data: {
                preferred_language: args.data.preferred_language,
            },
        })
        logHistory({
            entity_id: ctx.user?.id?.toString(),
            ...EventList[EventType.USER_UPDATED],
            creator_type: ctx.admin ? 'ADMIN' : 'USER',
            new_values: {
                preferred_language: args.data.preferred_language,
            },
            user_id: ctx.user?.id || -1,
            org_id: ctx.user?.organization_id || -1,
            creator_id: ctx.admin ? ctx.admin?.id : ctx.user?.id,
            meta: {
                ip_addr: ctx?.ipAddr || '',
                user_agent: ctx?.userAgent || '',
                user_id: ctx.user?.id || -1,
                user_name: ctx.user?.name || '',
                user_org_id: ctx.user?.organization_id || -1,
                creator_id: ctx.admin ? ctx.admin?.id : ctx.user?.id,
                creator_name: ctx.admin ? ctx.admin?.name : ctx.user?.name,
                creator_org_id: ctx.admin
                    ? ctx.admin?.active_org_id
                    : ctx.user?.organization_id,
            },
        })
        return true
    } catch (error) {
        throw new GraphQLError(error)
    }
}

const searchUser = async (
    _: any,
    args: { search: string; user_ids: number[]; filter: { user_type: string } },
    ctx: Context
) => {
    try {
        validateAdmin(ctx)

        const user_where: Prisma.userWhereInput = {
            OR: [
                {
                    name: {
                        contains: args.search ? args.search : '',
                        mode: 'insensitive',
                    },
                },
                {
                    email: {
                        contains: args.search ? args.search : '',
                        mode: 'insensitive',
                    },
                },
                {
                    phone: {
                        contains: args.search ? args.search : '',
                    },
                },
                {
                    remark: {
                        contains: args.search ? args.search : '',
                        mode: 'insensitive',
                    },
                },
            ],
            id: {
                notIn: args?.user_ids,
            },
        }
        if (
            args?.filter?.user_type &&
            args?.filter?.user_type !== 'TECHNICIAN'
        ) {
            user_where.user_type = args?.filter?.user_type
        } else {
            user_where.user_type = 'TECHNICIAN'
        }
        if (!args?.search) {
            return []
        }

        const users = await db.user.findMany({
            where: user_where,
            include: {
                manual_assignments: {
                    select: {
                        assignment_config_id: true,
                    },
                },
                location: {
                    select: {
                        city: true,
                    },
                },
            },
        })

        return users
    } catch (error) {
        throw new GraphQLError(error)
    }
}

const checkTransferToTms: UserResolvers['transfer_to_tms_done'] = async (
    user
) => {
    try {
        const onboarding_data = await db.user_onboarding_data.findFirst({
            where: {
                user_id: user.id || -1,
            },
            select: {
                user_created_in_tms: true,
            },
        })

        if (!onboarding_data) {
            return false
        }

        return onboarding_data?.user_created_in_tms || false
    } catch (error) {
        throw new GraphQLError(
            'There was some error during checking transfer to tms status'
        )
    }
}
const findAllDuplicateUsers = async (
    _: any,
    args: { user_phone_numbers: [string] },
    ctx: Context
) => {
    try {
        const admin = validateAdmin(ctx)

        if (args.user_phone_numbers.length < 1) {
            throw new GraphQLError('Users phone number is required')
        }
        const users = await db.user.findMany({
            where: {
                organization_id: admin.active_org_id || -1,
                phone: {
                    in: [...args.user_phone_numbers],
                },
            },
            select: {
                phone: true,
            },
        })
        return users
    } catch (error) {
        throw new GraphQLError(error)
    }
}

const userDesignation = async (_: any, __: any, ctx: Context) => {
    try {
        const { user } = ctx

        const designation = await db.user.findFirst({
            where: {
                id: user?.id || -1,
            },
            include: {
                designation: {
                    select: {
                        id: true,
                        name: true,
                        level: true,
                        is_active: true,
                        is_deleted: true,
                        created_at: true,
                        updated_at: true,
                        designation_expertise_map: {
                            select: {
                                id: false,
                                designation_id: false,
                                expertise_id: false,
                                expertise: {
                                    select: {
                                        id: true,
                                        name: true,
                                        icon: true,
                                    },
                                },
                            },
                        },
                        admin: {
                            select: {
                                id: true,
                                name: true,
                                type: true,
                            },
                        },
                    },
                },
            },
        })

        return designation
    } catch (error) {
        throw new GraphQLError(error)
    }
}

/**
 * Switch a user from Technician to Service Provider
 * @param _ root
 * @param args Arguments containing user_id and service provider details
 * @param ctx Context
 * @returns Result of the switch operation
 */
/**
 * Switch a user from Service Provider to Technician
 * @param _ root
 * @param args Arguments containing user_id and technician details
 * @param ctx Context
 * @returns Result of the switch operation
 */
const switchSPToTechnician = async (
    _: any,
    args: {
        data: {
            name: string
            user_id: number
            skills: number[]
            city?: string
            email?: string
            source?: string
            poc?: string
            phone?: string
            transfer_onboarding_status?: boolean
        }
    },
    ctx: Context
) => {
    try {
        const admin = validateAdmin(ctx)
        const { user_id, skills, city, email, source, poc, name, phone } =
            args.data

        // Check if user exists and is a service provider
        const user = await db.user.findFirst({
            where: {
                id: user_id,
            },
            include: {
                organization: true,
                user_expertise_map: true,
                location: true,
            },
        })

        if (!user) {
            throw new GraphQLError('User not found')
        }

        if (user.user_type !== UserRole.ServiceProvider) {
            throw new GraphQLError('User is not a service provider')
        }

        // Check if user has already been transferred to TMS
        // We'll check this by querying the user's onboarding stage
        const userTransferStatus = await db.user.findFirst({
            where: {
                id: user_id,
            },
        })

        if (
            userTransferStatus?.onboarding_stage ===
            OnboardingStage.TRANSFERRED_TO_TMS
        ) {
            throw new GraphQLError(
                'Cannot switch user that has been transferred to TMS'
            )
        }

        // Check if user has already switched 3 times
        const userMeta =
            typeof user.meta === 'string'
                ? JSON.parse(user.meta)
                : user.meta || {}
        const switchCount = userMeta.sp_to_technician_switch_count || 0

        // if (switchCount >= 3) {
        //     throw new GraphQLError(
        //         'User has already switched 3 times from service provider to technician'
        //     )
        // }

        // Store the current organization ID to delete it later
        const currentOrgId = user.organization_id

        // Check if user is organization owner and handle new owner assignment
        const isOrgOwner = await db.organization.findFirst({
            where: {
                org_owner_id: user_id,
            },
        })

        if (isOrgOwner) {
            // Get the current user's onboarding data to transfer to the new owner

            // No new owner provided, we'll keep the organization but remove the owner
            await db.organization.update({
                where: {
                    id: Number(currentOrgId),
                },
                data: {
                    org_owner_id: null,
                },
            })
        }

        // Reset the original user's onboarding status
        await db.user.update({
            where: {
                id: user_id,
            },
            data: {
                name: name,
                phone: phone,
                onboarded: false,
                onboarding_stage: OnboardingStage.NOT_STARTED,
                onboarding_stage_timestamps: [],
            },
        })

        // Update user expertise if needed
        if (skills && skills.length > 0) {
            // Delete existing expertise mappings
            await db.user_expertise_map.deleteMany({
                where: {
                    user_id: user_id,
                },
            })

            // Get existing expertise mappings
            const existingExpertiseMappings =
                await db.user_expertise_map.findMany({
                    where: {
                        user_id: user_id,
                    },
                })

            // Log the existing mappings before making changes
            if (existingExpertiseMappings.length > 0) {
                // Log the action in history
                logHistory({
                    entity_id: user_id.toString(),
                    ...EventList[EventType.USER_UPDATED],
                    creator_type: 'ADMIN',
                    new_values: {
                        action: 'Marked expertise mappings as inactive during SP to Technician switch',
                        expertise_mappings: existingExpertiseMappings.map(
                            (mapping) => ({
                                id: mapping.id,
                                expertise_id: mapping.expertise_id,
                                is_active: false,
                                deactivation_date: new Date().toISOString(),
                                deactivation_reason:
                                    'User switched from SP to Technician',
                            })
                        ),
                    },
                    user_id: user_id,
                    org_id: user.organization_id,
                    creator_id: admin.id,
                    meta: {
                        ip_addr: ctx?.ipAddr || '',
                        user_agent: ctx?.userAgent || '',
                        user_id: user_id,
                        user_name: user.name,
                        user_org_id: user.organization_id,
                        creator_id: admin.id,
                        creator_name: admin.name,
                        creator_org_id: admin.active_org_id,
                        action: 'Marked expertise mappings as inactive during SP to Technician switch',
                    },
                })
            }

            // Create new expertise mappings
            await db.user_expertise_map.createMany({
                data: skills.map((skill_id) => ({
                    user_id: user_id,
                    expertise_id: skill_id,
                })),
                skipDuplicates: true,
            })
        }

        // Create or update location with city if provided
        let locationId = user.location_id
        if (city && city.trim() !== '') {
            if (locationId) {
                // Update existing location
                await db.location.update({
                    where: {
                        id: locationId,
                    },
                    data: {
                        city: city,
                        country: 'India',
                    },
                })
            } else {
                // Create new location
                const newLocation = await db.location.create({
                    data: {
                        city: city,
                        country: 'India',
                        pincode: '',
                        state: '',
                        work_address: '',
                        landmark: '',
                    },
                })
                locationId = newLocation.id
            }
        }

        // Update user type and move to Wify organization (MASTER_ORG_ID)
        const updatedUser = await db.user.update({
            where: {
                id: user_id,
            },
            data: {
                user_type: UserRole.Technician,
                organization_id: MASTER_ORG_ID,
                email: email || user.email,
                poc: poc || user.poc,
                source: source || user.source,
                location_id: locationId,
                created_at: new Date(),
                meta: {
                    ...userMeta,
                    last_location: '/home',
                    sp_to_technician_switch_count: switchCount + 1,
                    last_switch_date: new Date().toISOString(),
                    last_switch_by_admin_id: admin.id,
                    last_switch_by_admin_name: admin.name,
                },
            },
        })

        try {
            // Mark organization as inactive instead of deleting it
            await db.organization.update({
                where: {
                    id: Number(currentOrgId),
                },
                data: {
                    is_active: false,
                    meta: {
                        deactivated_by_admin_id: admin.id,
                        deactivated_by_admin_name: admin.name,
                        deactivation_date: new Date().toISOString(),
                        deactivation_reason:
                            'User switched from SP to Technician',
                        previous_owner_id: user_id,
                        previous_owner_name: user.name,
                    },
                },
            })

            // Log the organization deactivation
            logHistory({
                entity_id: currentOrgId.toString(),
                ...EventList[EventType.ORGANIZATION_UPDATED],
                creator_type: 'ADMIN',
                new_values: {
                    is_active: false,
                    deactivation_reason: 'User switched from SP to Technician',
                },
                user_id: user_id,
                org_id: currentOrgId,
                creator_id: admin.id,
                meta: {
                    ip_addr: ctx?.ipAddr || '',
                    user_agent: ctx?.userAgent || '',
                    user_id: user_id,
                    user_name: user.name,
                    user_org_id: currentOrgId,
                    creator_id: admin.id,
                    creator_name: admin.name,
                    creator_org_id: admin.active_org_id,
                    action: 'Organization marked inactive during SP to Technician switch',
                },
            })
        } catch (error) {
            console.error('Failed to update organization:', error)
            // Continue even if organization update fails
        }

        // Log the switch in history
        logHistory({
            entity_id: user_id.toString(),
            ...EventList[EventType.USER_TYPE_SWITCHED],
            creator_type: 'ADMIN',
            new_values: {
                user_type: UserRole.Technician,
                organization_id: MASTER_ORG_ID,
                email: email || user.email,
                poc: poc || user.poc,
                source: source || user.source,
                location_id: locationId,
                meta: {
                    ...userMeta,
                    sp_to_technician_switch_count: switchCount + 1,
                    last_switch_date: new Date().toISOString(),
                    last_switch_by_admin_id: admin.id,
                    last_switch_by_admin_name: admin.name,
                },
            },
            user_id: user_id,
            org_id: MASTER_ORG_ID,
            creator_id: admin.id,
            meta: {
                ip_addr: ctx?.ipAddr || '',
                user_agent: ctx?.userAgent || '',
                user_id: user_id,
                user_name: user.name,
                user_org_id: MASTER_ORG_ID,
                creator_id: admin.id,
                creator_name: admin.name,
                creator_org_id: admin.active_org_id,
                switch_type: 'SP_TO_TECHNICIAN',
                city: city || user.location?.city || '',
            },
        })

        return {
            result: true,
            message:
                'User successfully switched from service provider to technician',
            user: updatedUser,
        }
    } catch (error) {
        throw new GraphQLError(
            error.message ||
                'Failed to switch user from service provider to technician'
        )
    }
}

const switchTechnicianToSP = async (
    _: any,
    args: {
        data: {
            user_id: number
            company_name: string
            company_poc_name: string
            owner_name: string
            skills: number[]
            city: string
            email: string
            service_type_id: any[]
        }
    },
    ctx: Context
) => {
    try {
        const admin = validateAdmin(ctx)
        const {
            user_id,
            company_name,
            company_poc_name,
            owner_name,
            skills,
            city,
            email,
            service_type_id,
        } = args.data

        // Check if user exists and is a technician
        const user = await db.user.findFirst({
            where: {
                id: user_id,
            },
            include: {
                organization: true,
                user_expertise_map: true,
            },
        })

        if (!user) {
            throw new GraphQLError('User not found')
        }

        if (user.user_type !== UserRole.Technician) {
            throw new GraphQLError('User is not a technician')
        }

        // Check if user has already switched 3 times
        const userMeta =
            typeof user.meta === 'string'
                ? JSON.parse(user.meta)
                : user.meta || {}
        const switchCount = userMeta.technician_to_sp_switch_count || 0

        // if (switchCount >= 3) {
        //     throw new GraphQLError(
        //         'User has already switched 3 times from technician to service provider'
        //     )
        // }

        // Create a new organization for the service provider
        const newOrg = await db.organization.create({
            data: {
                name: company_name,
                org_owner_id: user_id,
                meta: {
                    user_onboarded: user.transfer_to_tms_status,
                    sp_onboarding_date: user.transfer_to_tms_status
                        ? new Date().toISOString()
                        : '',
                    primary_city: city || '',
                },
                parent_id: Number(MAIN_ORG_DATA.ORG_ID),
            },
        })

        // Create organization service type map

        await db.organization_service_type_map.createMany({
            data: service_type_id.map((service_id) => ({
                organization_id: newOrg.id,
                service_type_id: service_id || '-1',
            })),
        })

        // Create organization expertise maps for the skills
        if (skills && skills.length > 0) {
            await db.organization_expertise_map.createMany({
                data: skills.map((skill_id) => ({
                    org_id: newOrg.id,
                    expertise_id: skill_id,
                    team_member_count: 0,
                })),
            })
        }

        // Update user's expertise if needed
        if (skills && skills.length > 0) {
            // Delete existing expertise mappings
            await db.user_expertise_map.deleteMany({
                where: {
                    user_id: user_id,
                },
            })

            // Create new expertise mappings
            await db.user_expertise_map.createMany({
                data: skills.map((skill_id) => ({
                    user_id: user_id,
                    expertise_id: skill_id,
                })),
            })
        }

        // Create or update location with city
        let locationId = user.location_id
        if (city) {
            if (locationId) {
                // Update existing location
                await db.location.update({
                    where: {
                        id: locationId,
                    },
                    data: {
                        city: city,
                        country: 'India',
                    },
                })
            } else {
                // Create new location
                const newLocation = await db.location.create({
                    data: {
                        city: city,
                        country: 'India',
                        pincode: '',
                        state: '',
                        work_address: '',
                        landmark: '',
                    },
                })
                locationId = newLocation.id
            }
        }

        // Update user type and organization
        const updatedUser = await db.user.update({
            where: {
                id: user_id,
            },
            data: {
                user_type: UserRole.ServiceProvider,
                organization_id: newOrg.id,
                email: email,
                poc: company_poc_name,
                name: owner_name,
                location_id: locationId,
                created_at: new Date(),
                onboarded: user.transfer_to_tms_status,
                ...(user.transfer_to_tms_status
                    ? { onboarding_stage: OnboardingStage.ONBOARD }
                    : {}),
                meta: {
                    ...userMeta,
                    ...(city ? { primary_city: city } : {}),
                    last_location: user.transfer_to_tms_status
                        ? '/profile'
                        : '/sp/home',
                    technician_to_sp_switch_count: switchCount + 1,
                    last_switch_date: new Date().toISOString(),
                    last_switch_by_admin_id: admin.id,
                    last_switch_by_admin_name: admin.name,

                    user_onboarded: user.transfer_to_tms_status,
                    sp_onboarding_date: user.transfer_to_tms_status
                        ? new Date().toISOString()
                        : '',
                },
            },
        })

        // Log the switch in history
        logHistory({
            entity_id: user_id.toString(),
            ...EventList[EventType.USER_UPDATED],
            creator_type: 'ADMIN',
            new_values: {
                user_type: UserRole.ServiceProvider,
                organization_id: newOrg.id,
                email: email,
                poc: company_poc_name || user.name,
                location_id: locationId,
                meta: {
                    ...userMeta,
                    technician_to_sp_switch_count: switchCount + 1,
                    last_switch_date: new Date().toISOString(),
                    last_switch_by_admin_id: admin.id,
                    last_switch_by_admin_name: admin.name,
                },
            },
            user_id: user_id,
            org_id: newOrg.id,
            creator_id: admin.id,
            meta: {
                ip_addr: ctx?.ipAddr || '',
                user_agent: ctx?.userAgent || '',
                user_id: user_id,
                user_name: user.name,
                user_org_id: newOrg.id,
                creator_id: admin.id,
                creator_name: admin.name,
                creator_org_id: admin.active_org_id,
                switch_type: 'TECHNICIAN_TO_SP',
                company_name: company_name,
                service_type_id: service_type_id,
                city: city,
            },
        })
        if (company_poc_name) {
            const onboarding_data_count = await db.user_onboarding_data.count({
                where: {
                    user_id: user.id,
                },
            })
            const userOnboardingData = await db.user_onboarding_data.upsert({
                where: {
                    user_id: user.id,
                },
                create: {
                    user_id: user.id,
                    poc: company_poc_name || undefined,
                    source: undefined,
                },
                update: {
                    poc: company_poc_name || undefined,
                },
            })
            logHistory({
                entity_id: userOnboardingData?.id?.toString(),
                ...(onboarding_data_count === 0
                    ? EventList[EventType.USER_ONBOARDING_CREATED]
                    : EventList[EventType.USER_ONBOARDING_UPDATED]),
                creator_type: 'ADMIN' as const,
                new_values: {
                    user_id: user.id,
                    poc: company_poc_name || undefined,
                },
                user_id: user.id || -1,
                org_id: user.organization_id || -1,
                creator_id: admin.id,
                meta: {
                    ip_addr: ctx?.ipAddr || '',
                    user_agent: ctx?.userAgent || '',
                    user_id: user.id || -1,
                    user_name: user.name || '',
                    user_org_id: user.organization_id || -1,
                    creator_id: admin.id,
                    creator_name: admin.name,
                    creator_org_id: admin.active_org_id,
                },
            })
        }
        return {
            result: true,
            message:
                'User successfully switched from technician to service provider',
            user: updatedUser,
        }
    } catch (error) {
        throw new GraphQLError(
            error.message ||
                'Failed to switch user from technician to service provider'
        )
    }
}

//create a functions
const resolveUserDesignations = async (root: { id: number }) => {
    try {
        const user = await db.user.findFirst({
            where: {
                id: root.id,
            },
            select: {
                id: true,
                name: true,
                user_expertise_map: true,
            },
        })

        //access expertise ids of the user
        const user_expertise_ids = user?.user_expertise_map?.map(
            (expertise) => expertise?.expertise_id
        )

        const designation_select: Prisma.designationSelect = {
            id: true,
            name: true,
            level: true,
            is_active: true,
            is_deleted: true,
            created_at: true,
            updated_at: true,
            designation_expertise_map: {
                select: {
                    id: false,
                    designation_id: false,
                    expertise_id: false,
                    expertise: {
                        select: {
                            id: true,
                            name: true,
                            icon: true,
                        },
                    },
                },
            },
            admin: {
                select: {
                    id: true,
                    name: true,
                    type: true,
                },
            },
            user: {
                select: {
                    id: true,
                    name: true,
                },
            },
        }

        const designations = await db.designation.findMany({
            where: {
                is_active: true,
                is_deleted: false,
                designation_expertise_map: {
                    some: {
                        expertise: { id: { in: user_expertise_ids } }, // Include all provided expertise
                    },
                },
            },
            select: designation_select,
            orderBy: {
                created_at: 'asc',
            },
        })

        return designations
    } catch (error) {
        throw new GraphQLError(
            'There was some error in fetching org info for user'
        )
    }
}

const userResolver = {
    Query: {
        userDetail: singleUserResolver,
        userDetailsForAdmin,
        getUsersForAdmin,
        getRejectandBanUser,
        getUserTimeline,
        getAllUsersCount,
        generateTokenFrAdmin,
        searchUser,
        findAllDuplicateUsers,
        userDesignation,
    },
    Mutation: {
        UpdateUser: updateUserResolver,
        DeleteUser: deleteUserResolver,
        verifyPhoneNumber,
        updateUserAdmin,
        createUserBulk,
        updateRejectandBanUser,
        updateUserMeta,
        referralCodeValidate,
        updateUserPreferredLanguage,
        switchTechnicianToSP,
        switchSPToTechnician,
    },
    User: {
        organization: resolveUsersOrg,
        roles: resolveUserRole,
        expertise: resolveExpertise,
        location: resolveUserLocation,
        transfer_to_tms_done: checkTransferToTms,
        supported_designations: resolveUserDesignations,
    },
    Subscription: {
        bellNotification: {
            subscribe: (_: any, args: { user_id: number }) => {
                return pubsub.asyncIterator(
                    `BELL_NOTIFICATION_${args?.user_id}`
                )
            },
        },
    },
}

export default userResolver
