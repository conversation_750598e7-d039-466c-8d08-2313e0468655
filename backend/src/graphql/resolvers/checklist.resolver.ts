import { GraphQLError } from 'graphql'
import DataProvider from '../../data'
import { logHistory } from '../../services/history.service'
import { validateAdmin, validateUser } from '../../utils/authValidators'
import { Context } from '../../utils/types'

const db = DataProvider.getDbInstance()

interface UserChecklistInput {
    user_id: number
    checklist_id?: string
    hiring_completed?: boolean
    technician_completed?: boolean
    meta?: any
    language?: string
}

export const autoCheckUserChecklistItem = async (
    userId: number,
    checklistName: 'Interview' | 'Training' | 'App Assessment', //only these can be autochecked
    completionType: 'hiring' | 'technician' = 'hiring',
    userType?: 'admin' | 'user',
    editorId?: number | null,
    meta?: any
): Promise<void> => {
    try {
        const checklist = await db.checklist.findFirst({
            where: {
                name: checklistName,
            },
        })

        if (!checklist) {
            console.warn(`${checklistName} checklist not found or inactive`)
            return
        }

        const existingChecklistData = await db.user_checklist_data.findUnique({
            where: {
                user_id_checklist_id: {
                    user_id: userId,
                    checklist_id: checklist.id,
                },
            },
        })

        const completionField =
            completionType === 'hiring'
                ? 'hiring_completed'
                : 'technician_completed'
        const completionTimeField =
            completionType === 'hiring'
                ? 'hiring_completed_at'
                : 'technician_completed_at'

        if (existingChecklistData?.[completionField]) {
            return
        }

        const currentTime = new Date()
        const metaData = {
            last_updated_at: currentTime.toString(),
            ...meta,
        }
        const commonData = {
            ...(editorId ? { created_by: editorId } : {}),
            [completionField]: true,
            [completionTimeField]: currentTime,
            updated_at: currentTime,
        }

        const updateData = {
            ...commonData,
            meta: {
                user_type_updated: userType,
                ...((existingChecklistData?.meta as object) || {}),
                ...metaData,
            },
        }

        const createData = {
            ...commonData,
            created_at: currentTime,
            user_id: userId,
            checklist_id: checklist.id,
            meta: {
                user_type_created: userType,
                ...metaData,
            },
        }

        await db.user_checklist_data.upsert({
            where: {
                user_id_checklist_id: {
                    user_id: userId,
                    checklist_id: checklist.id,
                },
            },
            update: updateData,
            create: createData,
        })
    } catch (error) {
        console.error(
            `Error auto-checking ${checklistName} ${completionType} feedback:`,
            error
        )
    }
}

/**
 * Get all active checklists
 */
const getAllChecklists = async (_: any, args: any, ctx: Context) => {
    try {
        if (ctx.admin) {
            validateAdmin(ctx)
        } else {
            validateUser(ctx)
        }
        const { user_type } = args
        const checklists = await db.checklist.findMany({
            where:
                user_type === 'SERVICE_PROVIDER'
                    ? { is_active_sp: true }
                    : undefined,
            orderBy: {
                created_at: 'asc',
            },
        })
        return {
            result: true,
            message: 'Checklists fetched successfully',
            data: checklists,
        }
    } catch (error) {
        console.error('Error fetching checklists:', error)
        throw new GraphQLError('Failed to fetch checklists')
    }
}

/**
 * Get checklist data for a specific user
 */
const getUserChecklistData = async (
    _: any,
    args: {
        user_id?: number
        user_type?: 'SERVICE_PROVIDER' | 'TECHNICIAN' | null
    },
    ctx: Context
) => {
    let user

    if (ctx.admin) {
        validateAdmin(ctx)
    } else {
        user = validateUser(ctx)
    }
    try {
        const userChecklistData = await db.checklist.findMany({
            where:
                args?.user_type === 'SERVICE_PROVIDER'
                    ? { is_active_sp: true }
                    : undefined,
            select: {
                id: true,
                name: true,
                user_checklist_data: {
                    where: {
                        user_id: user
                            ? user.id
                            : args.user_id
                            ? args?.user_id
                            : undefined,
                    },
                    select: {
                        id: true,
                        hiring_completed: true,
                        hiring_completed_at: true,
                        technician_completed: true,
                        technician_completed_at: true,
                        created_at: true,
                        updated_at: true,
                        created_by: true,
                        edited_by: true,
                        creator: {
                            select: {
                                id: true,
                                name: true,
                            },
                        },
                        editor: {
                            select: {
                                id: true,
                                name: true,
                            },
                        },
                        user: {
                            select: {
                                id: true,
                                name: true,
                            },
                        },
                        meta: true,
                    },
                },
            },
            orderBy: {
                created_at: 'desc',
            },
        })
        return {
            result: true,
            message: 'User checklist data fetched successfully',
            data: userChecklistData,
        }
    } catch (error) {
        console.error('Error fetching user checklist data:', error)
        throw new GraphQLError('Failed to fetch user checklist data')
    }
}

/**
 * Get a single checklist item for a specific user
 */
const getSingleUserChecklistData = async (
    _: any,
    args: { user_id: number; checklist_id: string },
    ctx: Context
) => {
    try {
        validateAdmin(ctx)
        const { user_id, checklist_id } = args
        const userChecklistData = await db.user_checklist_data.findUnique({
            where: {
                user_id_checklist_id: {
                    user_id: user_id,
                    checklist_id: checklist_id,
                },
            },
            include: {
                checklist: true,
                user: true,
            },
        })

        if (!userChecklistData) {
            return {
                result: false,
                message: 'User checklist data not found',
                data: null,
            }
        }

        return {
            result: true,
            message: 'User checklist data fetched successfully',
            data: userChecklistData,
        }
    } catch (error) {
        console.error('Error fetching single user checklist data:', error)
        throw new GraphQLError('Failed to fetch single user checklist data')
    }
}

/**
 * Update or create user checklist data
 */
const updateUserChecklistData = async (
    _: any,
    { data }: { data: UserChecklistInput | UserChecklistInput[] },
    ctx: Context
) => {
    try {
        let admin: any
        if (ctx.admin) {
            admin = validateAdmin(ctx)
        } else {
            validateUser(ctx)
        }
        const dataArray = Array.isArray(data) ? data : [data]
        const currentTime = new Date()
        const editorId = admin?.id

        const getPreviousRecord = async (
            user_id: number,
            checklist_id: string
        ) => {
            const previousRecord = await db.user_checklist_data.findUnique({
                where: {
                    user_id_checklist_id: {
                        user_id,
                        checklist_id,
                    },
                },
                select: {
                    meta: true,
                },
            })
            return previousRecord
        }
        // Process all items using Prisma transactions for atomicity
        await db.$transaction(async (tx) => {
            for (const item of dataArray) {
                const {
                    user_id,
                    checklist_id,
                    hiring_completed,
                    technician_completed,
                } = item

                if (!checklist_id) {
                    throw new GraphQLError('Checklist id not found')
                }
                const previousRecord = await getPreviousRecord(
                    user_id,
                    checklist_id
                )

                const metaData = {
                    user_type_updated: admin ? 'admin' : 'user',
                    last_updated_at: currentTime.toString(),
                }

                const updateData: any = {
                    updated_at: currentTime,
                    ...(editorId ? { edited_by: editorId } : {}),
                    meta:
                        previousRecord &&
                        typeof previousRecord.meta === 'object' &&
                        previousRecord.meta !== null
                            ? {
                                  ...JSON.parse(
                                      JSON.stringify(previousRecord.meta)
                                  ),
                                  ...metaData,
                              }
                            : metaData,
                }

                if (hiring_completed !== undefined) {
                    updateData.hiring_completed = hiring_completed
                    if (hiring_completed) {
                        updateData.hiring_completed_at = currentTime
                    }
                }

                if (technician_completed !== undefined) {
                    updateData.technician_completed = technician_completed
                    if (technician_completed) {
                        updateData.technician_completed_at = currentTime
                    }
                }

                // Use upsert to create or update in a single operation
                await tx.user_checklist_data.upsert({
                    where: {
                        user_id_checklist_id: {
                            user_id,
                            checklist_id,
                        },
                    },
                    update: updateData,
                    create: {
                        user_id,
                        checklist_id,
                        ...(editorId ? { created_by: editorId } : {}),
                        created_at: currentTime,
                        hiring_completed:
                            hiring_completed !== undefined
                                ? hiring_completed
                                : false,
                        hiring_completed_at: hiring_completed
                            ? currentTime
                            : null,
                        technician_completed:
                            technician_completed !== undefined
                                ? technician_completed
                                : false,
                        technician_completed_at: technician_completed
                            ? currentTime
                            : null,
                        meta: {
                            user_type_created: admin ? 'admin' : 'user',
                            last_updated_at: currentTime.toString(),
                        },
                    },
                    include: {
                        checklist: true,
                        user: true,
                    },
                })
            }
        })

        return {
            result: true,
            message: 'User checklist data updated successfully',
        }
    } catch (error) {
        console.error('Error updating user checklist data:', error)
        throw new GraphQLError('Failed to update user checklist data')
    }
}

/**
 * Update technician feedback data with new format
 */
const updateTechnicianFeedbackData = async (
    _: any,
    { data }: { data: UserChecklistInput },
    ctx: Context
) => {
    try {
        const { user_id, meta, language } = data
        const currentTime = new Date().toISOString()

        //check if user data exists
        const user = await db.user.findUnique({
            where: {
                id: user_id,
            },
            select: {
                transfer_to_tms_status: true,
            },
        })
        if (!user) {
            throw new GraphQLError('User not found')
        }
        if (!user.transfer_to_tms_status) {
            throw new GraphQLError('User has not been onboarded')
        }

        const checklistData = await db.checklist.findMany({
            select: {
                id: true,
                name: true,
            },
        })

        // Process all items using Prisma transactions for atomicity
        await db.$transaction(
            Object.entries(meta).map(([key, item]: [string, any]) => {
                console.log(key)
                const technician_completed =
                    item.label === 'Rating' ? true : item.value
                const checklist_name = item.label
                const checklist = checklistData.find(
                    (c) => c.name === checklist_name
                )
                const checklist_id = checklist?.id

                if (!checklist_id) {
                    throw new GraphQLError(
                        `Checklist id not found for ${checklist_name}`
                    )
                }

                // Prepare update data
                const updateData = {
                    updated_at: currentTime,
                    technician_completed: technician_completed,
                    technician_completed_at: currentTime,
                    meta: {
                        value: item.value,
                        label: item.label,
                        user_type_updated: 'user',
                        last_updated_at: currentTime,
                        language: language,
                    },
                }

                // Use upsert to create or update in a single operation
                const result = db.user_checklist_data.upsert({
                    where: {
                        user_id_checklist_id: {
                            user_id,
                            checklist_id,
                        },
                    },
                    update: updateData,
                    create: {
                        user_id,
                        checklist_id,
                        created_at: currentTime,
                        hiring_completed: false,
                        hiring_completed_at: null,
                        technician_completed: technician_completed,
                        technician_completed_at: currentTime,
                        meta: {
                            value: item.value,
                            label: item.label,
                            user_type_created: 'user',
                            last_updated_at: currentTime.toString(),
                            language: language,
                        },
                    },
                    include: {
                        checklist: true,
                        user: true,
                    },
                })

                logHistory({
                    entity_id: checklist_id,
                    creator_type: 'USER',
                    new_values: {
                        ...updateData,
                    },
                    event_title: 'Technician feedback updated',
                    user_id: user_id || -1,
                    creator_id: user_id,
                    meta: {
                        ip_addr: ctx?.ipAddr || '',
                        user_agent: ctx?.userAgent || '',
                        user_id: user_id || -1,
                        creator_id: user_id,
                    },
                })

                return result
            })
        )

        return {
            result: true,
            message: 'Technician feedback data updated successfully',
        }
    } catch (error) {
        console.error('Error updating technician feedback data:', error)
        throw new GraphQLError(error.message)
    }
}

/**
 * Get user checklist history information
 */
const getUserChecklistHistory = async (
    _: any,
    args: { user_id: number },
    ctx: Context
) => {
    try {
        if (ctx.admin) {
            validateAdmin(ctx)
        } else {
            validateUser(ctx)
        }

        const { user_id } = args

        // Get all checklist data for the user
        const userChecklistData = await db.user_checklist_data.findMany({
            where: {
                user_id: user_id,
            },
            orderBy: [
                {
                    created_at: 'asc', // First created entry will be first
                },
                {
                    updated_at: 'desc', // Last updated entry will be first
                },
            ],
            include: {
                creator: {
                    select: {
                        id: true,
                        name: true,
                    },
                },
                editor: {
                    select: {
                        id: true,
                        name: true,
                    },
                },
                user: {
                    select: {
                        id: true,
                        name: true,
                    },
                },
            },
        })

        if (!userChecklistData || userChecklistData.length === 0) {
            return {
                result: false,
                message: 'No checklist data found for this user',
                data: null,
            }
        }

        // Get first created entry
        const firstCreatedEntry = userChecklistData[0]

        // Get last updated entry
        const lastUpdatedEntry = [...userChecklistData].sort(
            (a, b) =>
                new Date(b.updated_at).getTime() -
                new Date(a.updated_at).getTime()
        )[0]

        // Determine creator type and name
        let creator = null
        if (
            firstCreatedEntry.meta &&
            typeof firstCreatedEntry.meta === 'object' &&
            'user_type_created' in firstCreatedEntry.meta
        ) {
            const creatorType = firstCreatedEntry.meta.user_type_created
            if (creatorType === 'admin' && firstCreatedEntry.creator) {
                creator = {
                    type: 'admin',
                    name: firstCreatedEntry.creator.name,
                    id: firstCreatedEntry.creator.id,
                }
            } else if (creatorType === 'user' && firstCreatedEntry.user) {
                creator = {
                    type: 'user',
                    name: 'System generated',
                    id: firstCreatedEntry.user.id,
                }
            }
        }

        // Determine last editor type and name
        let lastEditor = null
        if (
            lastUpdatedEntry.meta &&
            typeof lastUpdatedEntry.meta === 'object' &&
            'user_type_updated' in lastUpdatedEntry.meta
        ) {
            const editorType = lastUpdatedEntry.meta.user_type_updated

            if (editorType === 'admin' && lastUpdatedEntry.editor) {
                lastEditor = {
                    type: 'admin',
                    name: lastUpdatedEntry.editor.name,
                    id: lastUpdatedEntry.editor.id,
                }
            } else if (editorType === 'user' && lastUpdatedEntry.user) {
                lastEditor = {
                    type: 'user',
                    name: 'System generated',
                    id: lastUpdatedEntry.user.id,
                }
            }
        } else if (
            lastUpdatedEntry.meta &&
            typeof lastUpdatedEntry.meta === 'object' &&
            'user_type_created' in lastUpdatedEntry.meta
        ) {
            const creatorType = lastUpdatedEntry?.meta?.user_type_created

            if (creatorType === 'user' && lastUpdatedEntry.user) {
                lastEditor = {
                    type: 'user',
                    name: 'System generated',
                    id: lastUpdatedEntry.user.id,
                }
            } else {
                lastEditor = {
                    type: 'admin',
                    name: lastUpdatedEntry?.creator?.name,
                    id: lastUpdatedEntry?.creator?.id,
                }
            }
        }

        return {
            result: true,
            message: 'User checklist history fetched successfully',
            data: {
                created_at: firstCreatedEntry.created_at,
                updated_at: lastUpdatedEntry.updated_at,
                creator: creator,
                last_editor: lastEditor,
            },
        }
    } catch (error) {
        console.error('Error fetching user checklist history:', error)
        throw new GraphQLError('Failed to fetch user checklist history')
    }
}

const getFeedbackFormStatus = async (
    _: any,
    args: {
        user_id: number
    }
) => {
    try {
        const userChecklistData = await db.user_checklist_data.findFirst({
            where: {
                user_id: args?.user_id,
            },
            select: {
                technician_completed_at: true,
                meta: true,
            },

            orderBy: {
                created_at: 'desc',
            },
        })

        const submitted = !!userChecklistData?.technician_completed_at

        const user = await db.user.findFirst({
            where: {
                id: args?.user_id,
            },
            select: {
                id: true,
                transfer_to_tms_status: true,
            },
        })

        return {
            result: true,
            message: submitted
                ? 'User has already submitted feedback'
                : 'User has not submitted feedback',
            submitted,
            language: userChecklistData?.meta || { language: 'en' },
            onboarded: user?.transfer_to_tms_status || false,
            user_id: user?.id || undefined,
        }
    } catch (error) {
        console.error('Error fetching feedback form status:', error)
        throw new GraphQLError('Failed to fetch feedback form status')
    }
}

const checklistResolver = {
    Query: {
        getAllChecklists,
        getUserChecklistData,
        getSingleUserChecklistData,
        getUserChecklistHistory,
        getFeedbackFormStatus,
    },
    Mutation: {
        updateUserChecklistData,
        updateTechnicianFeedbackData,
    },
}

export default checklistResolver
