import { Prisma, user_onboarding_data } from '@prisma/client'
import { GraphQLError } from 'graphql'
import { pubsub } from '../../'
import DataProvider from '../../data'
import Queues from '../../queue'
import { logHistory } from '../../services/history.service'
import { FRONTEND_URL } from '../../utils/envVars'
import { EventList, EventType } from '../../utils/eventTypes'
import { shortenUrl } from '../../utils/utils'
import { Pagination, QueryResolvers } from '../__generated__/types'
import { validateAdmin } from './../../utils/authValidators'
import { CryptoUtility } from './../../utils/crypto'
import { Context } from './../../utils/types'
import { awardReferralPoints, createUserTimestamp } from './../../utils/user'
import { UserMetaType } from './fcm.resolver'
import { getUserIdsSortedByTTTDate } from '../../services/transfer.to.tms.service'

const db = DataProvider.getDbInstance()

/**
 *TTT means Transfer to TMS
 */

interface BankDataInput {
    bank_name: string
    ifsc: string
    account_no: string
    user_id: number
}

const getUserForTTT = async (
    _: any,
    args: {
        search: string
        user_id: number
        filter: {
            transfer_to_tms_status: boolean
            startDate?: string
            endDate?: string
        }
        pagination: Pagination
    },
    ctx: Context
) => {
    try {
        const admin = validateAdmin(ctx) //validate admin
        if (!admin.active_org_id) {
            throw new GraphQLError('No active organization id present')
        }

        return await db.$transaction(async (tx) => {
            const userIds = args?.user_id
                ? []
                : await getUserIdsSortedByTTTDate(tx, args)

            const where: Prisma.userWhereInput | undefined = {
                ...(args.user_id ? { id: args.user_id } : {}),
                ...(args.filter &&
                args.filter.transfer_to_tms_status !== undefined
                    ? {
                          transfer_to_tms_status:
                              args.filter.transfer_to_tms_status,
                      }
                    : {}),
            }

            if (args.search) {
                where.AND = {
                    OR: [
                        {
                            name: {
                                contains: `%${args.search}%`,
                                mode: 'insensitive',
                            },
                        },
                        {
                            email: {
                                contains: `%${args.search}%`,
                                mode: 'insensitive',
                            },
                        },
                        {
                            phone: {
                                contains: `%${args.search}%`,
                                mode: 'insensitive',
                            },
                        },
                    ],
                }
            }

            if (args.filter?.startDate && args.filter?.endDate) {
                const endDate = new Date(args.filter.endDate)
                endDate.setDate(endDate.getDate() + 1)
                where.user_onboarding_data = {
                    some: {
                        meta: {
                            path: ['transfer_to_tms_date'],
                            gte: new Date(args.filter.startDate).toISOString(),
                            lt: endDate.toISOString(),
                        },
                    },
                }
            }

            const user = await tx.user.findMany({
                where: {
                    id: {
                        in: args.user_id ? [...userIds, args.user_id] : userIds,
                    },
                },
                include: {
                    user_onboarding_data: true,
                    bank_details: {
                        select: {
                            encrypted: true,
                        },
                    },
                    designation: {
                        select: {
                            id: true,
                            name: true,
                            level: true,
                        },
                    },
                    location: true,
                },
            })

            if (args?.filter?.transfer_to_tms_status) {
                user.sort((a, b) => {
                    const getTransferToTmsDate = (meta: any) => {
                        if (
                            meta &&
                            typeof meta === 'object' &&
                            'transfer_to_tms_date' in meta
                        ) {
                            return meta.transfer_to_tms_date
                        }
                        return 0
                    }
                    const dateA = new Date(
                        getTransferToTmsDate(a.user_onboarding_data[0]?.meta)
                    ).getTime()
                    const dateB = new Date(
                        getTransferToTmsDate(b.user_onboarding_data[0]?.meta)
                    ).getTime()
                    return dateB - dateA
                })
            } else {
                user.sort((a, b) => {
                    const dateA = new Date(a.created_at).getTime()
                    const dateB = new Date(b.created_at).getTime()
                    return dateB - dateA
                })
            }

            //Decryption of User Bank Details
            user.forEach((data: any) => {
                if (
                    data.bank_details &&
                    data.bank_details.length > 0 &&
                    data.bank_details[0]?.encrypted
                ) {
                    try {
                        const bank_data = JSON.parse(
                            CryptoUtility.decrypt(
                                data.bank_details[0].encrypted
                            )
                        )
                        // Replace the array with the decrypted bank data object
                        data.bank_details = bank_data
                    } catch (error) {
                        console.error(
                            'Error decrypting bank details for user:',
                            data.id,
                            error
                        )
                        data.bank_details = null
                    }
                } else {
                    data.bank_details = null
                }
            })

            const total_count = await tx.user.count({
                where,
            })

            return { data: user, total_count: total_count }
        })
    } catch (error) {
        throw new GraphQLError(error)
    }
}
const createOrUpdateUsersOnboardingData = async (
    _: any,
    args: { userOnboardingData: user_onboarding_data },
    ctx: Context
) => {
    try {
        const admin = validateAdmin(ctx) //validate admin
        if (!admin.active_org_id) {
            throw new GraphQLError('No active organization id present')
        }

        if (args.userOnboardingData.user_created_in_tms) {
            const user = await db.user.update({
                where: {
                    id: args.userOnboardingData.user_id,
                },
                data: {
                    transfer_to_tms_status: true,
                },
            })
            const token = (user?.meta as UserMetaType)?.fcm_token

            await pubsub.publish(`TRANSFERRED_TO_TMS_${user?.id}`, {
                transferredToTMS: {
                    result: true,
                },
            })
            logHistory({
                entity_id: user.id?.toString(),
                ...EventList[EventType.TRANSFER_TO_TMS_STATUS],
                event_title: 'User Transferred to TMS',
                creator_type: 'ADMIN',
                new_values: {
                    transfer_to_tms_status: true,
                },
                user_id: user?.id || -1,
                org_id: user?.organization_id || -1,
                creator_id: admin.id,
                meta: {
                    ip_addr: ctx?.ipAddr || '',
                    user_agent: ctx?.userAgent || '',
                    user_id: user?.id || -1,
                    user_name: user?.name || '',
                    user_org_id: user?.organization_id || -1,
                    creator_id: ctx.admin.id,
                    creator_name: ctx.admin.name,
                    creator_org_id: ctx.admin.active_org_id,
                },
            })
            if (token) {
                Queues.pushNotificationQueue.add({
                    notification: {
                        title: `Congratulations ${user?.name}, You are now eligible for jobs and start your earning journey, `,
                        body: 'click to view your upcoming jobs.',
                    },
                    token: token,
                    meta: {
                        user_id: user.id,
                        user_name: user.name,
                        user_org_id: user.organization_id,
                    },
                })
            }
            const feedbackUrl = `${FRONTEND_URL}technician-feedback/${user?.id}`

            const shortUrl = await shortenUrl(feedbackUrl)
            Queues.whatsAppQueue.add({
                wa_number: user.phone
                    ? user.phone
                    : (user.phone?.toString() as string),
                template_name: 'onboarded_feedback_link',
                variables: {
                    'parameters[0]': user.name,
                    'parameters[1]': shortUrl || feedbackUrl,
                },
                meta: {
                    user_id: user.id,
                    user_name: user.name,
                    user_org_id: user.organization_id,
                },
            })

            await createUserTimestamp({ user_id: user.id, admin_id: admin.id })
            await awardReferralPoints(user)
        }

        return {
            result: true,
            message: 'Data Updated Success',
        }
    } catch (error) {
        throw new GraphQLError(error)
    }
}
const createOrUpdateUserBankData = async (
    _: any,
    args: { userBankData: BankDataInput },
    ctx: Context
) => {
    try {
        const admin = validateAdmin(ctx) //validate admin
        if (!admin.active_org_id) {
            throw new GraphQLError('No active organization id present')
        }
        const { user_id, ...userBankData } = args.userBankData
        const encrypted = CryptoUtility.encrypt(
            JSON.stringify({ ...userBankData })
        )
        const user = await db.user.findFirst({
            where: {
                id: user_id,
            },
        })
        if (!user) {
            throw new GraphQLError('User not found')
        }
        const bank_details = await db.bank_details.upsert({
            create: {
                encrypted: encrypted,
                user_id: user_id,
            },
            update: {
                encrypted: encrypted,
            },
            where: {
                user_id: user_id,
            },
        })

        logHistory({
            entity_id: bank_details.id?.toString(),
            ...EventList[EventType.UPDATE_BANK_DETAILS],
            creator_type: 'ADMIN',
            new_values: {
                encrypted: encrypted,
            },
            user_id: user_id,
            org_id: user.organization_id,
            creator_id: admin.id,
            meta: {
                ip_addr: ctx?.ipAddr || '',
                user_agent: ctx?.userAgent || '',
                user_id: user?.id || -1,
                user_name: user?.name || '',
                user_org_id: user?.organization_id || -1,
                creator_id: admin.id,
                creator_name: admin.name,
                creator_org_id: admin.active_org_id,
            },
        })
        return {
            result: true,
            message: 'Success.',
        }
    } catch (error) {
        throw new GraphQLError(error)
    }
}

const exportUserForTTT: QueryResolvers<Context>['exportUserForTTT'] = async (
    _,
    args,
    ctx
) => {
    try {
        const admin = validateAdmin(ctx)
        if (!admin.active_org_id) {
            throw new GraphQLError('No active organization id present')
        }
        // export data
        await Queues.exportUserTTTDataQueue.add({
            args,
            ctx,
        })
        return { result: true }
    } catch (error) {
        throw new GraphQLError(error)
    }
}

const transferToTmsResolver = {
    Query: {
        getUserForTTT,
        exportUserForTTT,
    },
    Mutation: {
        createOrUpdateUsersOnboardingData,
        createOrUpdateUserBankData,
    },
    Subscription: {
        transferredToTMS: {
            subscribe: (_: any, args: { user_id: number }) => {
                return pubsub.asyncIterator(
                    `TRANSFERRED_TO_TMS_${args?.user_id}`
                )
            },
        },
    },
}

export default transferToTmsResolver
