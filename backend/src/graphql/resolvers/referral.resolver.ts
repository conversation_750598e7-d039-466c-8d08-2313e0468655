import { OnboardingStage, Prisma } from '@prisma/client'
import { GraphQLError } from 'graphql'
import DataProvider from '../../data'
import Queues from '../../queue'
import { validateAdmin, validateUser } from '../../utils/authValidators'
import { FRONTEND_URL } from '../../utils/envVars'
import { Context } from '../../utils/types'
import { generateReferralCode } from '../../utils/user'
import { shortenUrl } from '../../utils/utils'
import { ExportableDataType, QueryResolvers } from '../__generated__/types'

const db = DataProvider.getDbInstance()

interface TermsAndCondition {
    [key: string]: string
}

interface ReferralConfigInput {
    existing_user_points: number
    new_user_points: number
    user_point_redeem_stage: OnboardingStage
    referral_enabled: boolean
    terms_and_condition: TermsAndCondition
}

const createReferralConfigAdmin = async (
    _: any,
    args: { data: ReferralConfigInput },
    ctx: Context
) => {
    try {
        const { data } = args
        const admin = validateAdmin(ctx)
        const createConfig = await db.referral_system_configuration.create({
            data: {
                existing_user_points: data.existing_user_points,
                new_user_points: data.new_user_points,
                user_point_redeem_stage: data.user_point_redeem_stage,
                referral_enabled: data.referral_enabled,
                terms_and_condition: data?.terms_and_condition,
                organization: {
                    connect: {
                        id: admin.active_org_id || -1,
                    },
                },
            },
        })
        return createConfig
    } catch (error) {
        throw new GraphQLError(error)
    }
}
const getReferralConfiguration = async (
    _: any,
    args: { configuration_id: number },
    ctx: Context
) => {
    try {
        const admin = validateAdmin(ctx)

        const referralConfigurations =
            await db.referral_system_configuration.findMany({
                where: {
                    organization_id: admin.active_org_id || -1,
                    ...(args.configuration_id
                        ? {
                              id: args.configuration_id,
                          }
                        : {}),
                },
                orderBy: { updated_at: 'asc' },
            })

        return referralConfigurations
    } catch (error) {
        throw new GraphQLError(error)
    }
}
const getReferralConfigurationForUser = async (
    _: any,
    __: any,
    ctx: Context
) => {
    try {
        const { user } = ctx

        if (!user) {
            throw new GraphQLError('user not valid')
        }

        const result = await db.referral_system_configuration.findFirst({
            where: {
                referral_enabled: true,
                user_point_redeem_stage: OnboardingStage?.ONBOARD,
            },
            orderBy: {
                created_at: 'desc',
            },
        })

        return result
    } catch (error) {
        throw new GraphQLError(error)
    }
}

interface UpdateReferralConfigInput extends ReferralConfigInput {
    configuration_id: number
}

const updateReferralConfigurations = async (
    _: any,
    args: { data: UpdateReferralConfigInput; configuration_id: number },
    ctx: Context
) => {
    try {
        validateAdmin(ctx)

        await db.referral_system_configuration.update({
            where: {
                id: args.configuration_id,
            },
            data: {
                ...args.data,
            },
        })

        return true
    } catch (error) {
        throw new GraphQLError(error)
    }
}
const generateReferralCodeFrUser = async (_: any, __: any, ctx: Context) => {
    try {
        const { user } = ctx

        const checkIfUsersTokenExist = await db.user_referral_details.findFirst(
            {
                where: {
                    user_id: user.id,
                },
            }
        )
        if (checkIfUsersTokenExist) return checkIfUsersTokenExist.referral_code
        const userReferral = await db.user_referral_details.create({
            data: {
                referral_code: generateReferralCode(user.name),
                user_id: user.id,
            },
        })
        //if user is present in unregistered referrals then update the status to false
        if (user.phone) {
            await db.unregistered_referrals.updateMany({
                where: {
                    phone_number: user.phone,
                    is_active: true,
                },
                data: {
                    is_active: false,
                },
            })
        }
        return userReferral.referral_code
    } catch (error) {
        throw new GraphQLError(error)
    }
}
const getUserReferralData = async (_: any, __: any, ctx: Context) => {
    try {
        const { user } = ctx
        const user_referral = await db.user_referral_details.findFirst({
            where: {
                user_id: user.id || -1,
            },
            include: {
                referred_by: {
                    select: {
                        name: true,
                        id: true,
                        user_referred_to: {
                            select: {
                                referral_code: true,
                            },
                        },
                    },
                },
                referred_to: {
                    select: {
                        name: true,
                        id: true,
                    },
                },
            },
        })
        return user_referral
    } catch (error) {
        throw new GraphQLError(error)
    }
}
const getLatestReferralConfiguration = async (
    _: any,
    args: { filter: OnboardingStage },
    ctx: Context
) => {
    try {
        const { user } = ctx
        if (!user) {
            throw new GraphQLError('user not valid')
        }
        const referrer = await db.referral_system_configuration.findFirst({
            where: {
                user_point_redeem_stage: args?.filter,
                referral_enabled: true,
            },
            orderBy: [
                {
                    created_at: 'desc',
                },
                {
                    updated_at: 'desc',
                },
            ],
        })
        if (!referrer) {
            throw new GraphQLError('Referral configuration not found')
        }
        return referrer
    } catch (error) {
        throw new GraphQLError(error)
    }
}

const getAllReferredUsersOfUser = async (_: any, args: any, ctx: Context) => {
    try {
        //this will return count of referred users also , and will take the user_id optional
        const { user } = ctx
        if (!user && !args?.user_id) {
            throw new GraphQLError('user not valid')
        }

        const where = {
            referred_by_id: user?.id || args?.user_id,
        }

        const select = {
            referred_to: {
                select: {
                    id: true,
                    name: true,
                    organization_id: true,
                    isActive: true,
                    created_at: true,
                    updated_at: true,
                    onboarding_stage: true,
                    source: true,
                    interview: {
                        select: {
                            status: true,
                        },
                    },
                },
            },
            id: true,
            referral_code: true,
            available_points: true,
            user_id: true,
            user_referral_ledger_id: true,
            referred_by_id: true,

            created_at: true,
        }

        const referredUsers = await db.user_referral_details.findMany({
            where: {
                ...where,
            },
            select: {
                ...select,
            },
        })

        return referredUsers
    } catch (error) {
        throw new GraphQLError(error)
    }
}
const getReferredUsersCount: QueryResolvers['getReferredUsersCount'] = async (
    _,
    __,
    ctx: Context
) => {
    try {
        const admin = validateAdmin(ctx)

        const common_where: Prisma.user_referral_detailsWhereInput = {
            referred_to: {
                organization_id: admin.active_org_id || -1,
            },
            referred_by_id: {
                not: null,
            },
        }

        const referred_users_count = await db.user_referral_details.count({
            where: common_where,
        })

        common_where.referred_to = {
            onboarding_stage: OnboardingStage.ONBOARD,
        }

        const onboard_users_count = await db.user_referral_details.count({
            where: common_where,
        })
        return {
            referred_count: referred_users_count,
            onboard_count: onboard_users_count,
        }
    } catch (error) {
        throw new GraphQLError(error)
    }
}

const getUnregisteredReferrals: QueryResolvers['getUnregisteredReferrals'] =
    async (_, args, ctx) => {
        try {
            validateAdmin(ctx)
            const { search, pagination } = args

            const where = {
                is_active: true,
                ...(search
                    ? {
                          OR: [
                              {
                                  name: {
                                      contains: search,
                                      mode: 'insensitive' as Prisma.QueryMode,
                                  },
                              },
                              {
                                  phone_number: {
                                      contains: search,
                                      mode: 'insensitive' as Prisma.QueryMode,
                                  },
                              },
                          ],
                      }
                    : {}),
            }

            const unregisteredReferrals =
                await db.unregistered_referrals.findMany({
                    where,
                    select: {
                        name: true,
                        phone_number: true,
                        referrer_id: true,
                        meta: true,
                        id: true,
                        referrer: {
                            select: {
                                id: true,
                                name: true,
                                phone: true,
                                user_referred_to: {
                                    select: {
                                        referral_code: true,
                                    },
                                },
                            },
                        },
                        created_at: true,
                        updated_at: true,
                    },
                    orderBy: { created_at: 'desc' },
                    ...(pagination?.take &&
                    pagination.skip !== undefined &&
                    pagination.skip !== null
                        ? {
                              take: pagination.take || 10,
                              skip: pagination.take * pagination.skip,
                          }
                        : {}),
                })

            const total_count = await db.unregistered_referrals.count({ where })
            const transformedData = unregisteredReferrals.map((referral) => ({
                ...referral,
                meta: referral.meta
                    ? (referral.meta as { [key: string]: any })
                    : undefined,
            }))
            return {
                data: transformedData,
                total_count,
            }
        } catch (error) {
            throw new GraphQLError(error)
        }
    }
const getMyReferrals: QueryResolvers['getMyReferrals'] = async (
    _,
    args,
    ctx: Context
) => {
    try {
        const user = validateUser(ctx)

        // --- REGISTERED REFERRALS ---
        const registeredWhere = {
            referred_by_id: user.id,
        }

        const registeredInclude = {
            referred_to: {
                select: {
                    name: true,
                    id: true,
                    phone: true,
                },
            },
            referred_by: {
                select: {
                    name: true,
                    id: true,
                    user_referred_to: {
                        select: {
                            referral_code: true,
                        },
                    },
                },
            },
        }

        const [registeredUsers, registeredTotalCount] = await Promise.all([
            db.user_referral_details.findMany({
                where: registeredWhere,
                include: registeredInclude,
                orderBy: { created_at: 'desc' },
            }),
            db.user_referral_details.count({ where: registeredWhere }),
        ])

        // --- UNREGISTERED REFERRALS ---

        const unregisteredWhere: Prisma.unregistered_referralsWhereInput = {
            referrer_id: user.id,
            is_active: true,
        }

        const [unregisteredReferrals, unregisteredTotalCount] =
            await Promise.all([
                db.unregistered_referrals.findMany({
                    where: unregisteredWhere,
                    select: {
                        name: true,
                        phone_number: true,
                        created_at: true,
                        referrer: {
                            select: {
                                name: true,
                                id: true,
                                user_referred_to: {
                                    select: {
                                        referral_code: true,
                                    },
                                },
                            },
                        },
                    },
                    orderBy: { created_at: 'desc' },
                }),
                db.unregistered_referrals.count({
                    where: unregisteredWhere,
                }),
            ])
        const enrichedRegistered = await Promise.all(
            registeredUsers.map(async (user: any) => {
                const referred_user = await db.user_referral_details.groupBy({
                    by: ['referred_by_id'],
                    where: {
                        referred_by_id: user.referred_to.id,
                    },
                    _count: true,
                })
                const url = `${FRONTEND_URL}/signup?referral_code=${user?.referred_by?.user_referred_to?.referral_code}`
                const shortUrl = await shortenUrl(url)
                user['referred_name'] = user?.referred_to?.name
                user['referred_id'] = user?.referred_to?.id
                user['referred_phone'] = user?.referred_to?.phone
                user['referrer_code'] =
                    user?.referred_by?.user_referred_to?.referral_code
                user['referred_count'] = referred_user[0]?._count || 0
                user['status'] = 'JOINED'
                user['url'] = `${shortUrl ? shortUrl : url} `

                return user
            })
        )
        const enrichedUnregistered = await Promise.all(
            unregisteredReferrals.map(async (referral: any) => {
                const url = `${FRONTEND_URL}/signup?referral_code=${referral?.referrer?.user_referred_to?.referral_code}`
                const shortUrl = await shortenUrl(url)
                referral['referrer_code'] =
                    referral?.referrer?.user_referred_to?.referral_code
                referral['status'] = 'PENDING'
                referral['referred_name'] = referral?.name
                referral['referred_id'] = referral?.id
                referral['referred_phone'] = referral?.phone_number
                referral['url'] = `${shortUrl ? shortUrl : url} `
                return referral
            })
        )
        return {
            data: [...enrichedRegistered, ...enrichedUnregistered],
            total_count: registeredTotalCount + unregisteredTotalCount,
        }
    } catch (error) {
        throw new GraphQLError(error)
    }
}

const getExportableReferredData: QueryResolvers['getExportableReferredData'] =
    async (_, args, ctx: Context) => {
        try {
            const admin = validateAdmin(ctx)
            const { filter, search, pagination } = args

            const where_filter: Prisma.user_referral_detailsWhereInput = {
                referred_to: {
                    organization_id: admin.active_org_id || -1,
                },
                ...(search
                    ? {
                          referred_to: {
                              name: {
                                  contains: `%${args.search}%`,
                                  mode: 'insensitive',
                              },
                          },
                      }
                    : {}),
            }
            const include: Prisma.user_referral_detailsInclude = {
                referred_to: {
                    select: {
                        name: true,
                        id: true,
                        onboarding_stage: true,
                        updated_at: true,
                        created_at: true,
                        designation: {
                            select: {
                                id: true,
                                name: true,
                                level: true,
                                is_active: true,
                                is_deleted: true,
                                created_at: true,
                                updated_at: true,
                                designation_expertise_map: {
                                    select: {
                                        id: false,
                                        designation_id: false,
                                        expertise_id: false,
                                        expertise: {
                                            select: {
                                                id: true,
                                                name: true,
                                                icon: true,
                                            },
                                        },
                                    },
                                },
                                admin: {
                                    select: {
                                        id: true,
                                        name: true,
                                        type: true,
                                    },
                                },
                            },
                        },
                    },
                },
            }

            if (filter?.type == ExportableDataType.Individual) {
                where_filter.referred_by_id = null
            } else {
                where_filter.referred_by_id = { not: null }
                include.referred_by = {
                    select: {
                        name: true,
                        id: true,
                        onboarding_stage: true,
                        designation: {
                            select: {
                                id: true,
                                name: true,
                                level: true,
                            },
                        },
                        user_referred_to: {
                            select: {
                                referral_code: true,
                            },
                        },
                    },
                }
            }

            if (filter?.start_date) {
                where_filter.referred_to = {
                    created_at: {
                        gte: filter.start_date.toISOString(),
                        lte: filter.end_date
                            ? filter.end_date.toISOString()
                            : new Date().toISOString(),
                    },
                }
            }

            const users = await db.user_referral_details.findMany({
                where: where_filter,
                include: include,
                orderBy: { created_at: 'desc' },
                ...(pagination?.take &&
                pagination.skip !== undefined &&
                pagination.skip !== null
                    ? {
                          take: pagination?.take || 10,
                          skip: pagination?.take * pagination.skip,
                      }
                    : {}),
            })

            //giving any as it can differ based on type (individual || referred)
            const modify_users = users.map(async (user: any) => {
                const referred_user = await db.user_referral_details.groupBy({
                    by: ['referred_by_id'],
                    where: {
                        referred_by_id: user.referred_to.id,
                    },
                    _count: true,
                })

                //added referrer_code separately to fix empty referral_code issue
                user['referrer_code'] =
                    user?.referred_by?.user_referred_to?.referral_code

                user['referred_count'] = referred_user[0]?._count || 0

                return user
            })

            const data = await Promise.all(modify_users)
            const total_count = await db.user_referral_details.count({
                where: where_filter,
            })

            return { data: data, total_count: total_count } as any
        } catch (error) {
            throw new GraphQLError(error)
        }
    }

const createUnregisteredReferral = async (_: any, args: any, ctx: Context) => {
    try {
        const { user } = ctx
        const { data } = args

        if (!user) {
            throw new GraphQLError('User not valid')
        }

        const unregisteredReferral = await db.unregistered_referrals.create({
            data: {
                name: data.name,
                phone_number: data.phone_number,
                meta: data.meta || {},
                is_active: true,
                referrer: {
                    connect: {
                        id: user.id,
                    },
                },
            },
        })

        return unregisteredReferral
    } catch (error) {
        throw new GraphQLError(error)
    }
}

const updateUnregisteredReferral = async (_: any, args: any, ctx: Context) => {
    try {
        const { user } = ctx
        const { id, data } = args

        if (!user) {
            throw new GraphQLError('User not valid')
        }

        // Check if the referral belongs to the user
        const existingReferral = await db.unregistered_referrals.findFirst({
            where: {
                id,
                referrer_id: user.id,
                is_active: true,
            },
        })

        if (!existingReferral) {
            throw new GraphQLError(
                'Referral not found or you do not have permission to update it'
            )
        }

        const updatedReferral = await db.unregistered_referrals.update({
            where: { id },
            data: {
                name: data.name,
                phone_number: data.phone_number,
                meta: data.meta || existingReferral.meta,
            },
        })

        return updatedReferral
    } catch (error) {
        throw new GraphQLError(error)
    }
}

const deleteUnregisteredReferral = async (_: any, args: any, ctx: Context) => {
    try {
        const { user } = ctx
        const { id } = args

        if (!user) {
            throw new GraphQLError('User not valid')
        }

        // Check if the referral belongs to the user
        const existingReferral = await db.unregistered_referrals.findFirst({
            where: {
                id,
                referrer_id: user.id,
                is_active: true,
            },
        })

        if (!existingReferral) {
            throw new GraphQLError(
                'Referral not found or you do not have permission to delete it'
            )
        }

        await db.unregistered_referrals.delete({
            where: { id },
        })

        return true
    } catch (error) {
        throw new GraphQLError(error)
    }
}
const checkUnregisteredReferral = async (
    _: any,
    args: { phone_number: string },
    ctx: Context
) => {
    try {
        validateUser(ctx)
        const { phone_number } = args

        const referrals = await db.unregistered_referrals.findMany({
            where: {
                phone_number,
                is_active: true,
            },
            select: {
                referrer: {
                    select: {
                        id: true,
                        name: true,
                        user_referred_to: {
                            select: {
                                referral_code: true,
                            },
                        },
                    },
                },
            },
        })

        if (referrals.length > 0) {
            return {
                is_referred: true,
                referrers: referrals.map((r) => ({
                    id: r.referrer?.id,
                    name: r.referrer?.name,
                    referral_code: r.referrer?.user_referred_to?.referral_code,
                })),
            }
        }

        return {
            is_referred: false,
            referrers: [],
        }
    } catch (error) {
        throw new GraphQLError(error)
    }
}

const exportUnregisteredReferrals: QueryResolvers['exportUnregisteredReferrals'] =
    async (_, args, ctx) => {
        try {
            validateAdmin(ctx)
            const { search, start_date, end_date } = args

            const where = {
                ...(search
                    ? {
                          OR: [
                              {
                                  name: {
                                      contains: search,
                                      mode: 'insensitive' as Prisma.QueryMode,
                                  },
                              },
                              {
                                  phone_number: {
                                      contains: search,
                                      mode: 'insensitive' as Prisma.QueryMode,
                                  },
                              },
                          ],
                      }
                    : {}),
                ...(start_date
                    ? {
                          created_at: {
                              gte: start_date,
                              lte: end_date || new Date(),
                          },
                      }
                    : {}),
            }

            const unregisteredReferrals =
                await db.unregistered_referrals.findMany({
                    where,
                    select: {
                        id: true,
                        name: true,
                        phone_number: true,
                        created_at: true,
                        updated_at: true,
                        referrer: {
                            select: {
                                name: true,
                                phone: true,
                                user_referred_to: {
                                    select: {
                                        referral_code: true,
                                    },
                                },
                            },
                        },
                    },
                    orderBy: { created_at: 'desc' },
                })

            return {
                data: unregisteredReferrals,
                total_count: unregisteredReferrals.length,
            }
        } catch (error) {
            throw new GraphQLError(error)
        }
    }

const sendRefferalInvite = async (_: any, args: any, ctx: Context) => {
    try {
        const user = validateUser(ctx)
        const { data } = args
        const url = `${FRONTEND_URL}?referral_code=${data.referral_code}`

        //data: { name: string; phone_number: string; meta: any; referral_code: string }
        const existingReferral = await db.unregistered_referrals.findFirst({
            where: {
                phone_number: data.phone_number,
                referrer_id: user.id,
                is_active: true,
            },
        })

        if (existingReferral) {
            throw new GraphQLError('Referral invite already sent')
        }

        const unregisteredReferral = await db.unregistered_referrals.create({
            data: {
                name: data.name,
                phone_number: data.phone_number,
                meta: data.meta || {},
                is_active: true,
                referrer: {
                    connect: {
                        id: user.id,
                    },
                },
                updated_at: new Date(),
            },
        })

        const shortUrl = await shortenUrl(url)
        //TODO uncomment is_prod after testing
        // if (IS_PROD) {

        // await sendSms(
        //     data.phone_number,
        //     `Wify se judiye aur Rs. 45,000 tak Ki Mahine Ki Kamai ka avsar paye. Judne ke liye link pe click kare or Register kare.' WIFY Partner App - ${
        //         shortUrl ? shortUrl : url
        //     } `,
        //     {
        //         user_id: user?.id,
        //         user_name: user?.name,
        //         user_org_id: user?.organization_id,
        //     }
        // )

        Queues.whatsAppQueue.add({
            wa_number: data.phone_number,
            template_name: 'referral_invite_link',
            variables: {
                'parameters[0]': data.name,
                'parameters[1]': user.name,
                'parameters[2]': shortUrl || url,
            },
            meta: {
                user_id: user.id,
                user_name: user.name,
                user_org_id: user.organization_id,
            },
        })
        // }
        return unregisteredReferral
    } catch (error) {
        throw new GraphQLError(error)
    }
}

const referralResolver = {
    Query: {
        getReferralConfiguration,
        getReferralConfigurationForUser,
        getReferredUsersCount,
        generateReferralCodeFrUser,
        getUserReferralData,
        getLatestReferralConfiguration,
        getAllReferredUsersOfUser,
        getExportableReferredData,
        getUnregisteredReferrals,
        getMyReferrals,
        checkUnregisteredReferral,
        exportUnregisteredReferrals,
    },
    Mutation: {
        createReferralConfigAdmin,
        updateReferralConfigurations,
        createUnregisteredReferral,
        updateUnregisteredReferral,
        deleteUnregisteredReferral,
        sendRefferalInvite,
    },
}

export default referralResolver
