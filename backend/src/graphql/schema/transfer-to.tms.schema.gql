enum MartialStatus {
    MARRIED
    UNMARRIED
}
enum HiringCriteria {
    TRAINING_ASSESSMENT_TEST
    TECHNICAL_ROUND_AND_PAST_EXPERIENCE_OF_MODULAR
    ONSITE_PRACTICAL_TEST_AND_THEORETICAL_TEST
}

type UserDesignation {
    id: String
    name: String
    level: Int
}
type UserOnboardingDataResult {
    id: String
    name: String
    gender: Gender
    phone: String
    organization: Organization
    organization_id: String
    phone_verified: Boolean
    photoUrl: String
    isActive: Boolean
    onboarding_stage: OnboardingStage
    location_id: Int
    location: Location
    email: String
    doc_verification_state: DocVerificationStatus
    phoneVerified: Boolean
    user_onboarding_data: [UserOnboardingData]
    bank_details: UserBankDetails
    employee_id: Int
    transfer_to_tms_status: Boolean
    designation: UserDesignation
    meta: JSON
}
type UserBankDetails {
    bank_name: String
    account_number: String
    ifsc: String
}
type BankDetails {
    user_id: Int
    encrypted: String
}
input UserBankDetailsInput {
    user_id: Int!
    bank_name: String
    account_number: String
    ifsc: String
}
input BankData {
    user_id: Int
    encrypted: UserBankDetailsInput
}

type UserOnboardingData {
    martial_status: MartialStatus
    source: String
    hiring_criteria: HiringCriteria
    date_of_joining: Date
    designation: String
    hiring_manager_id: Int
    esic_number: String
    mothers_name: String
    fathers_name: String
    salary_offered: String
    remark: String
    aadhar_address: String
    pancard_number: String
    aadhar_number: String
    user_created_in_tms: Boolean
    current_address: Int
    employee_id: Int
    form_no_11: String
    city: String
    assessment_score: String
    family_details: String
    assessed_by: String
    user_id: Int
    meta: JSON
    salary_vp: String
    poc: String
}
input UserOnboardingDataInput {
    martial_status: MartialStatus
    source: String
    hiring_criteria: HiringCriteria
    date_of_joining: Date
    designation: String
    hiring_manager_id: Int
    esic_number: String
    mothers_name: String
    fathers_name: String
    salary_offered: String
    remark: String
    aadhar_address: String
    pancard_number: String
    aadhar_number: String
    user_created_in_tms: Boolean
    current_address: Int
    employee_id: Int
    form_no_11: String
    city: String
    assessment_score: String
    family_details: String
    assessed_by: String
    user_id: Int
}
input GetUserTTTInputFilter {
    transfer_to_tms_status: Boolean
    startDate: String
    endDate: String
}
type Success {
    result: Boolean!
}

type UserOnboardingDataResultWithCount {
    data: [UserOnboardingDataResult]
    total_count: Int
}

type Query {
    getUserForTTT(
        user_id: Int
        search: String
        filter: GetUserTTTInputFilter
        pagination: Pagination
    ): UserOnboardingDataResultWithCount
    exportUserForTTT(search: String, filter: GetUserTTTInputFilter): Success!
}
type Mutation {
    createOrUpdateUsersOnboardingData(
        userOnboardingData: UserOnboardingDataInput
    ): Result
    createOrUpdateUserBankData(userBankData: UserBankDetailsInput): Result
    updateUsersOnboardingData: Boolean
}
type Subscription {
    transferredToTMS(user_id: Int!): Success!
}
