type ResponseResult {
    result: <PERSON><PERSON><PERSON>!
    message: String!
}

input AdminProtectedFeature {
    feature_id: ID!
    is_module: Boolean
    parent_module_id: Int
    module_id: Int
    parent_module_name: String
    module_name: String
    sub_module_name: String
    sub_module_url_user: String
    sub_module_url_sp: String
    icon_name: String
    section_id: String
    created_at: Date
    is_active: Boolean
    updated_at: Date
}
type AdminProtectedFeatureData {
    feature_id: ID!
    is_module: Boolean
    parent_module_id: Int
    module_id: Int
    parent_module_name: String
    module_name: String
    sub_module_name: String
    sub_module_url_user: String
    sub_module_url_sp: String
    icon_name: String!
    section_id: String
    created_at: Date!
    is_active: Boolean!
    updated_at: Date
}

input RolePermissionData {
    id: Int
    admin_role_id: Int
    feature_id: Int
    is_module: Boolean
    module_id: Int
    permission_for_user: JSON
    permission_for_sp: JSON
}
type AdminRolePermission {
    id: Int
    admin_role_id: Int
    feature_id: Int
    permission_for_user: JSON
    permission_for_sp: JSON
    admin_protected_feature: AdminProtectedFeatureData
}
input AdminRoleInput {
    name: String
    description: String
    is_root_admin: <PERSON>olean
    has_admin_privileges: Boolean
    can_reject: Boolean
    can_ban: Boolean
    can_accept: Boolean
    can_download: Boolean
    creator_id: Int
    role_permission_array: [RolePermissionData]
}
input UpdateAdminRoleInput {
    admin_role_id: Int
    name: String
    description: String
    is_root_admin: Boolean
    has_admin_privileges: Boolean
    can_reject: Boolean
    can_ban: Boolean
    can_download: Boolean
    can_accept: Boolean
    creator_id: Int
    admin_role_permission: [RolePermissionData]
}
type AdminRoleData {
    admin_role_id: ID!
    name: String!
    description: String
    is_root_admin: Boolean!
    has_admin_privileges: Boolean!
    can_reject: Boolean!
    can_ban: Boolean!
    can_download: Boolean!
    can_accept: Boolean!
    created_at: Date!
    updated_at: Date
    creator_id: ID!
    updated_by: ID
    admin_role_permission: [AdminRolePermission!]
}

type UnpublishedModuleData {
    module_id: Int!
    module_name: String!
    parent_module_name: String
    parent_module_id: Int
    is_published: Boolean!
    is_active: Boolean!
    icon_name: String!
    visible_in_sp: Boolean!
    visible_in_users: Boolean!
    published_at: Date
    published_by: Int
    submodules: [SubmoduleData]
}
type UnpublishedModuleWithSubmodules {
    module_id: Int
    module_name: String
    parent_module_name: String
    parent_module_id: Int
    is_published: Boolean
    is_active: Boolean
    icon_name: String
    visible_in_sp: Boolean
    visible_in_users: Boolean
    published_at: Date
    published_by: Int
    submodules: [SubmoduleData]
}

type SubmoduleData {
    feature_id: Int
    is_module: Boolean
    parent_module_id: Int
    module_id: Int
    parent_module_name: String
    module_name: String
    sub_module_name: String
    sub_module_url_user: String
    sub_module_url_sp: String
    icon_name: String
    section_id: String
    created_at: Date
    is_active: Boolean
    updated_at: Date
}
type AdminRoleResponse {
    result: Boolean!
    message: String!
    data: [AdminRoleData!]!
}
type AdminRoleByIdResponse {
    result: Boolean!
    message: String!
    data: AdminRoleData!
}
type UnpublishedModuleResponse {
    result: Boolean!
    message: String!
    data: [UnpublishedModuleData]
}
type UnpublishedModuleByIdResponse {
    result: Boolean!
    message: String!
    data: UnpublishedModuleWithSubmodules
}

input NewModuleData {
    module_name: String!
    icon_name: String!
    visible_in_sp: Boolean!
    visible_in_users: Boolean!
}
input NewFeatureData {
    is_module: Boolean
    sub_module_name: String
    sub_module_url_user: String
    sub_module_url_sp: String
}

input SectionInput {
    section_id: String!
    section_name: String!
    section_url_user: String
    section_url_sp: String
    sub_module_name: String!
}

type Submodule {
    submodule_id: Int!
    submodule_name: String!
    submodule_url_user: String
    submodule_url_sp: String
}

type Mutation {
    createAdminRole(data: AdminRoleInput): ResponseResult

    updateAdminRole(data: UpdateAdminRoleInput!): ResponseResult!
    deleteAdminRole(admin_role_id: Int!): ResponseResult!
    assignRoleToAdmin(admin_id: Int!, admin_role_id: Int!): ResponseResult!
    publishModule(
        module_id: Int!
        role_permission: [RolePermissionData!]!
    ): ResponseResult!
    createNewModule(
        module: NewModuleData!
        features: [NewFeatureData]!
    ): ResponseResult!
    createNewPage(page: [NewFeatureData]!, module_id: Int!): ResponseResult!
    createNewSection(
        moduleId: Int!
        submoduleId: Int!
        sections: [SectionInput!]!
    ): ResponseResult!
}

type Query {
    getAdminRoleByRoleId(admin_role_id: Int!): AdminRoleByIdResponse
    getAllAdminRoles: AdminRoleResponse
    getUnpublishedModules: UnpublishedModuleResponse
    getUnpublishedModuleById(module_id: Int!): UnpublishedModuleByIdResponse
    getSubmodules(moduleId: Int!): [Submodule!]!
}
