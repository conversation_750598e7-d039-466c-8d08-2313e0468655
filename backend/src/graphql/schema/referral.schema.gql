type ReferredTo {
    id: Int
    name: String
    organization_id: Int
    isActive: Boolean
    created_at: Date
    updated_at: Date
    onboarding_stage: OnboardingStage
    source: String
    interview: [InterviewData]
}

type ReferredUser {
    id: Int
    referral_code: String
    available_points: Int
    user_id: Int
    user_referral_ledger_id: Int
    referred_by_id: Int
    created_at: Date
    referred_to: ReferredTo
}
input ReferralConfigInput {
    existing_user_points: Int
    new_user_points: Int
    user_point_redeem_stage: OnboardingStage
    referral_enabled: Boolean
    terms_and_condition: JSON
}
input UpdateReferralConfigInput {
    existing_user_points: Int
    new_user_points: Int
    user_point_redeem_stage: OnboardingStage
    referral_enabled: Boolean
    terms_and_condition: JSON
}
type ReferralConfig {
    id: Int
    existing_user_points: Int
    new_user_points: Int
    user_point_redeem_stage: OnboardingStage
    referral_enabled: Boolean
    terms_and_condition: JSON
}
type ReferrerData {
    onboarding_stage: OnboardingStage
    name: String
    id: Int
    user_referred_to: ReferralCode
    updated_at: Date
    created_at: Date
    designation: Designation
}
type ReferralCode {
    referral_code: String
}

type ReferredUsersData {
    available_points: Int
    referral_code: String
    referred_to: ReferrerData
    referred_by: ReferrerData
    updated_at: Date
    created_at: Date
    referred_count: Int
    referrer_code: String
}

type ReferredUsersDataWithCount {
    data: [ReferredUsersData]
    total_count: Int
}

type ReferredAndOnboardCount {
    referred_count: Int!
    onboard_count: Int!
}

type UnregisteredReferral {
    id: ID!
    name: String!
    phone_number: String!
    referrer: ReferrerData
    referrer_id: Int
    created_at: Date!
    updated_at: Date!
    meta: JSON
}

type UnregisteredReferralsResponse {
    data: [UnregisteredReferral!]!
    total_count: Int!
}

enum ExportableDataType {
    REFERRED
    INDIVIDUAL
}
input ExportableReferredData {
    type: ExportableDataType
    start_date: Date
    end_date: Date
}
type Query {
    getReferralConfiguration(configuration_id: Int): [ReferralConfig]
    getReferralConfigurationForUser: ReferralConfig
    getReferredUsersCount: ReferredAndOnboardCount
    generateReferralCodeFrUser: String
    getUserReferralData: ReferredUsersData
    getLatestReferralConfiguration(filter: OnboardingStage!): ReferralConfig
    getAllReferredUsersOfUser(user_id: Int): [ReferredUser]
    getExportableReferredData(
        filter: ExportableReferredData
        search: String
        pagination: Pagination
    ): ReferredUsersDataWithCount
    getUnregisteredReferrals(
        search: String
        pagination: Pagination
    ): UnregisteredReferralsResponse!
}
input CreateUnregisteredReferralInput {
    name: String!
    phone_number: String!
    meta: JSON
}

type Mutation {
    createReferralConfigAdmin(data: ReferralConfigInput): ReferralConfig
    updateReferralConfigurations(
        data: UpdateReferralConfigInput
        configuration_id: Int!
    ): Boolean
    createUnregisteredReferral(
        data: CreateUnregisteredReferralInput!
    ): UnregisteredReferral!
    updateUnregisteredReferral(
        id: ID!
        data: CreateUnregisteredReferralInput!
    ): UnregisteredReferral!
    deleteUnregisteredReferral(id: ID!): Boolean!
}
