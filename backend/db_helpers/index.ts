import bulk from './bulk'
import { rl } from './common'
import initData from './init_data'

async function main() {
    console.log(`
        What do you wanna do ?
        1 > Create basic initial data to start
        2 > Create bulk data 
   `)

    const ans = await rl.question('Answer :: ')

    if (ans === '1') {
        initData()
    } else if (ans === '2') {
        bulk()
    } else {
        console.log(
            'wrong option selected. \n Please input the number given before each option'
        )
    }
}

main()
