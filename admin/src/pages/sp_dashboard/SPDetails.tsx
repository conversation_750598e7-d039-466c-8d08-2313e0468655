import { Card, Tabs } from "antd";
import { useEffect, useState } from "react";
import { Outlet, useNavigate, useParams, useSearchParams } from "react-router-dom";
import {
  useGetProviderDetailsAdminQuery,
  useGetTeamMembersDataForTttLazyQuery
} from "../../__generated__";
import ErrorComponent from "../../components/error/Error";
import Loading from "../../components/loading/Loading";
import { ProtectedRoute } from "../../hooks/useSecureRoutePermissions";
import Impersonification from "../users/impersonify";
import SPGeneralDetailsTab from "../users/user-details/service-provider/SPGeneralDetailsTab";
import SPDetailsHeader from "./SPDetailsHeader";
import TeamMembers from "./TeamMembersTab";

const SPDetails: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();
  const [activeKey, setActiveKey] = useState("general_details");
  const [disablePageLevelAuth, setDisablePageLevelAuth] = useState(false);

  const params = useParams();
  const { data: providerDetails } = useGetProviderDetailsAdminQuery({
    variables: {
      userId: Number(params.user_id)
    },
    fetchPolicy: "no-cache"
  });
  const [getTeamMembersDataForTtt, { data: teamMembers }] = useGetTeamMembersDataForTttLazyQuery({
    variables: {
      data: {
        organization_id: providerDetails?.getProviderDetailsAdmin?.id || -1
      }
    },
    fetchPolicy: "no-cache"
  });

  //Graphql Query
  const { data, loading, error } = useGetProviderDetailsAdminQuery({
    variables: {
      userId: Number(params?.user_id)
    }
  });

  //fetch the service provider query here
  const sp_data = data?.getProviderDetailsAdmin;
  const checkTeamMemberTransferred = teamMembers?.getTeamMembersDataForTTT
    ?.filter((member) => member.onboarded)
    .every((member) => member.user_onboarding_data?.[0]?.user_created_in_tms);
  //change this any later on 👈
  const menu = [
    {
      label: "General Details",
      key: "general_details",
      children: (
        <SPGeneralDetailsTab
          setActiveKey={setActiveKey}
          isTransferred={checkTeamMemberTransferred || false}
        />
      )
    },
    {
      label: "Documents",
      key: "documents",
      children: <Outlet />
    },
    {
      label: "Test Update",
      key: "assignment",
      children: <Outlet />
    },
    {
      label: "Interview",
      key: "interview",
      children: <Outlet />
    },
    {
      label: "Team Members",
      key: "team_members",
      children: <TeamMembers />,
      disabled: false
    },

    {
      label: "Compliance",
      key: "compliance",
      children: <Outlet />
    },

    {
      label: "Technician View",
      key: "impersonification",
      children: <Impersonification />,
      disabled: true
    }
  ];
  const setActiveTab = (tab: string, navigateToTab = true) => {
    setSearchParams({ tab: tab });
    if (navigateToTab) {
      navigate(`${tab}`);
    } else {
      navigate("/dashboard/sp/details/" + params?.user_id);
    }
  };
  useEffect(() => {
    const isSpDetailsExactPath = /^\/dashboard\/sp\/details\/\d+$/.test(location.pathname);

    setDisablePageLevelAuth(!isSpDetailsExactPath);
  }, [location.pathname]);
  useEffect(() => {
    const tab = searchParams.get("tab");
    if (tab) {
      setActiveKey(tab);
    }
  }, [searchParams]);
  const org_id = providerDetails?.getProviderDetailsAdmin?.id || -1;
  useEffect(() => {
    if (org_id) {
      getTeamMembersDataForTtt({
        variables: {
          data: {
            organization_id: org_id
          }
        }
      });
    }
  }, [providerDetails]);
  if (loading) {
    return <Loading tip="Loading..." />;
  }
  if (error) {
    return <ErrorComponent error={error.message} />;
  }
  return (
    <ProtectedRoute disablePageLevelAuth={disablePageLevelAuth}>
      {({ permissions }) => (
        <div className="top-2">
          <SPDetailsHeader
            data={{
              orgId: sp_data?.id as number,
              companyName: sp_data?.name || "Unavailable",
              name: sp_data?.org_owner?.name || "Unavailable",
              email: sp_data?.org_owner?.email || "Unavailable",
              phone: sp_data?.org_owner?.phone || "Unavailable",
              location: sp_data?.org_owner?.meta?.primary_city || "Unavailable",
              category:
                sp_data?.org_expertise_map?.map((expertise) => expertise?.expertise?.name) || [],
              profileImage: sp_data?.org_owner?.photoUrl || "",
              userState: sp_data?.meta?.user_state || "",
              onboarded: sp_data?.org_owner?.onboarded || false
            }}
            permissions={permissions}
          />
          <Card className="mt-2 bg-gray-50 border-0 shadow-0">
            <Tabs
              activeKey={activeKey}
              defaultActiveKey={searchParams?.get("tab") || ""}
              onChange={(key) => {
                setActiveKey(key);
                switch (key) {
                  case "assignment":
                    setActiveTab("assignment", true);
                    break;
                  case "interview":
                    setActiveTab("interview", true);
                    break;
                  case "documents":
                    setActiveTab("documents", true);
                    break;
                  case "team_members":
                    setActiveTab("team_members", false);
                    break;
                  case "compliance":
                    setActiveTab("compliance", true);
                    break;
                  default:
                    setActiveTab("general_details", false);
                    break;
                }
              }}
              items={menu}
            />
          </Card>
        </div>
      )}
    </ProtectedRoute>
  );
};

export default SPDetails;
