import { Checkbox, Collapse, Empty, Form, Modal, Spin } from "antd";
import { CheckboxChangeEvent } from "antd/es/checkbox";
import { CheckboxValueType } from "antd/es/checkbox/Group";
import { useEffect, useState } from "react";
import { BiExport } from "react-icons/bi";
import { toTitleCase } from "../../utils/utils";
import FormBuilder from "antd-form-builder";
import { useForm } from "antd/es/form/Form";
import dayjs from "dayjs";
import isEmpty from "is-empty";
import { ExportableDataType } from "../../__generated__";

interface ExportJsonProps {
  setIsExportModalOpen: (isExportModalOpen: boolean) => void;
  isExportModalOpen: boolean;
  meta: any;
  fetchExportableReferredData: (filter: any) => any;
  activeTab: string;
}

const ExportJson: React.FC<ExportJsonProps> = ({
  setIsExportModalOpen,
  isExportModalOpen,
  meta,
  fetchExportableReferredData,
  activeTab
}) => {
  //antd Panel
  const { Panel } = Collapse;
  //antd Checkbox
  const CheckboxGroup = Checkbox.Group;

  //Hooks
  const [exportForm] = useForm();
  const [exportData, setExportData] = useState<string[]>([]);
  const [downloadableData, setDownloadableData] = useState<Array<any>>();
  const [finalChecklist, setFinalChecklist] = useState<any>([]);
  const [checkAll, setCheckAll] = useState(false);
  const [formData, setFormData] = useState<any>({});
  const [isLoading, setIsLoading] = useState(false);

  //Functions
  const onDataDownload = () => {
    let id = 1;
    let result = "";

    if (downloadableData?.length) {
      for (let i = 0; i < downloadableData.length; i++) {
        const finalObj: any = {};
        finalObj.id = id;
        id++;

        //out of for each assign keys and values of final obj to result
        //in foreach create the final obj only

        finalChecklist?.forEach((item: any) => {
          if (item == "name") {
            finalObj[item] = downloadableData[i]?.referred_to?.name;
          }
          if (item == "user_code") {
            finalObj[item] = downloadableData[i]?.referral_code;
          }
          if (item == "available_points") {
            finalObj[item] = downloadableData[i]?.available_points || "-";
          }
          if (item == "referrer_name") {
            finalObj[item] = downloadableData[i]?.referred_by?.name;
          }
          if (item == "referrer_code") {
            finalObj[item] = downloadableData[i]?.referrer_code;
          }
          if (item == "onboarding_stage") {
            finalObj[item] = downloadableData[i]?.referred_to?.onboarding_stage;
          }
          if (item == "no_of_referred") {
            finalObj[item] = downloadableData[i]?.referred_count;
          }
          if (item == "created_at" || item == "updated_at") {
            const data =
              item === "created_at"
                ? downloadableData[i]?.created_at
                : downloadableData[i]?.updated_at;
            finalObj[item] = dayjs(data).format("MMMM-D-YYYY");
          }
          if (item == "designation") {
            finalObj[item] = downloadableData[i]?.referred_to?.designation?.name || "-";
          }
          if (item == "referrer_designation") {
            finalObj[item] = downloadableData[i]?.referred_by?.designation?.name || "-";
          }
        });

        if (i == 0) {
          result += Object.keys(finalObj).join(",") + "\n";
        }
        result += Object.values(finalObj).join(",") + "\n";
      }

      const element = document.createElement("a");
      const file = new Blob([result], { type: "text/plain" });
      element.href = URL.createObjectURL(file);
      element.download = `${dayjs().format("MMMM_D_YYYY")}_${
        activeTab == "individual" ? activeTab : "referred"
      }_Data.csv`;
      element.click();
    }
  };

  const onChangeCheckBox = (list: CheckboxValueType[]) => {
    setFinalChecklist(list);
    setCheckAll(list.length === exportData?.length);
  };

  const onCheckAllChange = (e: CheckboxChangeEvent) => {
    setFinalChecklist(e.target.checked ? exportData : []);
    setCheckAll(e.target.checked);
  };

  const fetchData = async () => {
    setIsLoading(true);
    //Graphql Query
    await fetchExportableReferredData({
      variables: {
        filter: {
          type:
            activeTab == "individual" ? ExportableDataType.Individual : ExportableDataType.Referred,
          start_date: formData?.user_creation_date?.[0] || null,
          end_date: formData?.user_creation_date?.[1] || null
        }
      },
      onCompleted(data: any) {
        const fields = data?.getExportableReferredData?.data?.[0];
        //pushed common fields
        const selectableFields = [
          "name",
          "user_code",
          "available_points",
          "onboarding_stage",
          "created_at",
          "updated_at",
          "no_of_referred",
          "designation"
        ];
        for (const key in fields) {
          if (key == "referred_by") {
            if (fields[key] !== null) {
              selectableFields.push("referrer_name");
              selectableFields.push("referrer_code");
              selectableFields.push("referrer_designation");
            }
          }
        }
        setIsLoading(false);
        setExportData(selectableFields);
        setDownloadableData(data.getExportableReferredData?.data);
      },
      onError(error: any) {
        console.error(error);
      }
    });
  };

  const handleValuesChange = (values: any) => {
    if (values.user_creation_date) {
      //if same day is selected for start and end date 00:00:00 - 23:59:59
      const start_date = values.user_creation_date[0].startOf("day");
      const end_date = values.user_creation_date[1].endOf("day");
      values.user_creation_date = [start_date, end_date];
    }

    setFormData({ ...formData, ...values });
  };

  useEffect(() => {
    fetchData();
  }, [formData]);

  return (
    <Modal
      centered
      destroyOnClose
      open={isExportModalOpen}
      onCancel={() => setIsExportModalOpen(false)}
      onOk={onDataDownload}
      okButtonProps={{
        disabled: isEmpty(downloadableData) || finalChecklist.length < 1
      }}
      closeIcon={false}
    >
      <div className="mx-2 my-2">
        <div className="text-center text-purple-600 text-xl mb-4">
          <BiExport />
          <span className="mx-2">Export Data</span>
        </div>

        <Form
          layout="vertical"
          form={exportForm}
          onValuesChange={handleValuesChange}
          onFinish={exportForm.submit}
        >
          <FormBuilder meta={meta as any} form={exportForm} />
        </Form>

        {isLoading ? (
          <div className="flex justify-center items-center">
            <Spin tip="Loading..." />
          </div>
        ) : (
          <Collapse defaultActiveKey={["1"]}>
            <Panel
              className="relative"
              header={
                <span>
                  <Checkbox
                    onChange={onCheckAllChange}
                    onClick={(e) => {
                      e.stopPropagation();
                    }}
                    checked={checkAll}
                    disabled={isEmpty(downloadableData)}
                  />
                  <span className="mx-2 text-md font-medium">All Data</span>
                </span>
              }
              key="1"
            >
              {!isEmpty(downloadableData) ? (
                <CheckboxGroup
                  className={`grid md:grid-cols-2 gap-2 `}
                  options={exportData.map((value) => {
                    return { label: toTitleCase(value), value: value };
                  })}
                  value={finalChecklist}
                  onChange={onChangeCheckBox}
                />
              ) : (
                <Empty />
              )}
            </Panel>
          </Collapse>
        )}
      </div>
    </Modal>
  );
};

export default ExportJson;
