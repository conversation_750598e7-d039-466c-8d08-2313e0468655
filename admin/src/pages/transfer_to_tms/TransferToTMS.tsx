import { Button, notification, Table, Tag } from "antd";
import ButtonGroup from "antd/es/button/button-group";
import Search from "antd/es/input/Search";
import Paragraph from "antd/es/typography/Paragraph";
import dayjs from "dayjs";
import isEmpty from "is-empty";
import { useCallback, useEffect, useState } from "react";
import { RiSendPlane2Line } from "react-icons/ri";
import { Outlet, useNavigate, useSearchParams } from "react-router-dom";
import { useExportUserForTttLazyQuery, useGetUserForTttQuery } from "../../__generated__";
import ErrorComponent from "../../components/error/Error";
import { ProtectedRoute } from "../../hooks/useSecureRoutePermissions";
import { debounce } from "../../utils/utils";
import { FilterOutlined } from "@ant-design/icons";
import TMSXferModal from "./TMSXferModal";
import { FiDownload } from "react-icons/fi";

type TranferToTMSFilter = "all" | "yes" | "no";

interface FilterValues {
  startDate: string;
  endDate: string;
  tranferToTMS: TranferToTMSFilter;
}

const TransferToTMS = () => {
  //Hooks
  const [searchParams, setSearchParams] = useSearchParams();
  const navigate = useNavigate();
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [isFilterApplied, setIsFilterApplied] = useState<boolean>(false);
  const [openFilterModal, setOpenFilterModal] = useState(false);
  const [filterValue, setFilterValue] = useState<FilterValues>({
    tranferToTMS: "all",
    startDate: "",
    endDate: ""
  });

  //graphql queries
  const {
    data: userData,
    loading,
    error
  } = useGetUserForTttQuery({
    variables: {
      search: searchParams.get("search") || undefined,
      filter: {
        transfer_to_tms_status:
          searchParams.get("transfer_to_tms_status") === "true"
            ? true
            : searchParams.get("transfer_to_tms_status") === "false"
            ? false
            : undefined,
        startDate: searchParams.get("start_date"),
        endDate: searchParams.get("end_date")
      },
      pagination: {
        take: pageSize,
        skip: currentPage - 1
      }
    },
    fetchPolicy: "cache-and-network"
  });

  const [exportTTT, { loading: exportLoading }] = useExportUserForTttLazyQuery();

  // Calculate the total number of data pages based on search results
  const totalDataPages = Math.ceil(userData?.getUserForTTT?.total_count || 0 / pageSize);

  //Handle Debounce Search
  const debounceSearch = useCallback(
    debounce((val: string, status: string) => {
      setCurrentPage(1);
      if (!isEmpty(status)) {
        setSearchParams({ search: val, transfer_to_tms_status: status });
      } else {
        setSearchParams({ search: val });
      }
    }, 500),
    []
  );

  const handleCurrentPage = (page: number, pageSize: number) => {
    setCurrentPage(page);
    setPageSize(pageSize);
  };

  const handleOpenFilterModal = () => {
    setOpenFilterModal(true);
  };

  const handleUpdateFilterValue = (newFilter: {
    transferDate?: [string, string];
    tranferToTMS: string;
  }) => {
    const { tranferToTMS, transferDate } = newFilter;

    const newFilterValues: FilterValues = {
      startDate: transferDate?.[0] || "",
      endDate: transferDate?.[1] || "",
      tranferToTMS: (tranferToTMS as TranferToTMSFilter) || "all"
    };

    // Update URL params with filters
    const params: Record<string, string> = {};

    if (newFilterValues.startDate && newFilterValues.endDate) {
      // Convert Date objects to strings for URL parameters
      params.start_date = dayjs(newFilterValues.startDate).format("YYYY-MM-DD");
      params.end_date = dayjs(newFilterValues.endDate).format("YYYY-MM-DD");
    } else {
      params.start_date = "";
      params.end_date = "";
    }

    switch (tranferToTMS) {
      case "all":
        params.transfer_to_tms_status = "";
        break;
      case "yes":
        params.transfer_to_tms_status = "true";
        break;
      case "no":
        params.transfer_to_tms_status = "false";
        break;
      default:
        break;
    }

    setCurrentPage(1);

    setFilterValue({
      startDate: newFilterValues.startDate,
      endDate: newFilterValues.endDate,
      tranferToTMS: newFilter.tranferToTMS as TranferToTMSFilter
    });

    setSearchParams((prevParams) => {
      const merged = {
        ...Object.fromEntries(prevParams),
        ...params
      };
      return merged;
    });
  };

  const handleExport = async () => {
    await exportTTT({
      variables: {
        search: searchParams.get("search") || undefined,
        filter: {
          transfer_to_tms_status:
            searchParams.get("transfer_to_tms_status") === "true"
              ? true
              : searchParams.get("transfer_to_tms_status") === "false"
              ? false
              : undefined,
          startDate: searchParams.get("start_date"),
          endDate: searchParams.get("end_date")
        }
      },
      fetchPolicy: "no-cache",
      onCompleted() {
        notification.success({
          message: "You will receive an email containing the export data soon"
        });
      },
      onError: (error) => {
        notification.error({ message: error.message });
      }
    });
  };

  useEffect(() => {
    const transfer_to_tms_status = searchParams.get("transfer_to_tms_status") ?? "all";
    const startDate = searchParams.get("start_date") ?? "";
    const endDate = searchParams.get("end_date") ?? "";
    setFilterValue({
      startDate,
      endDate,
      tranferToTMS: transfer_to_tms_status as TranferToTMSFilter
    });
  }, []);

  useEffect(() => {
    // Check if any filterValue is present and set isFilterApplied accordingly
    if (
      filterValue.tranferToTMS == "yes" ||
      filterValue.tranferToTMS == "no" ||
      (filterValue.startDate && filterValue.startDate !== "") ||
      (filterValue.endDate && filterValue.endDate !== "")
    ) {
      setIsFilterApplied(true);
    } else {
      setIsFilterApplied(false);
    }
  }, [filterValue]);

  if (error) {
    return <ErrorComponent error={error} />;
  }

  return (
    <ProtectedRoute disableChildAuth parent_url="/dashboard/transfer_to_tms">
      {({ permissions }) => (
        <>
          <div>
            <div className="text-xl mt-2 flex items-center p-2 mb-3">
              <RiSendPlane2Line className=" text-purple-500 text-3xl  mr-4" />{" "}
              <span className="font-semibold">
                Onboarding -{" "}
                <Tag color={"green"}>({userData?.getUserForTTT?.total_count ?? 0})</Tag>
              </span>
            </div>
            <div className="flex justify-between mb-3">
              <div className="flex gap-2">
                <Search
                  allowClear
                  placeholder="Search by name, email, phone"
                  defaultValue={searchParams.get("search") as string}
                  onChange={(e) => {
                    const status = searchParams.get("transfer_to_tms_status");
                    debounceSearch(e.target.value, status);
                  }}
                />
                <ButtonGroup>
                  <Button
                    onClick={handleOpenFilterModal}
                    icon={<FilterOutlined className="text-blue-500" />}
                    className={`flex items-center ${
                      isFilterApplied === true ? "ant-btn-primary" : "ant-btn-default"
                    }`}
                  >
                    Filter
                  </Button>
                </ButtonGroup>
              </div>
              <div>
                <Button
                  className="text-colorPrimary flex items-center"
                  icon={<FiDownload className="text-colorPrimary " />}
                  onClick={handleExport}
                  disabled={!permissions?.adminData?.can_download}
                  loading={exportLoading}
                >
                  Export
                </Button>
              </div>
            </div>
          </div>
          <Table
            loading={loading}
            pagination={{
              current: currentPage,
              pageSize,
              onChange: (page, pageSize) => {
                handleCurrentPage(page, pageSize);
              },
              total: totalDataPages || 1
            }}
            dataSource={userData?.getUserForTTT?.data as Array<any>}
            columns={[
              {
                title: "Name",
                width: 150,
                fixed: "left",
                dataIndex: "name",
                key: "name",
                align: "center",
                render: (data: any, item: any) => {
                  return <p> {item.name}</p>;
                }
              },
              {
                title: "Phone",
                width: 150,
                dataIndex: "phone",
                key: "phone",
                align: "center"
              },
              {
                title: "Email",
                width: 150,
                dataIndex: "email",
                key: "email",
                align: "center",
                render: (data: any, item: any) => {
                  return <p> {item.email || "-"}</p>;
                }
              },
              {
                title: "Hiring Criteria",
                width: 150,
                dataIndex: "hiring_criteria",
                key: "hiring_criteria",
                align: "center",
                render: (_: any, item: any) => {
                  return (
                    <div className="capitalize">
                      {item.user_onboarding_data?.[0]?.hiring_criteria
                        ? item.user_onboarding_data?.[0]?.hiring_criteria
                            .toLowerCase()
                            .replace(/_/g, " ")
                        : "-"}
                    </div>
                  );
                }
              },
              {
                title: "Designation",
                width: 150,
                dataIndex: "designation",
                key: "designation",
                align: "center",
                render: (_: any, item: any) => {
                  return <>{item?.designation?.name || "-"} </>;
                }
              },
              {
                title: "Salary Offered",
                width: 150,
                dataIndex: "salary_offered",
                key: "salary_offered",
                align: "center",
                render: (_: any, item: any) => {
                  return <>{item.user_onboarding_data?.[0]?.salary_offered || "-"} </>;
                }
              },
              {
                title: "Onboarded",
                width: 150,
                dataIndex: "transferred_to_tms",
                key: "transferred_to_tms",
                align: "center",
                render: (_: any, item: any) => {
                  return (
                    <Tag color={item.transfer_to_tms_status ? "green" : "red"}>
                      {item.transfer_to_tms_status === true ? "Yes" : "No"}
                    </Tag>
                  );
                }
              },
              {
                title: "Onboarded Date",
                width: 150,
                dataIndex: "transfer_to_tms_date",
                key: "transfer_to_tms_date",
                render: (_: any, item: any) => {
                  return (
                    <p>
                      {item.user_onboarding_data?.[0]?.meta?.transfer_to_tms_date
                        ? dayjs(item.user_onboarding_data[0]?.meta?.transfer_to_tms_date).format(
                            "MMM DD, YYYY"
                          )
                        : "-"}
                    </p>
                  );
                }
              },
              {
                title: "Assessed By",
                width: 150,
                dataIndex: "assessed_by",
                key: "assessed_by",
                align: "center",
                render: (_: any, item: any) => {
                  return <>{item.user_onboarding_data?.[0]?.assessed_by || "-"} </>;
                }
              },
              {
                title: "Date Of Joining",
                width: 150,
                dataIndex: "date_of_joining",
                key: "date_of_joining",
                align: "center",
                render: (_: any, item: any) => {
                  const date =
                    item &&
                    item.user_onboarding_data &&
                    item.user_onboarding_data[0] &&
                    item.user_onboarding_data[0].date_of_joining
                      ? new Date(item.user_onboarding_data[0].date_of_joining).toLocaleDateString()
                      : null;
                  return <>{date ? date.toString() : "-"} </>;
                }
              },
              {
                title: "Other Remark",
                width: 150,
                dataIndex: "remark",
                key: "remark",
                align: "center",
                render: (_: any, item: any) => {
                  return <>{item.user_onboarding_data?.[0]?.remark || "-"} </>;
                }
              },
              {
                title: "Address As Per Aadhar",
                width: 150,
                dataIndex: "aadhar_address",
                key: "aadhar_address",
                align: "center",
                render: (_: any, item: any) => {
                  return (
                    <Paragraph ellipsis={{ rows: 2, expandable: true, symbol: "more" }}>
                      {item.user_onboarding_data?.[0]?.aadhar_address || "-"}{" "}
                    </Paragraph>
                  );
                }
              },
              {
                title: "Employee Id",
                width: 150,
                dataIndex: "employee_id",
                key: "employee_id",
                align: "center",
                render: (_: any, item: any) => {
                  return <>{item.user_onboarding_data?.[0]?.employee_id || "-"} </>;
                }
              },
              {
                title: "User Bank Details ",
                width: 150,
                dataIndex: "bank_details",
                align: "center",
                children: [
                  {
                    title: "Bank Name",
                    width: 150,
                    dataIndex: "bank_name",
                    key: "bank_name",
                    render: (_: any, item: any) => {
                      return <>{item.bank_details?.bank_name || "-"} </>;
                    }
                  },
                  {
                    title: "Account Number",
                    width: 150,
                    dataIndex: "account_number",
                    key: "account_number",
                    render: (_: any, item: any) => {
                      return <>{item.bank_details?.account_number || "-"} </>;
                    }
                  },
                  {
                    title: "IFSC Code",
                    width: 150,
                    dataIndex: "ifsc",
                    key: "ifsc",
                    render: (_: any, item: any) => {
                      return <>{item.bank_details?.ifsc || "-"} </>;
                    }
                  }
                ]
              },
              {
                title: "Action",
                width: 175,
                key: "id",
                fixed: "right",
                dataIndex: "id",
                align: "center",
                render: (value: number) => (
                  <div>
                    <Button
                      disabled={!permissions.update}
                      type="primary"
                      onClick={() =>
                        navigate(`/dashboard/transfer_to_tms/add_technician?tab=1&user_id=${value}`)
                      }
                    >
                      View/Update
                    </Button>
                  </div>
                )
              }
            ]}
            size="small"
            bordered
            scroll={{ x: "calc(700px + 50%)" }}
          />
          <Outlet />
          {openFilterModal && (
            <TMSXferModal
              openFilterModal={openFilterModal}
              closeModal={() => setOpenFilterModal(false)}
              filterValue={{
                tranferToTMS: filterValue.tranferToTMS,
                transferDate:
                  filterValue?.startDate && filterValue?.endDate
                    ? [
                        dayjs(filterValue?.startDate, "YYYY-MM-DD"),
                        dayjs(filterValue?.endDate, "YYYY-MM-DD")
                      ]
                    : []
              }}
              applyFilters={handleUpdateFilterValue}
            />
          )}
        </>
      )}
    </ProtectedRoute>
  );
};

export default TransferToTMS;
