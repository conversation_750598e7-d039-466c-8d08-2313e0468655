import { CloseOutlined } from "@ant-design/icons";
import { <PERSON><PERSON>, DatePicker, Form, Modal, Radio } from "antd";
import { Dayjs } from "dayjs";
import React from "react";

interface Pros {
  openFilterModal: boolean;
  closeModal: () => void;
  filterValue: {
    transferDate?: [Dayjs, Dayjs] | [];
    tranferToTMS?: string;
  };
  applyFilters: (value: { transferDate: [string, string]; tranferToTMS: string }) => void;
}

const TMSXferModal: React.FC<Pros> = ({
  openFilterModal,
  closeModal,
  filterValue,
  applyFilters
}) => {
  const [form] = Form.useForm();
  const tranferToTMSValue = Form.useWatch("tranferToTMS", form);

  const handleResetFilters = () => {
    handleClearFilters();
  };

  const handleClearFilters = () => {
    form.setFieldsValue({ tranferToTMS: "all", transferDate: [] });
  };

  const handleRadioChange = (e: any) => {
    const value = e.target.value;
    if (value !== "yes") {
      form.setFieldsValue({ transferDate: [] });
    } else {
      // Keep the Previous value
    }
  };

  return (
    <>
      <Modal
        title={<div className="text-center text-purple-500 text-xl mb-4"> Add Filter </div>}
        className="text-center text-purple-500 text-xl mb-4"
        open={openFilterModal}
        onCancel={closeModal}
        footer={null}
        closeIcon={<CloseOutlined />}
        centered
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={(values) => {
            applyFilters(values);
            closeModal();
          }}
          initialValues={{
            ...filterValue,
            tranferToTMS:
              filterValue?.tranferToTMS === undefined || filterValue?.tranferToTMS === ""
                ? "all"
                : filterValue?.tranferToTMS === "true"
                ? "yes"
                : filterValue?.tranferToTMS === "false"
                ? "no"
                : filterValue?.tranferToTMS
          }}
        >
          <Form.Item label="Onboarding Status" name="tranferToTMS">
            <Radio.Group onChange={handleRadioChange}>
              <Radio value="all">All</Radio>
              <Radio value="yes">Yes</Radio>
              <Radio value="no">No</Radio>
            </Radio.Group>
          </Form.Item>
          <Form.Item label="Onboarded Date" name="transferDate">
            <DatePicker.RangePicker
              disabled={tranferToTMSValue !== "yes"}
              style={{ width: "100%" }}
              format="DD/MM/YYYY"
            />
          </Form.Item>

          <div className="flex justify-end mt-6">
            <Button onClick={handleResetFilters} className="mr-2">
              Clear Filter
            </Button>
            <Button type="primary" htmlType="submit" className="bg-purple-600">
              Apply Filters
            </Button>
          </div>
        </Form>
      </Modal>
    </>
  );
};

export default TMSXferModal;
