import {
  BackwardOutlined,
  DeleteOutlined,
  FileExcelOutlined,
  SwapOutlined,
  UserDeleteOutlined
} from "@ant-design/icons";
import {
  Avatar,
  Button,
  Card,
  Divider,
  Dropdown,
  MenuProps,
  Modal,
  Radio,
  Space,
  Tabs,
  Tag,
  Tooltip,
  notification
} from "antd";
import TextArea from "antd/es/input/TextArea";
import React, { useEffect, useState } from "react";
import { BsArrowLeft } from "react-icons/bs";
import { RiSendPlane2Line } from "react-icons/ri";
import { Outlet, useLocation, useNavigate, useParams, useSearchParams } from "react-router-dom";
import {
  FulfillmentType,
  Gender,
  GetAllUsersDocument,
  GetUserForTttDocument,
  OnboardingStage,
  UserDetailsQueryDocument,
  useDeleteUserAdminMutation,
  useGetUserChecklistDataQuery,
  useUpdateUserAdminMutation,
  useUserDetailsQueryLazyQuery
} from "../../__generated__";
import ErrorComponent from "../../components/error/Error";
import Loading from "../../components/loading/Loading";
import TransferToTMSModal from "../../components/readyForTMS/TransferToTMSModal";
import SwitchTechnicianToSPModal from "../../components/user/SwitchTechnicianToSPModal";
import OnboardingStageDropdown from "../../components/userDetails/OnboardingStageDropdown";
import { ProtectedRoute, useSecureRoutePermissions } from "../../hooks/useSecureRoutePermissions";
import { getUserProfileSrc } from "../../utils/utils";
import EditUserModal from "./EditUserModal";
import Impersonification from "./impersonify";
import GeneralDetailsTab from "./user-details/GeneralDetailsTab";
import WorkExperience from "./WorkExperience";

const UserDetails: React.FC = () => {
  const params = useParams();
  const [searchParams, setSearchParams] = useSearchParams();
  const [activeKey, setActiveKey] = useState("general_details");
  const navigate = useNavigate();

  //Hooks
  const [editUserModalOpen, setEditUserModalOpen] = useState(false);
  const [showRejectModal, setShowRejectModal] = useState(false);
  const [buttonType, setButtonType] = useState("");
  const [remarks, setRemarks] = useState("");
  const [skillsId, setSkillsId] = useState<number[]>([]);
  const [showTTTModal, setShowTTTModal] = useState(false);
  const [showSwitchToSPModal, setShowSwitchToSPModal] = useState(false);
  const pagePermissions = useSecureRoutePermissions();
  const [disablePageLevelAuth, setDisablePageLevelAuth] = useState(false);
  const location = useLocation();

  //GraphQL query
  const [fetchUserDetails, { data, error, loading, refetch }] = useUserDetailsQueryLazyQuery({
    variables: {
      userId: Number(params.user_id)
    }
  });
  const {
    data: userChecklistData,
    loading: checklistLoading,
    error: checklistError
  } = useGetUserChecklistDataQuery({
    variables: {
      userId: Number(params.user_id),
      userType: undefined
    },
    fetchPolicy: "cache-and-network"
  });
  //Graphql Mutation
  const [deleteUser] = useDeleteUserAdminMutation();
  const [updateUser] = useUpdateUserAdminMutation();

  const handleUserUpdate = async (value: string) => {
    try {
      await updateUser({
        variables: {
          data: {
            work_address: data?.userDetailsForAdmin?.location?.work_address,
            city: data?.userDetailsForAdmin?.location?.city,
            email: data?.userDetailsForAdmin?.email,
            gender: data?.userDetailsForAdmin?.gender,
            landmark: data?.userDetailsForAdmin?.location?.landmark,
            name: data?.userDetailsForAdmin?.name,
            skills: skillsId,
            phone: data?.userDetailsForAdmin?.phone,
            pincode: data?.userDetailsForAdmin?.location?.pincode,
            state: data?.userDetailsForAdmin?.location?.state,
            location_id: data?.userDetailsForAdmin?.location?.id,
            remark: remarks,
            user_state: value
          },
          userId: data?.userDetailsForAdmin?.id
        },
        refetchQueries: [UserDetailsQueryDocument],
        onCompleted() {
          notification.success({
            message: "User updated successfully"
          });
        },
        onError(error) {
          notification.error({
            message: error.message
          });
        }
      });
    } catch (error) {
      console.error(error);
    }
  };

  const handleDeleteUser = async () => {
    try {
      await deleteUser({
        variables: {
          userId: Number(params.user_id)
        },
        refetchQueries: [GetAllUsersDocument, GetUserForTttDocument],
        awaitRefetchQueries: true,
        onCompleted() {
          notification.success({
            message: "User deleted successfully"
          });
          navigate("/dashboard/users/");
        },
        onError(error) {
          notification.error({
            message: error.message
          });
        }
      });
    } catch (error) {
      console.error(error);
    }
  };

  const items: MenuProps["items"] = [
    {
      label: "Move to SP",
      key: "move_to_sp",
      icon: <SwapOutlined />,
      onClick: () => {
        setShowSwitchToSPModal(true);
      },
      disabled: data?.userDetailsForAdmin?.user_type === "SERVICE_PROVIDER"
    },
    {
      label: data?.userDetailsForAdmin?.transfer_to_tms_done ? (
        "Transferred"
      ) : (
        <div>
          <Tooltip
            placement="left"
            title={
              data?.userDetailsForAdmin?.transfer_to_tms_done ||
              !userChecklistData?.getUserChecklistData?.data?.some(
                (item) => item?.user_checklist_data?.[0]?.hiring_completed
              )
                ? data?.userDetailsForAdmin?.transfer_to_tms_done
                  ? "User is already transferred to TMS."
                  : "Complete onboarding checklist "
                : ""
            }
          >
            <span>Transfer to TMS</span>
          </Tooltip>
        </div>
      ),

      key: "ttt",
      icon: <RiSendPlane2Line />,
      onClick: () => {
        setShowTTTModal(true);
        setSearchParams({ tab: "1", user_id: params.user_id || "-1" });
      },
      //The user will not be allowed to Onboard unless at least one of the check box is checked
      disabled:
        data?.userDetailsForAdmin?.transfer_to_tms_done ||
        !userChecklistData?.getUserChecklistData?.data?.some(
          (item) => item?.user_checklist_data?.[0]?.hiring_completed
        )
    },
    {
      label: "Delete User",
      key: "delete",
      icon: <DeleteOutlined />,
      danger: true,
      onClick: () => {
        Modal.confirm({
          onOk: handleDeleteUser,
          okButtonProps: {
            danger: true
          },
          title: "Delete user?",
          content: (
            <span>
              Deleting this user will permanently remove all associated data and cannot be undone.
              Are you absolutely sure you want to proceed?
            </span>
          )
        });
      },
      disabled: !pagePermissions?.permissions?.delete
    },
    {
      label: "Reject",
      key: "reject",
      icon: <UserDeleteOutlined />,
      danger: true,
      onClick: () => {
        setButtonType("Reject");
        setShowRejectModal(true);
      },
      disabled: !pagePermissions?.permissions?.adminData?.can_reject
    },
    {
      label: "Ban",
      key: "ban",
      icon: <FileExcelOutlined />,
      danger: true,
      onClick: () => {
        setButtonType("Ban");
        setShowRejectModal(true);
      },
      disabled: !pagePermissions?.permissions?.adminData?.can_ban
    }
  ];

  const menuProps = {
    items
  };

  useEffect(() => {
    //This whole document is not written by me
    fetchUserDetails({
      variables: {
        userId: Number(params.user_id)
      }
    }).then((response) => {
      const { data } = response;
      const expertiseIds: any = data?.userDetailsForAdmin?.expertise?.map((e) => e?.id);
      setSkillsId(expertiseIds);
    });
    refetch();
  }, []);
  useEffect(() => {
    const isUserDetailsExactPath = /^\/dashboard\/users\/\d+$/.test(location.pathname);
    setDisablePageLevelAuth(!isUserDetailsExactPath);
  }, [location.pathname]);
  useEffect(() => {
    const tab = searchParams.get("tab");
    if (tab) {
      setActiveKey(tab);
    }
  }, [searchParams]);

  if (error || checklistError) {
    return <ErrorComponent error={error} />;
  }

  if (loading || checklistLoading) {
    return <Loading tip="Loading ..." />;
  }
  // const regex = /^\/dashboard\/users\/\d+$/;

  return (
    <ProtectedRoute disablePageLevelAuth={disablePageLevelAuth}>
      {({ permissions }) => (
        <div className="min-h-[calc(100vh-244px)] max-h-[calc(100vh-244px)] mt-[180px] fixed   ">
          {/* <div className=" z-10 bg-pink-300"> */}
          <>{console.log(permissions, "permissions new")}</>
          <div className="fixed top-[80px] flex flex-col items-start z-10   min-w-[85%] h-[180px]">
            <Button
              onClick={() => navigate("/dashboard/users/")}
              className="mb-2"
              icon={<BackwardOutlined />}
            >
              Go back
            </Button>
            <div
              onClick={() => navigate("/dashboard/users/")}
              className="hidden text-2xl items-center gap-4 mb-6 cursor-pointer hover:opacity-60 "
            >
              <BsArrowLeft />
              <span>Go back to users</span>
            </div>
            <div className="flex w-full justify-between p-10 bg-white ">
              <div>
                <Avatar
                  className="w-16 h-16 mr-4"
                  src={getUserProfileSrc(
                    data?.userDetailsForAdmin?.gender || Gender.NotSpecified,
                    data?.userDetailsForAdmin?.photoUrl || ""
                  )}
                />
                <Space direction="vertical">
                  <div>
                    <div className="flex space-y-3 space-x-8">
                      <div className="text-3xl">{data?.userDetailsForAdmin?.name}</div>
                      {data?.userDetailsForAdmin?.user_state != null && (
                        <div>
                          <Tag color={"red"}>
                            {data?.userDetailsForAdmin?.user_state == "Ban" ? "Banned" : "Rejected"}
                          </Tag>
                        </div>
                      )}
                    </div>

                    <div className="text-colorPrimary font-bold">
                      {/*we can handle service provider also in future */}
                      {data?.userDetailsForAdmin?.fulfillment_type == FulfillmentType.InHouse
                        ? "In House"
                        : "Contractor"}{" "}
                      - {data?.userDetailsForAdmin?.designation?.name}
                    </div>
                  </div>
                  <div>
                    <b>Phone</b> - {data?.userDetailsForAdmin?.phone}
                  </div>
                </Space>
              </div>
              <div className="mr-6 mt-4 flex items-center space-x-2">
                <div>
                  <OnboardingStageDropdown
                    stageValue={
                      data?.userDetailsForAdmin?.onboarding_stage ||
                      ("NOT_STARTED" as OnboardingStage)
                    }
                    userId={data?.userDetailsForAdmin?.id || -1}
                    refetch={refetch}
                  />
                </div>
                <Dropdown.Button
                  buttonsRender={([leftButton, rightButton]) => [
                    <Tooltip
                      title={!permissions.update ? "You don't have permission to edit" : ""}
                      key="leftButton"
                    >
                      {React.cloneElement(leftButton as React.ReactElement, {
                        disabled: !permissions.update
                      })}
                    </Tooltip>,

                    React.cloneElement(rightButton as React.ReactElement, {})
                  ]}
                  menu={menuProps}
                  onClick={() => setEditUserModalOpen(true)}
                >
                  Edit
                </Dropdown.Button>
              </div>
            </div>
          </div>
          <div className="">
            <Card className={`bg-gray-50  border-0  `}>
              <Tabs
                activeKey={activeKey}
                className="user-tab  min-w-[80%]  max-w-[89%] z-50  cursor-pointer "
                defaultActiveKey={searchParams?.get("tab") || ""}
                onChange={(key) => {
                  setActiveKey(key);
                  if (key == "assessment") {
                    navigate("assignment");
                  } else if (key == "interview") {
                    navigate("interview");
                  } else if (key == "documents") {
                    navigate("document");
                  } else if (key == "training") {
                    navigate("training");
                  } else if (key == "compliance") {
                    navigate("compliance");
                  } else {
                    navigate("");
                  }
                }}
                items={[
                  {
                    label: "General Details",
                    key: "general_details",
                    children: (
                      <GeneralDetailsTab
                        setActiveKey={setActiveKey}
                        isTransferred={data?.userDetailsForAdmin?.transfer_to_tms_done}
                      />
                    )
                  },
                  {
                    label: "Work Experience",
                    key: "work_experience",
                    children: <WorkExperience />
                  },
                  {
                    label: "Documents",
                    key: "documents",
                    children: <Outlet />
                  },
                  {
                    label: "Interview",
                    key: "interview",
                    children: <Outlet />
                  },
                  {
                    label: "Assessment",
                    key: "assessment",
                    children: <Outlet />
                  },
                  {
                    label: "Training",
                    key: "training",
                    children: <Outlet />
                  },
                  {
                    label: "Compliance",
                    key: "compliance",
                    children: (
                      <div
                        className="flex bg-white  w-[100%] overflow-y-auto custom-scroll-compliance"
                        style={{ maxHeight: "calc(100vh - 300px)" }}
                      >
                        <Outlet />
                      </div>
                    )
                  },
                  {
                    label: "Technician View",
                    key: "impersonification",
                    children: (
                      <div
                        className="flex  w-[calc(100vw-20%)] overflow-y-scroll"
                        style={{ maxHeight: "calc(100vh - 300px)" }}
                      >
                        <Impersonification />
                      </div>
                    )
                  }
                ]}
              />
            </Card>
            <EditUserModal
              user_id={data?.userDetailsForAdmin?.id || -1}
              open={editUserModalOpen}
              setOpen={setEditUserModalOpen}
            />
            <Modal
              onCancel={() => setShowRejectModal(false)}
              title="Reject Reasons"
              open={showRejectModal}
              footer={null}
            >
              <div>
                <Radio.Group
                  onChange={(e) => {
                    setRemarks(e.target.value);
                  }}
                >
                  <Space direction="vertical">
                    <Radio value="User is not properly authorized">
                      User is not properly authorized
                    </Radio>
                    <Radio value="User has police case">User has police case</Radio>
                    <Radio value="Data mismatch with the given data ">
                      Data mismatch with the given data
                    </Radio>
                  </Space>
                </Radio.Group>
                <Divider>Or</Divider>
                <TextArea
                  onChange={(e) => {
                    setRemarks(e.target.value);
                  }}
                  className="w-full"
                  placeholder="Type your reason to reject this document"
                />
                <Button
                  type="primary"
                  htmlType="submit"
                  onClick={() => {
                    handleUserUpdate(buttonType);
                    setShowRejectModal(false);
                  }}
                  className="mt-3"
                >
                  Submit
                </Button>
              </div>
            </Modal>
            {showTTTModal && (
              <TransferToTMSModal
                open={showTTTModal}
                onClose={() => setShowTTTModal(false)}
                isFromUsersComponent={true}
              />
            )}
            {showSwitchToSPModal && (
              <SwitchTechnicianToSPModal
                userId={Number(params.user_id)}
                userName={data?.userDetailsForAdmin?.name || ""}
                userDetails={data?.userDetailsForAdmin}
                isOpen={showSwitchToSPModal}
                onClose={() => {
                  refetch();
                  setShowSwitchToSPModal(false);
                }}
                onSuccess={() => {
                  notification.success({
                    message: "Individual user successfully moved to Service Provider"
                  });
                }}
              />
            )}
          </div>
        </div>
      )}
    </ProtectedRoute>
  );
};

export default UserDetails;
