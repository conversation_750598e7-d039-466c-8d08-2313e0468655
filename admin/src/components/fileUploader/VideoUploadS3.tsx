import { DeleteOutlined, LoadingOutlined, UploadOutlined } from "@ant-design/icons";
import { Button, Upload, message, notification } from "antd";
import { RcFile } from "antd/es/upload";
import axios from "axios";
import { isEmpty } from "lodash";
import React, { useEffect, useState } from "react";
import { convertSecondIntoTimeFrUser } from "../../utils/utils";

interface VideoUploaderToS3Props {
  setVideoUrl: (url: string) => void;
  setVideoName: (name: string) => void;
  setVideoType: (type: string) => void;
  setVideoDuration?: (duration: number) => void;
  videoUrl?: string;
}

const VideoUploaderS3: React.FC<VideoUploaderToS3Props> = ({
  setVideoUrl,
  setVideoName,
  setVideoType,
  setVideoDuration,
  videoUrl
}) => {
  const [video, setVideo] = useState("");
  const [videoLoading, setLoading] = useState(false);
  const [video_duration_in_seconds, setVideoDurationInSeconds] = useState<number | null>(null);

  useEffect(() => {
    if (!isEmpty(videoUrl)) {
      setVideo(videoUrl as string);
    }
  }, [videoUrl]);

  const uploadFileToS3 = async (file: File): Promise<string> => {
    try {
      setLoading(true);
      const response = await axios.post(
        `${import.meta.env.VITE_BACKEND_URL}/upload/signed-url`,
        {
          file_type: file.type
        },
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem("adminToken")}`
          }
        }
      );

      const { status, data: signedUrl, Key } = response.data;

      if (status) {
        await axios.put(signedUrl, file, {
          headers: {
            "Content-Type": file.type
          }
        });

        const url = import.meta.env.VITE_S3_URL + "/" + Key;
        setVideo(url);
        setVideoUrl(url);
        setVideoDuration && setVideoDuration(video_duration_in_seconds || -1);
        setLoading(false);
        setVideoName(file.name);
        setVideoType(file.type || "");
        return url;
      } else {
        console.error(response);
        notification.error({
          message: "There was some issue during uploading the file. Please try again later."
        });
      }
    } catch (err) {
      console.error(err);
      notification.error({
        message: "There was some issue during uploading the file. Please try again later."
      });
    }

    return "";
  };

  const beforeUpload = (file: RcFile) => {
    const isMp4OrWebm = file.type === "video/mp4" || file.type === "video/webm";
    if (!isMp4OrWebm) {
      message.error("You can only upload MP4 file or WebM file!");
      return false;
    }
    //File Size Upto 100MB
    const isLt2M = file.size / 1024 / 1024 < 100;
    if (!isLt2M) {
      message.error("video must smaller than 100MB!");
      return false;
    }
    handleDuration(file);
    return isMp4OrWebm && isLt2M;
  };

  const handleDuration = (file: RcFile) => {
    //get the video duration
    const video = document.createElement("video");
    video.src = URL.createObjectURL(file);

    video.preload = "metadata";

    video.onloadedmetadata = () => {
      const duration = Math.ceil(video.duration); //round off to next second
      setVideoDurationInSeconds(duration as unknown as number);
    };
  };

  const uploadButton = (
    <>
      <Button
        hidden={video ? true : false}
        icon={videoLoading ? <LoadingOutlined /> : <UploadOutlined />}
      >
        Upload
      </Button>
      <p className="text-gray-400 text-xs mt-2">
        {" "}
        You can only upload MP4 file or WebM file , upto 100MB
      </p>
    </>
  );

  return (
    <Upload
      listType="text"
      disabled={video ? true : false}
      showUploadList={false}
      multiple={false}
      customRequest={async (info) => {
        const beforeUploadResponse = beforeUpload(info.file as RcFile);
        if (!beforeUploadResponse) {
          return;
        }
        const file = (info.file as any).originFileObj || info.file;
        await uploadFileToS3(file);
      }}
    >
      {!isEmpty(video) ? (
        <div className="flex">
          <Button
            onClick={() => {
              setVideoUrl("deleted");
              setVideo("");
              setVideoName("deleted");
            }}
            className="text-green-500"
            type="dashed"
            icon={<DeleteOutlined className="text-red-500" />}
          >
            Uploaded
          </Button>
          <p className="ml-2 text-gray-400 mt-2 text-xs">
            {video_duration_in_seconds && convertSecondIntoTimeFrUser(video_duration_in_seconds)}
          </p>
        </div>
      ) : (
        uploadButton
      )}
    </Upload>
  );
};

export default VideoUploaderS3;
