import { DeleteOutlined, LoadingOutlined, UploadOutlined } from "@ant-design/icons";
import { Button, Image, Tooltip, Upload, message, notification } from "antd";
import { RcFile } from "antd/es/upload";
import axios from "axios";
import React, { useEffect, useState } from "react";

interface ImageUploaderToS3Props {
  setImageUrl: (url: string) => void;
  text?: string;
  imgUrl?: string;
}

const ImageUploaderS3: React.FC<ImageUploaderToS3Props> = ({ setImageUrl, text, imgUrl }) => {
  const [image, setImage] = useState("");
  const [imageLoading, setLoading] = useState(false);

  const uploadFileToS3 = async (file: File): Promise<string> => {
    try {
      setLoading(true);
      const response = await axios.post(
        `${import.meta.env.VITE_BACKEND_URL}/upload/signed-url`,
        {
          file_type: file.type
        },
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem("adminToken")}`
          }
        }
      );

      const { status, data: signedUrl, Key } = response.data;

      if (status) {
        await axios.put(signedUrl, file, {
          headers: {
            "Content-Type": file.type
          }
        });

        const url = import.meta.env.VITE_S3_URL + "/" + Key;
        setImage(url);
        setImageUrl(url);
        setLoading(false);
        return url;
      } else {
        console.error(response);
        notification.error({
          message: "There was some issue during uploading the file. Please try again later."
        });
      }
    } catch (err) {
      console.error(err);
      notification.error({
        message: "There was some issue during uploading the file. Please try again later."
      });
    }

    return "";
  };

  const beforeUpload = (file: RcFile) => {
    const isJpgOrPng = file.type === "image/jpeg" || file.type === "image/png";
    if (!isJpgOrPng) {
      message.error("You can only upload JPG/PNG file!");
    }
    const isLt2M = file.size / 1024 / 1024 < 10;
    if (!isLt2M) {
      message.error("Image must smaller than 10MB!");
    }
    return isJpgOrPng && isLt2M;
  };

  const uploadButton = (
    <button
      style={{ border: 0, background: "none", cursor: "pointer" }}
      type="button"
      hidden={image ? true : false}
    >
      {imageLoading ? <LoadingOutlined /> : <UploadOutlined />}
      <div style={{ marginTop: 8 }} className="">
        {text ? text : "Click Here to Upload Photo"}
      </div>
    </button>
  );

  useEffect(() => {
    if (imgUrl) {
      setImage(imgUrl);
    }
  }, [imgUrl]);

  return (
    <Upload
      listType="picture-card"
      className="avatar-uploader" //it has no effect
      disabled={image ? true : false}
      showUploadList={false}
      multiple={false}
      customRequest={async (info) => {
        const beforeUploadResponse = beforeUpload(info.file as RcFile);
        if (!beforeUploadResponse) {
          return;
        }
        const file = (info.file as any).originFileObj || info.file;
        await uploadFileToS3(file);
      }}
    >
      {image ? (
        <>
          <div className="relative">
            <Image src={image} alt="no image" width={"100px"} height={"100px"} />
            <div className="absolute top-0 right-0">
              <Tooltip title="Remove Image"></Tooltip>
            </div>
            <Button
              className="absolute top-0 right-0"
              icon={
                <DeleteOutlined
                  className="text-red-500"
                  onClick={() => {
                    setImageUrl("deleted");
                    setImage("");
                    //TODO: Remove file from S3
                  }}
                />
              }
              size="small"
            />
          </div>
        </>
      ) : (
        uploadButton
      )}
    </Upload>
  );
};

export default ImageUploaderS3;
