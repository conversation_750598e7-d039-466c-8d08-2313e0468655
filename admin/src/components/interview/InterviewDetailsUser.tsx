import {
  Button,
  DatePicker,
  Descriptions,
  Input,
  Modal,
  Popconfirm,
  Switch,
  Tabs,
  Tag,
  Typography,
  notification
} from "antd";
import { RangePickerProps } from "antd/es/date-picker";
import dayjs from "dayjs";
import React, { useEffect, useState } from "react";
import { useParams } from "react-router-dom";
import {
  FeedBackState,
  GetUserInterviewDetailsDocument,
  GetUserInterviewDetailsForAdminDocument,
  GetUserInterviewsDocument,
  InterviewState,
  InterviewType,
  UpdateInterviewType,
  useGetUserInterviewDetailsForAdminQuery,
  useUpdateInterviewModeOfUserMutation,
  useUpdateUserInterviewDetailsMutation
} from "../../__generated__";
import { useIntervieweeFeedbackStatus } from "../../hooks/useIntervieweeFeedbackStatus";
import Loading from "../loading/Loading";
import InterviewFeedbackOfAUser from "./InterviewFeedbackOfUser";
import IntervieweeFeedbackOfUser from "./IntervieweeFeedbackOfUser";
const { Paragraph } = Typography;

const InterviewDetails: React.FC = () => {
  //Hooks
  const [interviewUpdateReason, setInterviewUpdateReason] = useState("");
  const [disableActionButtons, setDisableActionButtons] = useState<boolean>(false);
  const [selectedInterviewTime, setSelectedInterviewTime] = useState<RangePickerProps["value"]>();
  //Params
  const { user_id, interview_id } = useParams();

  const { data, loading, error } = useGetUserInterviewDetailsForAdminQuery({
    fetchPolicy: "no-cache",
    variables: {
      interviewId: Number(interview_id)
    },
    onCompleted(data) {
      // Disable action buttons if interview is cancelled, completed, or expired
      if (
        data?.getUserInterviewDetailsForAdmin?.status === InterviewState.Cancelled ||
        data?.getUserInterviewDetailsForAdmin?.status === InterviewState.Completed ||
        data?.getUserInterviewDetailsForAdmin?.status === InterviewState.Expired
      ) {
        setDisableActionButtons(true);
      } else {
        setDisableActionButtons(false);
      }
    }
  });

  const userInterviewDetails = data?.getUserInterviewDetailsForAdmin;

  // Get interviewee feedback status
  const { feedbackState: intervieweeFeedbackState } = useIntervieweeFeedbackStatus(
    userInterviewDetails?.feedback?.feedback_form_id
  );

  // Update UI when interview status changes
  useEffect(() => {
    if (
      userInterviewDetails?.status === InterviewState.Cancelled ||
      userInterviewDetails?.status === InterviewState.Completed ||
      userInterviewDetails?.status === InterviewState.Expired
    ) {
      setDisableActionButtons(true);
    } else {
      setDisableActionButtons(false);
    }
  }, [userInterviewDetails?.status]);

  //GraphQl Mutation
  const [updateInterviewDetails] = useUpdateUserInterviewDetailsMutation();
  const [updateInterviewMode, { loading: updateInterviewModeLoading }] =
    useUpdateInterviewModeOfUserMutation();

  //👇🏻 All Functions below this 👇🏻
  // Handle Google Calendar errors
  const handleCalendarError = (error: any) => {
    if (error.message.includes("Google Calendar Event is Expired or Deleted")) {
      // Show a confirmation dialog to the user
      return true;
    }
    notification.error({ message: error.message });
    return false;
  };

  // Update interview status in database only (when Google Calendar event is missing)
  const updateInterviewStatusOnly = async (
    status: InterviewState,
    isActive: boolean,
    note?: string
  ) => {
    try {
      // Make a direct API call to update the interview status in the database
      // This is a simplified version that doesn't try to update the Google Calendar event
      await updateInterviewDetails({
        variables: {
          data: {
            update_type: UpdateInterviewType.Update,
            user_id: Number(user_id),
            event_id: "NONE", // Use a placeholder since the event doesn't exist
            interview_status: status,
            is_active: isActive,
            ...(note ? { note } : {})
          }
        },
        onError(error) {
          notification.error({ message: error.message });
        },
        onCompleted() {
          notification.success({ message: `Interview ${status} Successfully` });
          // Update the disableActionButtons state to reflect the new status
          setDisableActionButtons(true);
        },
        refetchQueries: [
          GetUserInterviewDetailsForAdminDocument,
          GetUserInterviewsDocument,
          GetUserInterviewDetailsDocument
        ]
      });
    } catch (error) {
      console.error(error);
    }
  };

  //Handle Interview Cancel
  const handleInterviewCancellation = async (eventId: string) => {
    try {
      await updateInterviewDetails({
        variables: {
          data: {
            update_type: UpdateInterviewType.Cancel,
            user_id: Number(user_id),
            event_id: eventId,
            interview_status: InterviewState.Cancelled,
            is_active: false
          }
        },
        onError(error) {
          if (handleCalendarError(error)) {
            // If it's a Google Calendar error, confirm with the user and update only the database
            Modal.confirm({
              title: "Google Calendar Event Not Found",
              content:
                "The Google Calendar event for this interview no longer exists. Would you like to cancel the interview in the system only?",
              onOk: () =>
                updateInterviewStatusOnly(InterviewState.Cancelled, false, interviewUpdateReason),
              okText: "Yes, Cancel Interview",
              cancelText: "No"
            });
          }
        },
        onCompleted() {
          notification.success({ message: "Interview Cancelled Successfully" });
          // Update the disableActionButtons state to reflect the new status
          setDisableActionButtons(true);
        },
        refetchQueries: [
          GetUserInterviewDetailsForAdminDocument,
          GetUserInterviewsDocument,
          GetUserInterviewDetailsDocument
        ]
      });
    } catch (error) {
      console.error(error);
    }
  };

  //Handle Interview Update
  const handleInterviewUpdate = async (eventId: string) => {
    try {
      const startDate = selectedInterviewTime?.[0];
      const endDate = selectedInterviewTime?.[1];

      if (!startDate || !endDate) {
        notification.error({ message: "Please select start and end times for the interview" });
        return;
      }

      await updateInterviewDetails({
        variables: {
          data: {
            update_type: UpdateInterviewType.Update,
            user_id: Number(user_id),
            event_id: eventId,
            start: startDate,
            end: endDate,
            interview_status: InterviewState.Rescheduled,
            is_active: true,
            ...(interviewUpdateReason ? { note: interviewUpdateReason } : {})
          }
        },
        onError(error) {
          if (handleCalendarError(error)) {
            // If it's a Google Calendar error, confirm with the user and update only the database
            Modal.confirm({
              title: "Google Calendar Event Not Found",
              content:
                "The Google Calendar event for this interview no longer exists. Would you like to reschedule the interview in the system only?",
              onOk: () =>
                updateInterviewStatusOnly(InterviewState.Rescheduled, true, interviewUpdateReason),
              okText: "Yes, Reschedule Interview",
              cancelText: "No"
            });
          }
        },
        onCompleted() {
          notification.success({ message: "Interview Updated Successfully" });
          // Update the disableActionButtons state to reflect the new status
          setDisableActionButtons(true);
        },
        refetchQueries: [
          GetUserInterviewDetailsForAdminDocument,
          GetUserInterviewsDocument,
          GetUserInterviewDetailsDocument
        ]
      });
    } catch (error) {
      console.error(error);
    }
  };

  const handleCompleteInterview = async (eventId: string) => {
    try {
      await updateInterviewDetails({
        variables: {
          data: {
            update_type: UpdateInterviewType.Update,
            user_id: Number(user_id),
            event_id: eventId,
            interview_status: InterviewState.Completed,
            is_active: false,
            ...(interviewUpdateReason ? { note: interviewUpdateReason } : {})
          }
        },
        onError(error) {
          if (handleCalendarError(error)) {
            // If it's a Google Calendar error, confirm with the user and update only the database
            Modal.confirm({
              title: "Google Calendar Event Not Found",
              content:
                "The Google Calendar event for this interview no longer exists. Would you like to mark the interview as completed in the system only?",
              onOk: () =>
                updateInterviewStatusOnly(InterviewState.Completed, false, interviewUpdateReason),
              okText: "Yes, Complete Interview",
              cancelText: "No"
            });
          }
        },
        onCompleted() {
          notification.success({ message: "Interview Updated Successfully" });

          // Update the disableActionButtons state to reflect the new status
          setDisableActionButtons(true);
        },
        refetchQueries: [
          GetUserInterviewDetailsForAdminDocument,
          GetUserInterviewsDocument,
          GetUserInterviewDetailsDocument
        ]
      });
    } catch (error) {
      console.error(error);
    }
  };

  if (loading) {
    return <Loading tip="Loading..." />;
  }
  if (error) {
    return <></>;
  }

  return (
    <div className="">
      <Tabs animated className=" text-lg font-bold">
        <Tabs.TabPane tab={"Details"} key={"interview"}>
          <Descriptions bordered column={2}>
            <Descriptions.Item span={6} label="Scheduled By">
              {userInterviewDetails?.scheduled_by?.name}
            </Descriptions.Item>
            <Descriptions.Item label="Interview date and time">
              {" "}
              {dayjs(userInterviewDetails?.start_time).format("dddd, MMMM D, YYYY h:mm A")}
              {"-"}
              {dayjs(userInterviewDetails?.end_time).format("h:mm A")}
            </Descriptions.Item>
            <Descriptions.Item label="Created on">
              {dayjs(userInterviewDetails?.created_at).format("dddd, MMMM D, YYYY h:mm A")}
            </Descriptions.Item>
            <Descriptions.Item label="Interviewer Feedback Status">
              <Tag
                color={
                  userInterviewDetails?.feedback?.interviewer_feedback_state ===
                  FeedBackState.Completed
                    ? "green"
                    : "orange"
                }
              >
                {userInterviewDetails?.feedback?.interviewer_feedback_state || "Not Available"}{" "}
              </Tag>
            </Descriptions.Item>
            <Descriptions.Item label="Interviewer Feedback Link">
              <Button
                type="link"
                onClick={() => {
                  window.open(
                    `/feedbacks/submit/${
                      userInterviewDetails?.feedback?.feedback_form_id || "Invalid"
                    }`
                  );
                }}
              >
                {userInterviewDetails?.feedback?.feedback_form_id ? "Open" : ""}
              </Button>
            </Descriptions.Item>
            <Descriptions.Item label="Interviewee feedback status">
              <Tag
                color={
                  intervieweeFeedbackState === FeedBackState.Completed
                    ? "green"
                    : intervieweeFeedbackState === FeedBackState.Pending
                    ? "orange"
                    : "red"
                }
              >
                {intervieweeFeedbackState || "Not Available"}{" "}
              </Tag>
            </Descriptions.Item>

            <Descriptions.Item label="Interviewer Name " span={1}>
              {userInterviewDetails?.interviewer?.name || "Not Available"}
            </Descriptions.Item>
            <Descriptions.Item label="Interviewer Email" span={1}>
              {userInterviewDetails?.interviewer?.email || "Not Available"}
            </Descriptions.Item>
            <Descriptions.Item label="Meet Link">
              {userInterviewDetails?.interview_type === InterviewType.InterviewOnline ? (
                <Paragraph copyable>
                  {`https://meet.google.com/${userInterviewDetails?.google_meet_link}`}
                </Paragraph>
              ) : (
                `-`
              )}
            </Descriptions.Item>
            <Descriptions.Item label="Mode" span={2}>
              <Switch
                checkedChildren={"Online"}
                unCheckedChildren={"Offline"}
                defaultChecked={
                  userInterviewDetails?.interview_type === InterviewType.InterviewOnline
                    ? true
                    : false
                }
                loading={updateInterviewModeLoading}
                onChange={async (checked: boolean) => {
                  await updateInterviewMode({
                    variables: {
                      data: {
                        interview_id: Number(interview_id),
                        is_online: checked ? true : false
                      }
                    },
                    refetchQueries: [GetUserInterviewDetailsForAdminDocument]
                  });
                }}
              />
            </Descriptions.Item>

            <Descriptions.Item label="Actions">
              <div className="flex gap-2">
                <Popconfirm
                  showCancel={false}
                  disabled={disableActionButtons}
                  title={
                    <>
                      <p>Are you sure to Cancel The Interview</p>
                      <Input
                        placeholder="Cancellation Reason"
                        value={interviewUpdateReason}
                        onChange={(e) => {
                          setInterviewUpdateReason(e.target.value);
                        }}
                      />
                    </>
                  }
                  onConfirm={() =>
                    handleInterviewCancellation(userInterviewDetails?.google_meet_link as string)
                  }
                  okText="Confirm"
                  cancelText="Cancel"
                >
                  <Button
                    disabled={disableActionButtons}
                    className="cursor-pointer text-red-600 py-1 px-2 rounded-md"
                  >
                    Cancel
                  </Button>
                </Popconfirm>
                <Popconfirm
                  disabled={disableActionButtons}
                  showCancel={false}
                  title={
                    <>
                      <p>Complete Interview</p>
                      <Input
                        value={interviewUpdateReason}
                        onChange={(e) => {
                          setInterviewUpdateReason(e.target.value);
                        }}
                        placeholder="Feedback"
                      />
                    </>
                  }
                  onConfirm={() =>
                    handleCompleteInterview(userInterviewDetails?.google_meet_link as string)
                  }
                  okText="Confirm"
                >
                  <Button
                    disabled={disableActionButtons}
                    className="cursor-pointer  text-green-600 py-1 px-2 rounded-md"
                  >
                    Complete
                  </Button>
                </Popconfirm>

                <Popconfirm
                  showCancel={false}
                  disabled={disableActionButtons}
                  title={
                    <>
                      <p>Reschedule Interview</p>
                      <DatePicker.RangePicker
                        onChange={(time) => setSelectedInterviewTime(time)}
                        showTime={{ format: "HH:mm", use12Hours: true }}
                      />
                    </>
                  }
                  onConfirm={() =>
                    handleInterviewUpdate(userInterviewDetails?.google_meet_link as string)
                  }
                  okText="Confirm"
                >
                  <Button
                    disabled={disableActionButtons}
                    className="cursor-pointer  text-purple-600 py-1 px-2 rounded-md"
                  >
                    Reschedule
                  </Button>
                </Popconfirm>
              </div>
            </Descriptions.Item>
            <Descriptions.Item label="Comments" span={1}>
              {userInterviewDetails?.note || "-"}
            </Descriptions.Item>
          </Descriptions>
        </Tabs.TabPane>
        <Tabs.TabPane
          tab={"Interviewer Feedback"}
          key={"interviewer_feedback"}
          disabled={
            userInterviewDetails?.feedback?.interviewer_feedback_state === FeedBackState.Pending
              ? true
              : false
          }
        >
          {userInterviewDetails?.feedback?.interviewer_feedback_state === FeedBackState.Completed &&
            userInterviewDetails?.feedback?.id && (
              <InterviewFeedbackOfAUser feedback_id={userInterviewDetails?.feedback.id} />
            )}
        </Tabs.TabPane>
        <Tabs.TabPane
          tab={"Interviewee feedback"}
          key={"interviewee_feedback"}
          disabled={intervieweeFeedbackState !== FeedBackState.Completed}
        >
          {userInterviewDetails?.feedback?.feedback_form_id &&
            intervieweeFeedbackState === FeedBackState.Completed && (
              <IntervieweeFeedbackOfUser
                feedback_form_id={userInterviewDetails?.feedback.feedback_form_id}
              />
            )}
        </Tabs.TabPane>
      </Tabs>
    </div>
  );
};

export default InterviewDetails;
