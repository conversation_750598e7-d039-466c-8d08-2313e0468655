import { Button, Empty, Tag } from "antd";
import dayjs from "dayjs";
import React, { useEffect } from "react";
import { BsCalendarPlus, BsPersonFill } from "react-icons/bs";
import { HiOutlineTemplate } from "react-icons/hi";
import { useNavigate, useParams } from "react-router-dom";
import { useGetUserInterviewsQuery } from "../../__generated__";
import { setInterviewStatusColor } from "../../utils/utils";
import Loading from "../loading/Loading";

const InterviewScheduledList: React.FC<{ permissions: any }> = () => {
  //Hooks
  const { user_id, interview_id } = useParams();
  //navigation
  const navigate = useNavigate();
  //Graphql Query
  const { data: interviewsResponse, loading } = useGetUserInterviewsQuery({
    fetchPolicy: "no-cache",
    variables: {
      userId: Number(user_id)
    }
  });
  const interviewData = interviewsResponse?.getUserInterviews;
  console.log(interviewData);
  useEffect(() => {
    if (!interview_id && interviewData) {
      navigate(interviewData[0]?.id + "");
    }
  }, [interviewData]);

  if (loading) {
    return <Loading tip="Loading..." />;
  }
  return (
    <div className="py-4 px-3  ">
      <div className="flex items-center  w-full pb-3 text-lg font-bold">
        <div className="flex min-w-[209px] max-w-[209px] w-full ">
          {interviewData?.length === 0 ? "No Interview Scheduled" : "Available Interviews"}
        </div>

        <Button
          type="primary"
          icon={<BsCalendarPlus />}
          onClick={() => navigate(`schedule`)}
          className=""
        >
          Schedule New Interview
        </Button>
      </div>

      {interviewData?.length === 0 ? (
        <>
          <div className="flex">
            <Empty description={"No Interview Data "} />
          </div>
        </>
      ) : (
        <>
          {interviewData?.map((interview) => {
            return (
              <div className="space-y-10" onClick={() => navigate(interview?.id + "")}>
                <div
                  className={`p-3 m-2 rounded-md border border-solid  bg-white hover:border-purple-400 hover:shadow-md cursor-pointer hover:bg-purple-50 ${
                    interview?.id == interview_id ? "border-purple-400" : "border-gray-200"
                  }`}
                >
                  <div className="flex justify-start items-center mb-[6px]">
                    <HiOutlineTemplate className="text-purple-500 mr-2" />
                    <div className="text-gray-600 font-semibold">
                      {interview?.feedback?.template?.title || "WIFY Office"}
                    </div>
                  </div>
                  <div className="font-[400] ">
                    {dayjs(interview?.start_time).format("dddd, MMMM D, YYYY h:mm A")}
                  </div>
                  <div className="flex justify-between items-center ">
                    <small className="flex items-center gap-2">
                      <BsPersonFill fill="purple" />
                      <div className="text-purple-500">{interview?.interviewer?.name}</div>
                    </small>
                    <Tag color={setInterviewStatusColor(interview?.status)}>
                      {interview?.status}
                    </Tag>
                  </div>
                </div>
              </div>
            );
          })}
        </>
      )}
    </div>
  );
};

export default InterviewScheduledList;
