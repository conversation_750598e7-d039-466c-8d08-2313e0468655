import { DeleteOutlined, LoadingOutlined, PlusOutlined } from "@ant-design/icons";
import {
  Button,
  Col,
  Divider,
  Form,
  Image,
  Input,
  Modal,
  Row,
  Select,
  message,
  notification
} from "antd";
import { useForm } from "antd/es/form/Form";
import FormItem from "antd/es/form/FormItem";
import TextArea from "antd/es/input/TextArea";
import { RcFile } from "antd/es/upload";
import Upload from "antd/es/upload/Upload";
import axios from "axios";
import React, { useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import {
  AssignmentUserType,
  GetAllQuestionsDocument,
  QuestionDifficulty,
  useGetAllBucketsQuery,
  useGetAllQuestionsQuery,
  useGetQuestionQuery,
  useUpdateQuestionMutation
} from "../../__generated__";
import { hasDuplicates } from "../../utils/utils";

const SpUpdateQuestionModal: React.FC = () => {
  //hooks
  const [selectedBucketId, setSelectedBucketId] = useState("");
  const [image, setImageUrl] = useState("");
  const [imageLoading, setLoading] = useState(false);

  //navigate
  const navigate = useNavigate();

  //form
  const [form] = useForm();
  const { id } = useParams();

  const { data: buckets } = useGetAllBucketsQuery({
    variables: {
      data: {
        assign_to: AssignmentUserType.ServiceProvider
      }
    }
  });

  //Query
  useGetAllQuestionsQuery({
    variables: {
      filter: {
        bucket_id: id
      }
    },
    onCompleted(data) {
      console.log(data.getAllQuestions[0]);
    }
  });

  useGetQuestionQuery({
    variables: {
      questionId: id
    },
    onCompleted(data) {
      const correct_option = data?.getQuestion?.option?.filter(
        (option: any) => option?.is_right_option === true
      );
      form.setFieldsValue({
        question: data?.getQuestion?.question_text,
        bucket: data?.getQuestion?.bucket?.name,
        difficulty: data?.getQuestion?.difficulty,
        correct_option: correct_option?.[0]?.option_text,
        option_1: data?.getQuestion?.option?.[0]?.option_text,
        option_2: data?.getQuestion?.option?.[1]?.option_text,
        option_3: data?.getQuestion?.option?.[2]?.option_text,
        option_4: data?.getQuestion?.option?.[3]?.option_text
      });
      setImageUrl((data.getQuestion?.images && data.getQuestion?.images[0]) || "");
      setSelectedBucketId(data?.getQuestion?.bucket?.id || "");
    }
  });

  //Mutation
  const [updateQuestion, { loading }] = useUpdateQuestionMutation();

  const handleModalCancel = () => {
    navigate(-1);
  };
  const handleOnSubmit = (values: any) => {
    const { option_1, option_2, option_3, option_4, correct_option } = values;
    const optionarray = [option_1, option_2, option_3, option_4];
    if (hasDuplicates(...optionarray)) {
      message.error("Options must have unique values");
      return;
    }
    const options = [
      {
        option_text: option_1,
        is_right_option: correct_option === option_1
      },
      {
        option_text: option_2,
        is_right_option: correct_option === option_2
      },
      {
        option_text: option_3,
        is_right_option: correct_option === option_3
      },
      {
        option_text: option_4,
        is_right_option: correct_option === option_4
      }
    ];

    updateQuestion({
      variables: {
        question_id: id,
        data: {
          bucket_id: selectedBucketId,
          difficulty: values.difficulty,
          options: options,
          question_text: values.question,
          images: image ? [image] : image === "" ? [] : undefined
        }
      },
      onCompleted() {
        message.success("Question updated successfully");
        navigate(-1);
      },
      onError(err) {
        console.error(err);
        message.error("There was some error while updating the question.");
      },
      refetchQueries: [GetAllQuestionsDocument]
    });
  };

  const uploadFileToS3 = async (file: File): Promise<string> => {
    try {
      setLoading(true);
      const response = await axios.post(
        `${import.meta.env.VITE_BACKEND_URL}/upload/signed-url`,
        {
          file_type: file.type
        },
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem("adminToken")}`
          }
        }
      );

      const { status, data: signedUrl, Key } = response.data;

      if (status) {
        await axios.put(signedUrl, file, {
          headers: {
            "Content-Type": file.type
          }
        });

        const url = import.meta.env.VITE_S3_URL + "/" + Key;
        setImageUrl(url);
        setLoading(false);
        return url;
      } else {
        console.error(response);
        notification.error({
          message: "There was some issue during uploading the file. Please try again later."
        });
      }
    } catch (err) {
      console.error(err);
      notification.error({
        message: "There was some issue during uploading the file. Please try again later."
      });
    }

    return "";
  };

  const beforeUpload = (file: RcFile) => {
    const isJpgOrPng = file.type === "image/jpeg" || file.type === "image/png";
    if (!isJpgOrPng) {
      message.error("You can only upload JPG/PNG file!");
    }
    const isLt2M = file.size / 1024 / 1024 < 5;
    if (!isLt2M) {
      message.error("Image must smaller than 5MB!");
    }
    return isJpgOrPng && isLt2M;
  };

  const uploadButton = (
    <button style={{ border: 0, background: "none" }} type="button" hidden={image ? true : false}>
      {imageLoading ? <LoadingOutlined /> : <PlusOutlined />}
      <div style={{ marginTop: 8 }}>Image </div>
    </button>
  );
  return (
    <>
      <Modal
        closeIcon={false}
        centered
        open
        onCancel={handleModalCancel}
        onOk={() => form.submit()}
        okText="Update"
        width={1000}
        okButtonProps={{ loading: loading }}
      >
        <div className="text-xl mb-2"> Update Question</div>

        <Form form={form} onFinish={handleOnSubmit}>
          <div className="flex justify-start items-end gap-2 w-full">
            <div className="w-10/12 ">
              <FormItem
                rules={[
                  {
                    required: true,
                    message: "Question is required"
                  }
                ]}
                name={"question"}
                label={"Question"}
              >
                <TextArea rows={4} placeholder="Question" allowClear />
              </FormItem>
            </div>
            <div className="w-2/12">
              <FormItem name={"image"}>
                <Upload
                  listType="picture-card"
                  className="avatar-uploader"
                  disabled={image ? true : false}
                  showUploadList={false}
                  multiple={false}
                  customRequest={async (info) => {
                    const beforeUploadResponse = beforeUpload(info.file as RcFile);
                    if (!beforeUploadResponse) {
                      return;
                    }
                    const file = (info.file as any).originFileObj || info.file;
                    await uploadFileToS3(file);
                  }}
                >
                  {image ? (
                    <>
                      <Image src={image} alt="no image" width={"100%"} />
                      <Button
                        icon={
                          <DeleteOutlined
                            onClick={() => {
                              setImageUrl("");
                              //TODO: Remove file from S3
                            }}
                          />
                        }
                      />
                    </>
                  ) : (
                    uploadButton
                  )}
                </Upload>
              </FormItem>
            </div>
          </div>
          <div className="text-xs text-gray-500 mb-6 w-full text-end">
            Image must smaller than 5MB! with JPG/PNG
          </div>
          <Row gutter={10}>
            <Col span={12}>
              <FormItem
                rules={[
                  {
                    required: true,
                    message: "Option this required"
                  }
                ]}
                name={"option_1"}
                label={"Option 1"}
              >
                <Input allowClear />
              </FormItem>
            </Col>
            <Col span={12}>
              <FormItem
                rules={[
                  {
                    required: true,
                    message: "Option this required"
                  }
                ]}
                name={"option_2"}
                label={"Option 2"}
              >
                <Input allowClear />
              </FormItem>
            </Col>
          </Row>
          <Row gutter={10}>
            <Col span={12}>
              <FormItem
                rules={[
                  {
                    required: true,
                    message: "Option this required"
                  }
                ]}
                name={"option_3"}
                label={"Option 3"}
              >
                <Input allowClear />
              </FormItem>
            </Col>
            <Col span={12}>
              <FormItem
                rules={[
                  {
                    required: true,
                    message: "Option this required"
                  }
                ]}
                name={"option_4"}
                label={"Option 4"}
              >
                <Input allowClear />
              </FormItem>
            </Col>
          </Row>
          <Divider />
          <Row gutter={10}>
            <Col span={12}>
              <FormItem
                rules={[
                  { required: true, message: "This cannot be empty" },
                  ({ getFieldValue }) => ({
                    validator: (_, value) => {
                      const options = [
                        getFieldValue("option_1"),
                        getFieldValue("option_2"),
                        getFieldValue("option_3"),
                        getFieldValue("option_4")
                      ];
                      if (options.includes(value) || value === "") {
                        return Promise.resolve();
                      } else {
                        return Promise.reject("Correct answer should be one of the options");
                      }
                    }
                  })
                ]}
                name={"correct_option"}
                label={"Correct answer"}
              >
                <Input allowClear />
              </FormItem>
            </Col>
            <Col span={12}>
              <FormItem
                name={"bucket"}
                label={"Bucket"}
                rules={[{ required: true, message: "Please select a bucket" }]}
              >
                <Select
                  value={selectedBucketId} // Display the bucket name in the form
                  onChange={(value) => setSelectedBucketId(value)} // Update the selectedBucket state when the selection changes
                  options={buckets?.getAllBuckets?.data?.map((b) => ({
                    value: b?.id,
                    label: b?.name
                  }))}
                />
              </FormItem>
            </Col>
          </Row>
          <Row gutter={10}>
            <Col span={12}>
              <FormItem name={"difficulty"} label={"Difficulty"}>
                <Select
                  options={[
                    {
                      value: QuestionDifficulty.Hard,
                      label: "Hard"
                    },
                    {
                      value: QuestionDifficulty.Medium,
                      label: "Medium"
                    },
                    {
                      value: QuestionDifficulty.Easy,
                      label: "Easy"
                    }
                  ]}
                />
              </FormItem>
            </Col>
          </Row>
        </Form>
      </Modal>
    </>
  );
};

export default SpUpdateQuestionModal;
