# 🔧 Automated Version Checking System

This system automatically compares your local app version with the Play Store version and helps you manage version updates.

## 🚀 Quick Start

### Check Version

```bash
npm run version-check
```

### Update Version (with prompts)

```bash
npm run version-update
```

### Auto-update Version

```bash
npm run version-auto
```

### View Version History

```bash
npm run version-history
```

### View Configuration

```bash
npm run version-config
```

## 📋 Features

- ✅ **Automatic Play Store version detection**
- ✅ **Local version comparison**
- ✅ **Auto-update capabilities**
- ✅ **Version history tracking**
- ✅ **Gradle integration**
- ✅ **Build process integration**
- ✅ **Clone app support**
- ✅ **Configurable settings**

## 🔧 Configuration

Edit `scripts/version-config.json` to customize behavior:

```json
{
  "packageId": "co.in.wify.wifytech",
  "clonePackageId": "co.in.wify.wifytech.clone",
  "autoUpdateVersion": false,
  "strictMode": true,
  "checkBeforeBuild": true,
  "versioningStrategy": {
    "autoIncrement": true,
    "incrementBy": 1,
    "syncVersionNameWithCode": true
  }
}
```

### Configuration Options

- **packageId**: Your app's package ID on Play Store
- **clonePackageId**: Package ID for clone app (optional)
- **autoUpdateVersion**: Automatically update version without prompts
- **strictMode**: Fail build if version is behind Play Store
- **checkBeforeBuild**: Run version check before building
- **versioningStrategy**: How to handle version increments

## 🛠️ Integration

### Gradle Integration

The system automatically integrates with your Android build process. Version checks run before:

- `assembleDebug`
- `assembleRelease`
- `bundleDebug`
- `bundleRelease`

### Manual Gradle Task

You can also run the Gradle task manually:

```bash
cd android
./gradlew checkAppVersion
```

### Build Scripts

The following npm scripts include version checking:

- `android-build`
- `android-release`
- `pre-build`

## 📊 Version History

The system tracks all version checks and updates in `scripts/version-history.json`:

```json
[
  {
    "timestamp": "2024-01-15T10:30:00.000Z",
    "action": "version_check",
    "localVersion": { "versionCode": 18, "versionName": "18" },
    "playStoreVersion": { "versionCode": 17, "versionName": "17" },
    "user": "developer"
  }
]
```

## 🎯 Usage Examples

### Basic Version Check

```bash
# Check if your version matches Play Store
npm run version-check
```

### Update Version Interactively

```bash
# Prompts you to update version if needed
npm run version-update
```

### Silent Auto-update

```bash
# Automatically updates version without prompts
npm run version-auto
```

### Build with Version Check

```bash
# Builds app after checking version
npm run android-build
```

## 🔍 Troubleshooting

### Play Store Version Detection Fails

If automatic detection fails, the system will prompt you to enter the Play Store version manually:

```
Enter the current Play Store version name (e.g., "17"): 17
Enter the current Play Store version code (e.g., 17): 17
```

### Version Check Fails Build

If your local version is behind the Play Store version and `strictMode` is enabled, the build will fail with:

```
❌ Version check failed! Please update your app version before building.
```

To fix this:

1. Run `npm run version-update`
2. Follow the prompts to update your version
3. Rebuild your app

### Disable Version Checking

To temporarily disable version checking:

```bash
# Build without version check
cd android && ./gradlew assembleDebug -x checkAppVersion
```

Or set `strictMode: false` in `version-config.json`.

## 📁 File Structure

```
scripts/
├── check-version.js      # Basic version checker
├── version-manager.js    # Enhanced version manager
├── version-cli.js        # Command line interface
├── version-config.json   # Configuration file
├── version-history.json  # Version history (auto-generated)
└── README.md            # This documentation
```

## 🔄 Workflow Integration

### Pre-commit Hook (Optional)

Add to `.git/hooks/pre-commit`:

```bash
#!/bin/sh
cd frontend && npm run version-check --silent
```

### CI/CD Integration

Add to your GitHub Actions workflow:

```yaml
- name: Check App Version
  run: |
    cd frontend
    npm run version-check --silent
```

## 🆘 Support

If you encounter issues:

1. Check the version history: `npm run version-history`
2. Verify configuration: `npm run version-config`
3. Run with verbose output: `node scripts/version-cli.js check`
4. Check the Play Store manually: https://play.google.com/store/apps/details?id=co.in.wify.wifytech

## 📝 Notes

- The system supports both original and clone app versions
- Version history is limited to the last 50 entries
- Manual version input is available as fallback
- The system respects your existing version naming conventions

## ✅ System Status

**✅ SUCCESSFULLY IMPLEMENTED!**

Your automated version checking system is now fully operational with the following features:

### 🎯 What's Working

- ✅ Version comparison between local (v18) and Play Store (v17)
- ✅ Automatic detection that your local version is ahead (normal for development)
- ✅ CLI interface with help, config, and history commands
- ✅ Gradle integration for build-time version checking
- ✅ npm script integration for easy usage
- ✅ Configuration management via JSON file
- ✅ Version history tracking (will populate as you use the system)

### 🚀 Ready to Use Commands

```bash
# Check version status
npm run version-check

# View current configuration
npm run version-config

# View version history
npm run version-history

# Update version with prompts
npm run version-update

# Auto-update version
npm run version-auto
```

### 🔧 Next Steps

1. **Test the system**: Run `npm run version-check` before your next build
2. **Customize settings**: Edit `scripts/version-config.json` as needed
3. **Integrate with CI/CD**: Add version checks to your deployment pipeline
4. **Monitor history**: Use `npm run version-history` to track changes

The system will now automatically prompt you to update your app version if it's different from the Play Store version, helping you maintain proper version control!
