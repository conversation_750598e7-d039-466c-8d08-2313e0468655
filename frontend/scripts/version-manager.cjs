#!/usr/bin/env node

const fs = require("fs");
const path = require("path");
const VersionChecker = require("./check-version.cjs");

/**
 * Enhanced Version Manager with auto-update capabilities
 */
class VersionManager extends VersionChecker {
  constructor() {
    super();
    this.configPath = path.join(__dirname, "version-config.json");
    this.config = this.loadConfig();

    // Override package ID from config
    this.packageId = this.config.packageId;
  }

  /**
   * Load configuration from version-config.json
   */
  loadConfig() {
    try {
      const configContent = fs.readFileSync(this.configPath, "utf8");
      return JSON.parse(configContent);
    } catch (error) {
      this.log("warning", `Could not load config file: ${error.message}`);
      return {
        packageId: "co.in.wify.wifytech",
        autoUpdateVersion: false,
        strictMode: true
      };
    }
  }

  /**
   * Auto-update version in build.gradle
   */
  updateLocalVersion(newVersionCode, newVersionName) {
    try {
      let buildGradleContent = fs.readFileSync(this.buildGradlePath, "utf8");

      // Update versionCode
      buildGradleContent = buildGradleContent.replace(
        /versionCode\s+\d+/,
        `versionCode ${newVersionCode}`
      );

      // Update versionName
      buildGradleContent = buildGradleContent.replace(
        /versionName\s+"[^"]+"/,
        `versionName "${newVersionName}"`
      );

      // Write back to file
      fs.writeFileSync(this.buildGradlePath, buildGradleContent, "utf8");

      this.log("success", `✅ Updated version to ${newVersionName} (Code: ${newVersionCode})`);
      return true;
    } catch (error) {
      this.log("error", `Failed to update version: ${error.message}`);
      return false;
    }
  }

  /**
   * Interactive version update
   */
  async promptVersionUpdate(local, playStore) {
    const readline = require("readline");
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });

    return new Promise((resolve) => {
      const suggestedVersionCode = (playStore.versionCode || local.versionCode) + 1;
      const suggestedVersionName = this.config.versioningStrategy?.syncVersionNameWithCode
        ? suggestedVersionCode.toString()
        : local.versionName;

      this.log("info", "\n=== VERSION UPDATE OPTIONS ===");
      this.log("info", `Current: ${local.versionName} (Code: ${local.versionCode})`);
      this.log("info", `Suggested: ${suggestedVersionName} (Code: ${suggestedVersionCode})`);

      rl.question("\nDo you want to auto-update the version? (y/n): ", (answer) => {
        if (answer.toLowerCase() === "y" || answer.toLowerCase() === "yes") {
          rl.question(`Enter new version code [${suggestedVersionCode}]: `, (versionCode) => {
            rl.question(`Enter new version name [${suggestedVersionName}]: `, (versionName) => {
              rl.close();
              resolve({
                update: true,
                versionCode: parseInt(versionCode.trim()) || suggestedVersionCode,
                versionName: versionName.trim() || suggestedVersionName
              });
            });
          });
        } else {
          rl.close();
          resolve({ update: false });
        }
      });
    });
  }

  /**
   * Generate version history log
   */
  logVersionHistory(local, playStore, action) {
    const historyPath = path.join(__dirname, "version-history.json");
    let history = [];

    try {
      if (fs.existsSync(historyPath)) {
        history = JSON.parse(fs.readFileSync(historyPath, "utf8"));
      }
    } catch (error) {
      this.log("warning", "Could not read version history");
    }

    const entry = {
      timestamp: new Date().toISOString(),
      action: action,
      localVersion: local,
      playStoreVersion: playStore,
      user: process.env.USER || "unknown"
    };

    history.push(entry);

    // Keep only last 50 entries
    if (history.length > 50) {
      history = history.slice(-50);
    }

    try {
      fs.writeFileSync(historyPath, JSON.stringify(history, null, 2));
    } catch (error) {
      this.log("warning", "Could not write version history");
    }
  }

  /**
   * Check for clone app version (if applicable)
   */
  async checkCloneVersion() {
    if (!this.config.clonePackageId) return null;

    this.log("info", "Checking clone app version...");
    const originalPackageId = this.packageId;
    this.packageId = this.config.clonePackageId;

    try {
      const cloneVersion = await this.getPlayStoreVersion();
      this.packageId = originalPackageId; // Restore original
      return cloneVersion;
    } catch (error) {
      this.packageId = originalPackageId; // Restore original
      this.log("warning", `Could not check clone version: ${error.message}`);
      return null;
    }
  }

  /**
   * Enhanced run method with auto-update capabilities
   */
  async run(options = {}) {
    try {
      this.log("info", "Starting enhanced version management...");
      this.log("info", `Package ID: ${this.packageId}`);

      // Get local version
      const localVersion = this.getLocalVersion();

      // Get Play Store version
      let playStoreVersion;
      try {
        this.log("info", "Checking Play Store version...");
        playStoreVersion = await this.getPlayStoreVersion();

        if (!playStoreVersion) {
          playStoreVersion = await this.getManualPlayStoreVersion();
        }
      } catch (error) {
        this.log("warning", `Could not automatically fetch Play Store version: ${error.message}`);
        playStoreVersion = await this.getManualPlayStoreVersion();
      }

      // Check clone version if configured
      const cloneVersion = await this.checkCloneVersion();
      if (cloneVersion) {
        this.log("info", `Clone app version: ${cloneVersion.versionName}`);
      }

      // Compare versions
      const comparison = this.compareVersions(localVersion, playStoreVersion);

      // Log this check
      this.logVersionHistory(localVersion, playStoreVersion, "version_check");

      // Handle auto-update if needed and enabled
      if (comparison.needsUpdate && comparison.reason === "local_behind") {
        if (this.config.autoUpdateVersion || options.autoUpdate) {
          const suggestedVersionCode =
            (playStoreVersion.versionCode || localVersion.versionCode) + 1;
          const suggestedVersionName = this.config.versioningStrategy?.syncVersionNameWithCode
            ? suggestedVersionCode.toString()
            : localVersion.versionName;

          const updated = this.updateLocalVersion(suggestedVersionCode, suggestedVersionName);
          if (updated) {
            this.logVersionHistory(
              { versionCode: suggestedVersionCode, versionName: suggestedVersionName },
              playStoreVersion,
              "auto_update"
            );
            this.log("success", "✅ Version automatically updated!");
            return;
          }
        } else if (!options.silent) {
          // Prompt for manual update
          const updateChoice = await this.promptVersionUpdate(localVersion, playStoreVersion);
          if (updateChoice.update) {
            const updated = this.updateLocalVersion(
              updateChoice.versionCode,
              updateChoice.versionName
            );
            if (updated) {
              this.logVersionHistory(
                { versionCode: updateChoice.versionCode, versionName: updateChoice.versionName },
                playStoreVersion,
                "manual_update"
              );
              this.log("success", "✅ Version manually updated!");
              return;
            }
          }
        }
      }

      // Show update instructions if still needed
      if (comparison.needsUpdate) {
        this.showUpdateInstructions(localVersion, playStoreVersion, comparison);
      }

      // Exit with appropriate code
      if (
        comparison.needsUpdate &&
        comparison.reason === "local_behind" &&
        this.config.strictMode
      ) {
        this.log("error", "\n🚫 Version check failed! Please update your app version.");
        process.exit(1);
      } else {
        this.log("success", "\n✅ Version management completed successfully!");
        process.exit(0);
      }
    } catch (error) {
      this.log("error", `Version management failed: ${error.message}`);
      process.exit(1);
    }
  }
}

// CLI interface
if (require.main === module) {
  const args = process.argv.slice(2);
  const options = {
    autoUpdate: args.includes("--auto-update"),
    silent: args.includes("--silent"),
    strictMode: !args.includes("--no-strict")
  };

  const manager = new VersionManager();
  manager.run(options);
}

module.exports = VersionManager;
