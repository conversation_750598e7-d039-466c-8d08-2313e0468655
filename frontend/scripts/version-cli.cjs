#!/usr/bin/env node

const VersionManager = require("./version-manager.cjs");
const VersionChecker = require("./check-version.cjs");
const fs = require("fs");
const path = require("path");

/**
 * Command Line Interface for Version Management
 */
class VersionCLI {
  constructor() {
    this.commands = {
      check: "Check version against Play Store",
      update: "Update version with prompts",
      "auto-update": "Automatically update version",
      history: "Show version history",
      config: "Show current configuration",
      help: "Show this help message"
    };
  }

  showHelp() {
    console.log("\n🔧 Wify App Version Management CLI\n");
    console.log("Usage: node version-cli.js <command> [options]\n");
    console.log("Commands:");

    Object.entries(this.commands).forEach(([cmd, desc]) => {
      console.log(`  ${cmd.padEnd(12)} ${desc}`);
    });

    console.log("\nOptions:");
    console.log("  --silent     Run without prompts");
    console.log("  --no-strict  Don't fail on version mismatch");
    console.log("  --help       Show this help message");

    console.log("\nExamples:");
    console.log("  node version-cli.js check");
    console.log("  node version-cli.js update");
    console.log("  node version-cli.js auto-update --silent");
    console.log("  node version-cli.js history");
    console.log("");
  }

  async showHistory() {
    const historyPath = path.join(__dirname, "version-history.json");

    try {
      if (!fs.existsSync(historyPath)) {
        console.log("📝 No version history found.");
        return;
      }

      const history = JSON.parse(fs.readFileSync(historyPath, "utf8"));

      console.log("\n📊 Version History (Last 10 entries):\n");

      const recent = history.slice(-10).reverse();

      recent.forEach((entry, index) => {
        const date = new Date(entry.timestamp).toLocaleString();
        const action = entry.action.replace("_", " ").toUpperCase();

        console.log(`${index + 1}. ${date}`);
        console.log(`   Action: ${action}`);
        console.log(
          `   Local: ${entry.localVersion.versionName} (${entry.localVersion.versionCode})`
        );
        console.log(
          `   Store: ${entry.playStoreVersion.versionName}${entry.playStoreVersion.versionCode ? ` (${entry.playStoreVersion.versionCode})` : ""}`
        );
        console.log(`   User: ${entry.user}`);
        console.log("");
      });
    } catch (error) {
      console.error("❌ Error reading version history:", error.message);
    }
  }

  showConfig() {
    const configPath = path.join(__dirname, "version-config.json");

    try {
      const config = JSON.parse(fs.readFileSync(configPath, "utf8"));

      console.log("\n⚙️  Current Configuration:\n");
      console.log(JSON.stringify(config, null, 2));
      console.log("");
    } catch (error) {
      console.error("❌ Error reading configuration:", error.message);
    }
  }

  async run() {
    const args = process.argv.slice(2);
    const command = args[0];
    const options = {
      silent: args.includes("--silent"),
      strictMode: !args.includes("--no-strict"),
      autoUpdate: command === "auto-update"
    };

    if (!command || command === "help" || args.includes("--help")) {
      this.showHelp();
      return;
    }

    switch (command) {
      case "check":
        console.log("🔍 Running version check...");
        const checker = new VersionChecker();
        await checker.run();
        break;

      case "update":
        console.log("🔄 Running version update...");
        const updateManager = new VersionManager();
        await updateManager.run(options);
        break;

      case "auto-update":
        console.log("🤖 Running automatic version update...");
        const autoManager = new VersionManager();
        await autoManager.run({ ...options, autoUpdate: true });
        break;

      case "history":
        await this.showHistory();
        break;

      case "config":
        this.showConfig();
        break;

      default:
        console.error(`❌ Unknown command: ${command}`);
        console.log('Run "node version-cli.js help" for available commands.');
        process.exit(1);
    }
  }
}

// Run CLI if this script is executed directly
if (require.main === module) {
  const cli = new VersionCLI();
  cli.run().catch((error) => {
    console.error("❌ CLI Error:", error.message);
    process.exit(1);
  });
}

module.exports = VersionCLI;
