#!/usr/bin/env node

const https = require("https");
const fs = require("fs");
const path = require("path");

/**
 * Automated Version Checking System for Android App
 *
 * This script compares your local app version with the Play Store version
 * and prompts you to update if they don't match.
 */

class VersionChecker {
  constructor() {
    this.packageId = "co.in.wify.wifytech"; // Your app's package ID
    this.buildGradlePath = path.join(__dirname, "../android/app/build.gradle");
    this.colors = {
      red: "\x1b[31m",
      green: "\x1b[32m",
      yellow: "\x1b[33m",
      blue: "\x1b[34m",
      reset: "\x1b[0m",
      bold: "\x1b[1m"
    };
  }

  /**
   * Get the current version from build.gradle
   */
  getLocalVersion() {
    try {
      const buildGradleContent = fs.readFileSync(this.buildGradlePath, "utf8");

      // Extract versionCode and versionName
      const versionCodeMatch = buildGradleContent.match(/versionCode\s+(\d+)/);
      const versionNameMatch = buildGradleContent.match(/versionName\s+"([^"]+)"/);

      if (!versionCodeMatch || !versionNameMatch) {
        throw new Error("Could not find version information in build.gradle");
      }

      return {
        versionCode: parseInt(versionCodeMatch[1]),
        versionName: versionNameMatch[1]
      };
    } catch (error) {
      this.log("error", `Error reading local version: ${error.message}`);
      process.exit(1);
    }
  }

  /**
   * Get the Play Store version using Google Play Store API
   * Note: This requires the app to be published on Play Store
   */
  async getPlayStoreVersion() {
    return new Promise((resolve, reject) => {
      // Using a web scraping approach as an alternative to official API
      // This scrapes the Play Store page for version information
      const url = `https://play.google.com/store/apps/details?id=${this.packageId}&hl=en`;

      https
        .get(url, (res) => {
          let data = "";

          res.on("data", (chunk) => {
            data += chunk;
          });

          res.on("end", () => {
            try {
              // Look for version information in the HTML
              // Play Store shows version in different formats, we'll try to extract it
              const versionMatch =
                data.match(/Current Version<\/span><span[^>]*>([^<]+)<\/span>/i) ||
                data.match(/Version<\/span><span[^>]*>([^<]+)<\/span>/i) ||
                data.match(/"softwareVersion":"([^"]+)"/i);

              if (versionMatch) {
                resolve({
                  versionName: versionMatch[1].trim(),
                  source: "play_store_scrape"
                });
              } else {
                // If scraping fails, we'll use a manual input method
                resolve(null);
              }
            } catch (error) {
              reject(error);
            }
          });
        })
        .on("error", (error) => {
          reject(error);
        });
    });
  }

  /**
   * Manual version input when automatic detection fails
   */
  async getManualPlayStoreVersion() {
    const readline = require("readline");
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });

    return new Promise((resolve) => {
      this.log("info", "Automatic Play Store version detection failed.");
      this.log("info", "Please check your app on Google Play Store manually.");
      this.log("info", `Visit: https://play.google.com/store/apps/details?id=${this.packageId}`);

      rl.question('\nEnter the current Play Store version name (e.g., "17"): ', (versionName) => {
        rl.question("Enter the current Play Store version code (e.g., 17): ", (versionCode) => {
          rl.close();
          resolve({
            versionName: versionName.trim(),
            versionCode: parseInt(versionCode.trim()) || null,
            source: "manual_input"
          });
        });
      });
    });
  }

  /**
   * Compare versions and provide recommendations
   */
  compareVersions(local, playStore) {
    this.log("info", "\n=== VERSION COMPARISON ===");
    this.log("info", `Local Version: ${local.versionName} (Code: ${local.versionCode})`);
    this.log(
      "info",
      `Play Store Version: ${playStore.versionName}${playStore.versionCode ? ` (Code: ${playStore.versionCode})` : ""}`
    );

    // Compare version codes if available
    if (playStore.versionCode) {
      if (local.versionCode === playStore.versionCode) {
        this.log("success", "\n✅ Version codes match! Your app is up to date.");
        return { needsUpdate: false, reason: "versions_match" };
      } else if (local.versionCode > playStore.versionCode) {
        this.log("warning", "\n⚠️  Your local version is AHEAD of Play Store.");
        this.log("info", "This is normal if you're preparing a new release.");
        return { needsUpdate: false, reason: "local_ahead" };
      } else {
        this.log("error", "\n❌ Your local version is BEHIND Play Store!");
        this.log("error", "You need to update your app version before publishing.");
        return { needsUpdate: true, reason: "local_behind" };
      }
    } else {
      // Compare version names if version codes aren't available
      if (local.versionName === playStore.versionName) {
        this.log("warning", "\n⚠️  Version names match, but couldn't verify version codes.");
        this.log("info", "Please manually verify version codes match.");
        return { needsUpdate: false, reason: "names_match_codes_unknown" };
      } else {
        this.log("warning", "\n⚠️  Version names differ. Please verify manually.");
        this.log("info", "Consider updating your version if you're behind.");
        return { needsUpdate: true, reason: "names_differ" };
      }
    }
  }

  /**
   * Provide update instructions
   */
  showUpdateInstructions(local, playStore, comparison) {
    if (!comparison.needsUpdate) return;

    this.log("info", "\n=== UPDATE INSTRUCTIONS ===");

    if (comparison.reason === "local_behind") {
      const suggestedVersionCode = (playStore.versionCode || local.versionCode) + 1;
      const suggestedVersionName = suggestedVersionCode.toString();

      this.log("info", "To update your app version:");
      this.log("info", `1. Open: ${this.buildGradlePath}`);
      this.log(
        "info",
        `2. Change versionCode from ${local.versionCode} to ${suggestedVersionCode}`
      );
      this.log(
        "info",
        `3. Change versionName from "${local.versionName}" to "${suggestedVersionName}"`
      );
      this.log("info", "4. Rebuild your app");

      this.log("warning", "\nExample:");
      this.log("info", `   versionCode ${suggestedVersionCode}`);
      this.log("info", `   versionName "${suggestedVersionName}"`);
    }

    this.log("info", "\nAfter updating, run this script again to verify.");
  }

  /**
   * Logging utility with colors
   */
  log(type, message) {
    const timestamp = new Date().toLocaleTimeString();
    let prefix = "";
    let color = this.colors.reset;

    switch (type) {
      case "error":
        prefix = "❌ ERROR";
        color = this.colors.red;
        break;
      case "warning":
        prefix = "⚠️  WARNING";
        color = this.colors.yellow;
        break;
      case "success":
        prefix = "✅ SUCCESS";
        color = this.colors.green;
        break;
      case "info":
        prefix = "ℹ️  INFO";
        color = this.colors.blue;
        break;
    }

    if (prefix) {
      console.log(
        `${color}${this.colors.bold}[${timestamp}] ${prefix}${this.colors.reset}${color} ${message}${this.colors.reset}`
      );
    } else {
      console.log(`${color}${message}${this.colors.reset}`);
    }
  }

  /**
   * Main execution function
   */
  async run() {
    try {
      this.log("info", "Starting automated version check...");
      this.log("info", `Package ID: ${this.packageId}`);

      // Get local version
      const localVersion = this.getLocalVersion();

      // Try to get Play Store version
      let playStoreVersion;
      try {
        this.log("info", "Checking Play Store version...");
        playStoreVersion = await this.getPlayStoreVersion();

        if (!playStoreVersion) {
          playStoreVersion = await this.getManualPlayStoreVersion();
        }
      } catch (error) {
        this.log("warning", `Could not automatically fetch Play Store version: ${error.message}`);
        playStoreVersion = await this.getManualPlayStoreVersion();
      }

      // Compare versions
      const comparison = this.compareVersions(localVersion, playStoreVersion);

      // Show update instructions if needed
      this.showUpdateInstructions(localVersion, playStoreVersion, comparison);

      // Exit with appropriate code
      if (comparison.needsUpdate && comparison.reason === "local_behind") {
        this.log("error", "\n🚫 Version check failed! Please update your app version.");
        process.exit(1);
      } else {
        this.log("success", "\n✅ Version check completed successfully!");
        process.exit(0);
      }
    } catch (error) {
      this.log("error", `Version check failed: ${error.message}`);
      process.exit(1);
    }
  }
}

// Run the version checker if this script is executed directly
if (require.main === module) {
  const checker = new VersionChecker();
  checker.run();
}

module.exports = VersionChecker;
