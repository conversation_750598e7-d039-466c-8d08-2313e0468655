package co.in.wify.wifytech;

import android.content.Intent;
import android.util.Log;

import com.getcapacitor.JSObject;
import com.getcapacitor.Plugin;
import com.getcapacitor.PluginCall;
import com.getcapacitor.PluginMethod;
import com.getcapacitor.annotation.ActivityCallback;
import com.getcapacitor.annotation.CapacitorPlugin;
import com.google.android.play.core.appupdate.AppUpdateInfo;
import com.google.android.play.core.install.model.AppUpdateType;

/**
 * Capacitor Plugin for Google Play In-App Updates
 * 
 * This plugin exposes the In-App Update functionality to the web layer,
 * allowing the frontend to trigger and monitor update flows.
 */
@CapacitorPlugin(name = "InAppUpdate")
public class InAppUpdatePlugin extends Plugin implements InAppUpdateManager.InAppUpdateListener {
    private static final String TAG = "InAppUpdatePlugin";
    
    private InAppUpdateManager updateManager;
    private PluginCall pendingCall;
    
    @Override
    public void load() {
        super.load();
        updateManager = new InAppUpdateManager(getActivity(), this);
        Log.d(TAG, "InAppUpdate plugin loaded");
    }
    
    /**
     * Check for available updates
     */
    @PluginMethod
    public void checkForUpdate(PluginCall call) {
        Log.d(TAG, "checkForUpdate called from web");
        pendingCall = call;
        updateManager.checkForUpdate();
    }
    
    /**
     * Start immediate update flow
     */
    @PluginMethod
    public void startImmediateUpdate(PluginCall call) {
        Log.d(TAG, "startImmediateUpdate called from web");
        // This will be handled by the checkForUpdate flow
        // We'll just acknowledge the call
        JSObject result = new JSObject();
        result.put("success", true);
        result.put("message", "Immediate update flow will be triggered if update is available");
        call.resolve(result);
    }
    
    /**
     * Complete flexible update (install downloaded update)
     */
    @PluginMethod
    public void completeFlexibleUpdate(PluginCall call) {
        Log.d(TAG, "completeFlexibleUpdate called from web");
        updateManager.completeFlexibleUpdate();
        
        JSObject result = new JSObject();
        result.put("success", true);
        result.put("message", "Flexible update completion triggered");
        call.resolve(result);
    }
    
    /**
     * Get update info without triggering update flow
     */
    @PluginMethod
    public void getUpdateInfo(PluginCall call) {
        Log.d(TAG, "getUpdateInfo called from web");
        // For now, we'll use the same check mechanism
        // In a more advanced implementation, you could separate info gathering from update triggering
        pendingCall = call;
        updateManager.checkForUpdate();
    }
    
    // InAppUpdateListener implementation
    
    @Override
    public void onUpdateAvailable(AppUpdateInfo updateInfo, int updateType) {
        Log.d(TAG, "Update available - Type: " + (updateType == AppUpdateType.IMMEDIATE ? "immediate" : "flexible"));
        
        JSObject result = new JSObject();
        result.put("updateAvailable", true);
        result.put("updateType", updateType == AppUpdateType.IMMEDIATE ? "immediate" : "flexible");
        result.put("priority", updateInfo.updatePriority());
        
        if (updateInfo.clientVersionStalenessDays() != null) {
            result.put("stalenessDays", updateInfo.clientVersionStalenessDays());
        }
        
        // Notify web layer
        notifyListeners("updateAvailable", result);
        
        // Resolve pending call if any
        if (pendingCall != null) {
            pendingCall.resolve(result);
            pendingCall = null;
        }
    }
    
    @Override
    public void onUpdateNotAvailable() {
        Log.d(TAG, "No update available");
        
        JSObject result = new JSObject();
        result.put("updateAvailable", false);
        result.put("message", "No update available");
        
        // Notify web layer
        notifyListeners("updateNotAvailable", result);
        
        // Resolve pending call if any
        if (pendingCall != null) {
            pendingCall.resolve(result);
            pendingCall = null;
        }
    }
    
    @Override
    public void onUpdateDownloaded() {
        Log.d(TAG, "Update downloaded, ready to install");
        
        JSObject result = new JSObject();
        result.put("status", "downloaded");
        result.put("message", "Update downloaded successfully. Ready to install.");
        
        // Notify web layer
        notifyListeners("updateDownloaded", result);
    }
    
    @Override
    public void onUpdateInstalled() {
        Log.d(TAG, "Update installed successfully");
        
        JSObject result = new JSObject();
        result.put("status", "installed");
        result.put("message", "Update installed successfully");
        
        // Notify web layer
        notifyListeners("updateInstalled", result);
    }
    
    @Override
    public void onUpdateFailed(String error) {
        Log.e(TAG, "Update failed: " + error);
        
        JSObject result = new JSObject();
        result.put("status", "failed");
        result.put("error", error);
        
        // Notify web layer
        notifyListeners("updateFailed", result);
        
        // Reject pending call if any
        if (pendingCall != null) {
            pendingCall.reject("Update failed: " + error);
            pendingCall = null;
        }
    }
    
    @Override
    public void onUpdateCancelled() {
        Log.d(TAG, "Update cancelled by user");
        
        JSObject result = new JSObject();
        result.put("status", "cancelled");
        result.put("message", "Update cancelled by user");
        
        // Notify web layer
        notifyListeners("updateCancelled", result);
        
        // Resolve pending call if any (not an error, user choice)
        if (pendingCall != null) {
            pendingCall.resolve(result);
            pendingCall = null;
        }
    }
    
    // Handle activity results
    @ActivityCallback
    private void handleUpdateResult(PluginCall call, Intent data) {
        if (updateManager != null) {
            updateManager.handleActivityResult(
                call.getInt("requestCode", 0),
                call.getInt("resultCode", 0)
            );
        }
    }
    
    @Override
    protected void handleOnResume() {
        super.handleOnResume();
        if (updateManager != null) {
            updateManager.resumeUpdateIfNeeded();
        }
    }
    
    @Override
    protected void handleOnDestroy() {
        super.handleOnDestroy();
        if (updateManager != null) {
            updateManager.cleanup();
        }
    }
}
