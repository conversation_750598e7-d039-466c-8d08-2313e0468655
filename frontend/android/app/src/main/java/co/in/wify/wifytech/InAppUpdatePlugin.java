package co.in.wify.wifytech;

import android.content.Intent;
import android.util.Log;

import com.getcapacitor.JSObject;
import com.getcapacitor.Plugin;
import com.getcapacitor.PluginCall;
import com.getcapacitor.PluginMethod;
import com.getcapacitor.annotation.CapacitorPlugin;

/**
 * Capacitor Plugin for Google Play In-App Updates
 * 
 * This plugin provides a bridge between the web layer and the native Android
 * In-App Update functionality. It handles all update flows and provides
 * real-time event notifications to the web layer.
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 */
@CapacitorPlugin(name = "InAppUpdate")
public class InAppUpdatePlugin extends Plugin implements InAppUpdateManager.InAppUpdateListener {
    
    private static final String TAG = "InAppUpdatePlugin";
    
    private InAppUpdateManager updateManager;
    
    @Override
    public void load() {
        super.load();
        Log.d(TAG, "InAppUpdate plugin loaded");
        
        // Initialize the update manager
        updateManager = new InAppUpdateManager(getActivity(), this);
    }
    
    /**
     * Check for available updates
     */
    @PluginMethod
    public void checkForUpdate(PluginCall call) {
        Log.d(TAG, "checkForUpdate called");
        
        if (updateManager == null) {
            call.reject("Update manager not initialized");
            return;
        }
        
        try {
            updateManager.checkForUpdate();
            
            // Return immediately - actual result will come via events
            JSObject result = new JSObject();
            result.put("success", true);
            result.put("message", "Update check initiated");
            call.resolve(result);
            
        } catch (Exception e) {
            Log.e(TAG, "Error checking for updates", e);
            call.reject("Failed to check for updates: " + e.getMessage());
        }
    }
    
    /**
     * Start immediate update flow
     */
    @PluginMethod
    public void startImmediateUpdate(PluginCall call) {
        Log.d(TAG, "startImmediateUpdate called");
        
        if (updateManager == null) {
            call.reject("Update manager not initialized");
            return;
        }
        
        try {
            updateManager.startImmediateUpdate();
            
            JSObject result = new JSObject();
            result.put("success", true);
            result.put("message", "Immediate update started");
            call.resolve(result);
            
        } catch (Exception e) {
            Log.e(TAG, "Error starting immediate update", e);
            call.reject("Failed to start immediate update: " + e.getMessage());
        }
    }
    
    /**
     * Start flexible update flow
     */
    @PluginMethod
    public void startFlexibleUpdate(PluginCall call) {
        Log.d(TAG, "startFlexibleUpdate called");
        
        if (updateManager == null) {
            call.reject("Update manager not initialized");
            return;
        }
        
        try {
            updateManager.startFlexibleUpdate();
            
            JSObject result = new JSObject();
            result.put("success", true);
            result.put("message", "Flexible update started");
            call.resolve(result);
            
        } catch (Exception e) {
            Log.e(TAG, "Error starting flexible update", e);
            call.reject("Failed to start flexible update: " + e.getMessage());
        }
    }
    
    /**
     * Complete flexible update (install downloaded update)
     */
    @PluginMethod
    public void completeFlexibleUpdate(PluginCall call) {
        Log.d(TAG, "completeFlexibleUpdate called");
        
        if (updateManager == null) {
            call.reject("Update manager not initialized");
            return;
        }
        
        try {
            updateManager.completeFlexibleUpdate();
            
            JSObject result = new JSObject();
            result.put("success", true);
            result.put("message", "Flexible update completion initiated");
            call.resolve(result);
            
        } catch (Exception e) {
            Log.e(TAG, "Error completing flexible update", e);
            call.reject("Failed to complete flexible update: " + e.getMessage());
        }
    }
    
    /**
     * Get update info without triggering update flow
     */
    @PluginMethod
    public void getUpdateInfo(PluginCall call) {
        Log.d(TAG, "getUpdateInfo called");
        
        if (updateManager == null) {
            call.reject("Update manager not initialized");
            return;
        }
        
        try {
            updateManager.getUpdateInfo();
            
            // Return immediately - actual result will come via events
            JSObject result = new JSObject();
            result.put("success", true);
            result.put("message", "Update info request initiated");
            call.resolve(result);
            
        } catch (Exception e) {
            Log.e(TAG, "Error getting update info", e);
            call.reject("Failed to get update info: " + e.getMessage());
        }
    }
    
    // InAppUpdateManager.InAppUpdateListener implementation
    
    @Override
    public void onUpdateAvailable(String updateType, int priority, int stalenessDays) {
        Log.d(TAG, String.format("Update available: type=%s, priority=%d, staleness=%d", 
                                updateType, priority, stalenessDays));
        
        JSObject data = new JSObject();
        data.put("updateType", updateType);
        data.put("priority", priority);
        data.put("stalenessDays", stalenessDays);
        data.put("updateAvailable", true);
        
        notifyListeners("updateAvailable", data);
    }
    
    @Override
    public void onUpdateNotAvailable() {
        Log.d(TAG, "No update available");
        
        JSObject data = new JSObject();
        data.put("updateAvailable", false);
        
        notifyListeners("updateNotAvailable", data);
    }
    
    @Override
    public void onUpdateDownloaded() {
        Log.d(TAG, "Update downloaded");
        
        JSObject data = new JSObject();
        data.put("downloaded", true);
        
        notifyListeners("updateDownloaded", data);
    }
    
    @Override
    public void onUpdateInstalled() {
        Log.d(TAG, "Update installed");
        
        JSObject data = new JSObject();
        data.put("installed", true);
        
        notifyListeners("updateInstalled", data);
    }
    
    @Override
    public void onUpdateFailed(String error) {
        Log.e(TAG, "Update failed: " + error);
        
        JSObject data = new JSObject();
        data.put("error", error);
        data.put("failed", true);
        
        notifyListeners("updateFailed", data);
    }
    
    @Override
    public void onUpdateCancelled() {
        Log.d(TAG, "Update cancelled");
        
        JSObject data = new JSObject();
        data.put("cancelled", true);
        
        notifyListeners("updateCancelled", data);
    }
    
    @Override
    protected void handleOnActivityResult(int requestCode, int resultCode, Intent data) {
        super.handleOnActivityResult(requestCode, resultCode, data);
        
        if (updateManager != null) {
            updateManager.onActivityResult(requestCode, resultCode);
        }
    }
    
    @Override
    protected void handleOnDestroy() {
        super.handleOnDestroy();
        
        if (updateManager != null) {
            updateManager.cleanup();
            updateManager = null;
        }
    }
}
