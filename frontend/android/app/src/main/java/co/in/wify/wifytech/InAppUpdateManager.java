package co.in.wify.wifytech;

import android.app.Activity;
import android.content.IntentSender;
import android.util.Log;

import com.google.android.play.core.appupdate.AppUpdateInfo;
import com.google.android.play.core.appupdate.AppUpdateManager;
import com.google.android.play.core.appupdate.AppUpdateManagerFactory;
import com.google.android.play.core.appupdate.AppUpdateOptions;
import com.google.android.play.core.install.InstallState;
import com.google.android.play.core.install.InstallStateUpdatedListener;
import com.google.android.play.core.install.model.AppUpdateType;
import com.google.android.play.core.install.model.InstallStatus;
import com.google.android.play.core.install.model.UpdateAvailability;
import com.google.android.play.core.tasks.OnSuccessListener;
import com.google.android.play.core.tasks.OnFailureListener;

/**
 * In-App Update Manager for Wify Android App
 * 
 * This class handles Google Play In-App Updates with both immediate and flexible update flows.
 * It integrates with the existing version checking system to provide seamless update experience.
 */
public class InAppUpdateManager {
    private static final String TAG = "InAppUpdateManager";
    
    // Request codes for update flows
    public static final int REQUEST_CODE_IMMEDIATE_UPDATE = 1001;
    public static final int REQUEST_CODE_FLEXIBLE_UPDATE = 1002;
    
    // Update priority thresholds
    private static final int IMMEDIATE_UPDATE_PRIORITY = 4; // High priority updates
    private static final int FLEXIBLE_UPDATE_PRIORITY = 2;  // Medium priority updates
    private static final int STALENESS_DAYS_IMMEDIATE = 7;  // Force immediate after 7 days
    private static final int STALENESS_DAYS_FLEXIBLE = 3;   // Suggest flexible after 3 days
    
    private AppUpdateManager appUpdateManager;
    private Activity activity;
    private InAppUpdateListener updateListener;
    
    // Install state listener for flexible updates
    private InstallStateUpdatedListener installStateUpdatedListener = new InstallStateUpdatedListener() {
        @Override
        public void onStateUpdate(InstallState state) {
            if (state.installStatus() == InstallStatus.DOWNLOADED) {
                // The update has been downloaded but not installed.
                // Notify the user to complete the update.
                if (updateListener != null) {
                    updateListener.onUpdateDownloaded();
                }
                showUpdateCompleteSnackbar();
            } else if (state.installStatus() == InstallStatus.INSTALLED) {
                // Update has been installed successfully
                if (updateListener != null) {
                    updateListener.onUpdateInstalled();
                }
                // Unregister listener as update is complete
                if (appUpdateManager != null) {
                    appUpdateManager.unregisterListener(installStateUpdatedListener);
                }
            } else if (state.installStatus() == InstallStatus.FAILED) {
                // Update failed
                if (updateListener != null) {
                    updateListener.onUpdateFailed("Update installation failed");
                }
                Log.e(TAG, "Update installation failed");
            }
        }
    };
    
    /**
     * Interface for update callbacks
     */
    public interface InAppUpdateListener {
        void onUpdateAvailable(AppUpdateInfo updateInfo, int updateType);
        void onUpdateNotAvailable();
        void onUpdateDownloaded();
        void onUpdateInstalled();
        void onUpdateFailed(String error);
        void onUpdateCancelled();
    }
    
    /**
     * Constructor
     * @param activity The current activity
     * @param listener Callback listener for update events
     */
    public InAppUpdateManager(Activity activity, InAppUpdateListener listener) {
        this.activity = activity;
        this.updateListener = listener;
        this.appUpdateManager = AppUpdateManagerFactory.create(activity);
    }
    
    /**
     * Check for available updates and determine update type based on priority and staleness
     */
    public void checkForUpdate() {
        Log.d(TAG, "Checking for app updates...");
        
        appUpdateManager.getAppUpdateInfo().addOnSuccessListener(new OnSuccessListener<AppUpdateInfo>() {
            @Override
            public void onSuccess(AppUpdateInfo appUpdateInfo) {
                if (appUpdateInfo.updateAvailability() == UpdateAvailability.UPDATE_AVAILABLE) {
                    Log.d(TAG, "Update available. Priority: " + appUpdateInfo.updatePriority() + 
                          ", Staleness: " + appUpdateInfo.clientVersionStalenessDays());
                    
                    int updateType = determineUpdateType(appUpdateInfo);
                    
                    if (updateType != -1) {
                        if (updateListener != null) {
                            updateListener.onUpdateAvailable(appUpdateInfo, updateType);
                        }
                        startUpdate(appUpdateInfo, updateType);
                    } else {
                        Log.d(TAG, "Update available but not critical enough to prompt user");
                        if (updateListener != null) {
                            updateListener.onUpdateNotAvailable();
                        }
                    }
                } else if (appUpdateInfo.updateAvailability() == UpdateAvailability.DEVELOPER_TRIGGERED_UPDATE_IN_PROGRESS) {
                    // If an in-app update is already running, resume the update.
                    Log.d(TAG, "Update already in progress, resuming...");
                    startUpdate(appUpdateInfo, AppUpdateType.IMMEDIATE);
                } else {
                    Log.d(TAG, "No update available");
                    if (updateListener != null) {
                        updateListener.onUpdateNotAvailable();
                    }
                }
            }
        }).addOnFailureListener(new OnFailureListener() {
            @Override
            public void onFailure(Exception e) {
                Log.e(TAG, "Failed to check for updates", e);
                if (updateListener != null) {
                    updateListener.onUpdateFailed("Failed to check for updates: " + e.getMessage());
                }
            }
        });
    }
    
    /**
     * Determine the appropriate update type based on priority and staleness
     */
    private int determineUpdateType(AppUpdateInfo appUpdateInfo) {
        int priority = appUpdateInfo.updatePriority();
        Integer stalenessDays = appUpdateInfo.clientVersionStalenessDays();
        
        // Force immediate update for high priority or very stale versions
        if (priority >= IMMEDIATE_UPDATE_PRIORITY || 
            (stalenessDays != null && stalenessDays >= STALENESS_DAYS_IMMEDIATE)) {
            if (appUpdateInfo.isUpdateTypeAllowed(AppUpdateType.IMMEDIATE)) {
                Log.d(TAG, "Immediate update recommended (Priority: " + priority + ", Staleness: " + stalenessDays + ")");
                return AppUpdateType.IMMEDIATE;
            }
        }
        
        // Suggest flexible update for medium priority or moderately stale versions
        if (priority >= FLEXIBLE_UPDATE_PRIORITY || 
            (stalenessDays != null && stalenessDays >= STALENESS_DAYS_FLEXIBLE)) {
            if (appUpdateInfo.isUpdateTypeAllowed(AppUpdateType.FLEXIBLE)) {
                Log.d(TAG, "Flexible update recommended (Priority: " + priority + ", Staleness: " + stalenessDays + ")");
                return AppUpdateType.FLEXIBLE;
            }
        }
        
        // No update needed
        return -1;
    }
    
    /**
     * Start the update flow
     */
    private void startUpdate(AppUpdateInfo appUpdateInfo, int updateType) {
        try {
            AppUpdateOptions appUpdateOptions = AppUpdateOptions.newBuilder(updateType).build();
            
            if (updateType == AppUpdateType.FLEXIBLE) {
                // Register listener for flexible updates
                appUpdateManager.registerListener(installStateUpdatedListener);
            }
            
            appUpdateManager.startUpdateFlowForResult(
                appUpdateInfo,
                activity,
                appUpdateOptions,
                updateType == AppUpdateType.IMMEDIATE ? REQUEST_CODE_IMMEDIATE_UPDATE : REQUEST_CODE_FLEXIBLE_UPDATE
            );
            
            Log.d(TAG, "Started " + (updateType == AppUpdateType.IMMEDIATE ? "immediate" : "flexible") + " update flow");
            
        } catch (IntentSender.SendIntentException e) {
            Log.e(TAG, "Failed to start update flow", e);
            if (updateListener != null) {
                updateListener.onUpdateFailed("Failed to start update: " + e.getMessage());
            }
        }
    }
    
    /**
     * Complete a flexible update (call this when user chooses to install downloaded update)
     */
    public void completeFlexibleUpdate() {
        appUpdateManager.completeUpdate();
    }
    
    /**
     * Show snackbar to complete flexible update
     */
    private void showUpdateCompleteSnackbar() {
        // This would typically show a Snackbar, but since we're in a service,
        // we'll notify through the listener instead
        Log.d(TAG, "Update downloaded, ready to install");
    }
    
    /**
     * Handle activity result from update flow
     */
    public void handleActivityResult(int requestCode, int resultCode) {
        if (requestCode == REQUEST_CODE_IMMEDIATE_UPDATE || requestCode == REQUEST_CODE_FLEXIBLE_UPDATE) {
            if (resultCode != Activity.RESULT_OK) {
                Log.d(TAG, "Update flow cancelled by user");
                if (updateListener != null) {
                    updateListener.onUpdateCancelled();
                }
            }
        }
    }
    
    /**
     * Resume update if needed (call in onResume)
     */
    public void resumeUpdateIfNeeded() {
        appUpdateManager.getAppUpdateInfo().addOnSuccessListener(new OnSuccessListener<AppUpdateInfo>() {
            @Override
            public void onSuccess(AppUpdateInfo appUpdateInfo) {
                if (appUpdateInfo.updateAvailability() == UpdateAvailability.DEVELOPER_TRIGGERED_UPDATE_IN_PROGRESS) {
                    // If an in-app update is already running, resume the update.
                    startUpdate(appUpdateInfo, AppUpdateType.IMMEDIATE);
                }
                
                if (appUpdateInfo.installStatus() == InstallStatus.DOWNLOADED) {
                    // Update has been downloaded but not installed, show completion UI
                    if (updateListener != null) {
                        updateListener.onUpdateDownloaded();
                    }
                }
            }
        });
    }
    
    /**
     * Cleanup resources
     */
    public void cleanup() {
        if (appUpdateManager != null && installStateUpdatedListener != null) {
            appUpdateManager.unregisterListener(installStateUpdatedListener);
        }
    }
}
