package co.in.wify.wifytech;

import android.app.Activity;
import android.content.IntentSender;
import android.util.Log;

import com.google.android.play.core.appupdate.AppUpdateInfo;
import com.google.android.play.core.appupdate.AppUpdateManager;
import com.google.android.play.core.appupdate.AppUpdateManagerFactory;
import com.google.android.play.core.appupdate.AppUpdateOptions;
import com.google.android.play.core.install.InstallException;
import com.google.android.play.core.install.InstallState;
import com.google.android.play.core.install.InstallStateUpdatedListener;
import com.google.android.play.core.install.model.AppUpdateType;
import com.google.android.play.core.install.model.InstallStatus;
import com.google.android.play.core.install.model.UpdateAvailability;
import com.google.android.gms.tasks.OnSuccessListener;
import com.google.android.gms.tasks.OnFailureListener;

/**
 * In-App Update Manager
 * 
 * This class handles Google Play In-App Updates with intelligent update type
 * determination
 * based on update priority and staleness. It provides a complete update flow
 * management
 * system with event callbacks for the Capacitor bridge.
 * 
 * Update Logic:
 * - Immediate Updates: Priority >= 4 OR staleness >= 7 days
 * - Flexible Updates: Priority >= 2 OR staleness >= 3 days
 * - No Update: Priority < 2 AND staleness < 3 days
 * 
 * @version 1.0
 */
public class InAppUpdateManager implements InstallStateUpdatedListener {

    private static final String TAG = "InAppUpdateManager";

    // Update priority thresholds
    private static final int IMMEDIATE_UPDATE_PRIORITY_THRESHOLD = 4;
    private static final int FLEXIBLE_UPDATE_PRIORITY_THRESHOLD = 2;

    // Staleness thresholds (in days)
    private static final int IMMEDIATE_UPDATE_STALENESS_DAYS = 7;
    private static final int FLEXIBLE_UPDATE_STALENESS_DAYS = 3;

    // Request codes for update flows
    private static final int IMMEDIATE_UPDATE_REQUEST_CODE = 1001;

    private final Activity activity;
    private final AppUpdateManager appUpdateManager;
    private final InAppUpdateListener listener;

    private AppUpdateInfo currentUpdateInfo;
    private boolean isFlexibleUpdateInProgress = false;

    /**
     * Interface for update event callbacks
     */
    public interface InAppUpdateListener {
        void onUpdateAvailable(String updateType, int priority, int stalenessDays);

        void onUpdateNotAvailable();

        void onUpdateDownloaded();

        void onUpdateInstalled();

        void onUpdateFailed(String error);

        void onUpdateCancelled();
    }

    /**
     * Constructor
     * 
     * @param activity The current activity
     * @param listener Callback listener for update events
     */
    public InAppUpdateManager(Activity activity, InAppUpdateListener listener) {
        this.activity = activity;
        this.listener = listener;
        this.appUpdateManager = AppUpdateManagerFactory.create(activity);

        // Register for install state updates (for flexible updates)
        appUpdateManager.registerListener(this);
    }

    /**
     * Check for available updates and determine update type
     */
    public void checkForUpdate() {
        Log.d(TAG, "Checking for app updates...");

        appUpdateManager.getAppUpdateInfo().addOnSuccessListener(new OnSuccessListener<AppUpdateInfo>() {
            @Override
            public void onSuccess(AppUpdateInfo appUpdateInfo) {
                currentUpdateInfo = appUpdateInfo;

                if (appUpdateInfo.updateAvailability() == UpdateAvailability.UPDATE_AVAILABLE) {
                    int priority = appUpdateInfo.updatePriority();
                    int stalenessDays = appUpdateInfo.clientVersionStalenessDays() != null
                            ? appUpdateInfo.clientVersionStalenessDays()
                            : 0;

                    Log.d(TAG, String.format("Update available - Priority: %d, Staleness: %d days",
                            priority, stalenessDays));

                    String updateType = determineUpdateType(priority, stalenessDays);

                    if (updateType != null) {
                        listener.onUpdateAvailable(updateType, priority, stalenessDays);
                    } else {
                        Log.d(TAG, "Update available but priority/staleness below threshold");
                        listener.onUpdateNotAvailable();
                    }
                } else if (appUpdateInfo
                        .updateAvailability() == UpdateAvailability.DEVELOPER_TRIGGERED_UPDATE_IN_PROGRESS) {
                    // Handle case where update was already started
                    Log.d(TAG, "Update already in progress");
                    if (appUpdateInfo.isUpdateTypeAllowed(AppUpdateType.IMMEDIATE)) {
                        startImmediateUpdate();
                    }
                } else {
                    Log.d(TAG, "No update available");
                    listener.onUpdateNotAvailable();
                }
            }
        }).addOnFailureListener(new OnFailureListener() {
            @Override
            public void onFailure(Exception e) {
                Log.e(TAG, "Failed to check for updates", e);
                listener.onUpdateFailed("Failed to check for updates: " + e.getMessage());
            }
        });
    }

    /**
     * Determine the appropriate update type based on priority and staleness
     * 
     * @param priority      Update priority (0-5)
     * @param stalenessDays Days since update became available
     * @return "immediate", "flexible", or null if no update should be triggered
     */
    private String determineUpdateType(int priority, int stalenessDays) {
        // Immediate update conditions
        if (priority >= IMMEDIATE_UPDATE_PRIORITY_THRESHOLD || stalenessDays >= IMMEDIATE_UPDATE_STALENESS_DAYS) {
            return "immediate";
        }

        // Flexible update conditions
        if (priority >= FLEXIBLE_UPDATE_PRIORITY_THRESHOLD || stalenessDays >= FLEXIBLE_UPDATE_STALENESS_DAYS) {
            return "flexible";
        }

        // No update needed
        return null;
    }

    /**
     * Start immediate update flow
     */
    public void startImmediateUpdate() {
        if (currentUpdateInfo == null) {
            listener.onUpdateFailed("No update info available");
            return;
        }

        if (!currentUpdateInfo.isUpdateTypeAllowed(AppUpdateType.IMMEDIATE)) {
            listener.onUpdateFailed("Immediate update not allowed");
            return;
        }

        try {
            Log.d(TAG, "Starting immediate update...");
            appUpdateManager.startUpdateFlowForResult(
                    currentUpdateInfo,
                    activity,
                    AppUpdateOptions.newBuilder(AppUpdateType.IMMEDIATE).build(),
                    IMMEDIATE_UPDATE_REQUEST_CODE);
        } catch (IntentSender.SendIntentException e) {
            Log.e(TAG, "Failed to start immediate update", e);
            listener.onUpdateFailed("Failed to start immediate update: " + e.getMessage());
        }
    }

    /**
     * Start flexible update flow
     */
    public void startFlexibleUpdate() {
        if (currentUpdateInfo == null) {
            listener.onUpdateFailed("No update info available");
            return;
        }

        if (!currentUpdateInfo.isUpdateTypeAllowed(AppUpdateType.FLEXIBLE)) {
            listener.onUpdateFailed("Flexible update not allowed");
            return;
        }

        try {
            Log.d(TAG, "Starting flexible update...");
            isFlexibleUpdateInProgress = true;

            appUpdateManager.startUpdateFlowForResult(
                    currentUpdateInfo,
                    activity,
                    AppUpdateOptions.newBuilder(AppUpdateType.FLEXIBLE).build(),
                    IMMEDIATE_UPDATE_REQUEST_CODE // Using same request code for simplicity
            );
        } catch (IntentSender.SendIntentException e) {
            Log.e(TAG, "Failed to start flexible update", e);
            isFlexibleUpdateInProgress = false;
            listener.onUpdateFailed("Failed to start flexible update: " + e.getMessage());
        }
    }

    /**
     * Complete flexible update (install downloaded update)
     */
    public void completeFlexibleUpdate() {
        if (isFlexibleUpdateInProgress) {
            Log.d(TAG, "Completing flexible update...");
            appUpdateManager.completeUpdate();
        } else {
            listener.onUpdateFailed("No flexible update in progress");
        }
    }

    /**
     * Get current update info without triggering update flow
     */
    public void getUpdateInfo() {
        Log.d(TAG, "Getting update info...");

        appUpdateManager.getAppUpdateInfo().addOnSuccessListener(new OnSuccessListener<AppUpdateInfo>() {
            @Override
            public void onSuccess(AppUpdateInfo appUpdateInfo) {
                currentUpdateInfo = appUpdateInfo;

                if (appUpdateInfo.updateAvailability() == UpdateAvailability.UPDATE_AVAILABLE) {
                    int priority = appUpdateInfo.updatePriority();
                    int stalenessDays = appUpdateInfo.clientVersionStalenessDays() != null
                            ? appUpdateInfo.clientVersionStalenessDays()
                            : 0;

                    String updateType = determineUpdateType(priority, stalenessDays);

                    if (updateType != null) {
                        listener.onUpdateAvailable(updateType, priority, stalenessDays);
                    } else {
                        listener.onUpdateNotAvailable();
                    }
                } else {
                    listener.onUpdateNotAvailable();
                }
            }
        }).addOnFailureListener(new OnFailureListener() {
            @Override
            public void onFailure(Exception e) {
                Log.e(TAG, "Failed to get update info", e);
                listener.onUpdateFailed("Failed to get update info: " + e.getMessage());
            }
        });
    }

    /**
     * Handle activity result from update flows
     */
    public void onActivityResult(int requestCode, int resultCode) {
        if (requestCode == IMMEDIATE_UPDATE_REQUEST_CODE) {
            if (resultCode == Activity.RESULT_OK) {
                Log.d(TAG, "Update completed successfully");
                listener.onUpdateInstalled();
            } else if (resultCode == Activity.RESULT_CANCELED) {
                Log.d(TAG, "Update cancelled by user");
                listener.onUpdateCancelled();
            } else {
                Log.e(TAG, "Update failed with result code: " + resultCode);
                listener.onUpdateFailed("Update failed with result code: " + resultCode);
            }
        }
    }

    /**
     * InstallStateUpdatedListener implementation for flexible updates
     */
    @Override
    public void onStateUpdate(InstallState installState) {
        switch (installState.installStatus()) {
            case InstallStatus.DOWNLOADING:
                Log.d(TAG, "Update downloading...");
                break;

            case InstallStatus.DOWNLOADED:
                Log.d(TAG, "Update downloaded");
                isFlexibleUpdateInProgress = false;
                listener.onUpdateDownloaded();
                break;

            case InstallStatus.INSTALLED:
                Log.d(TAG, "Update installed");
                listener.onUpdateInstalled();
                break;

            case InstallStatus.FAILED:
                Log.e(TAG, "Update installation failed");
                isFlexibleUpdateInProgress = false;
                listener.onUpdateFailed("Update installation failed");
                break;

            case InstallStatus.CANCELED:
                Log.d(TAG, "Update cancelled");
                isFlexibleUpdateInProgress = false;
                listener.onUpdateCancelled();
                break;
        }
    }

    /**
     * Clean up resources
     */
    public void cleanup() {
        if (appUpdateManager != null) {
            appUpdateManager.unregisterListener(this);
        }
    }
}
