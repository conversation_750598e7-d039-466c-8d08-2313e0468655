package co.in.wify.wifytech;

import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.os.Bundle;

import androidx.appcompat.app.AppCompatDelegate;

import com.getcapacitor.BridgeActivity;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebResourceError;
import android.webkit.WebResourceRequest;
import android.webkit.WebViewClient;

import android.net.ConnectivityManager;
import android.net.NetworkInfo;

import java.net.URL;

public class MainActivity extends BridgeActivity {
    @Override
    protected void onCreate(Bundle savedInstanceState) {

        AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_NO);

        registerPlugin(FilePickerPlugin.class);
        registerPlugin(TmsPlugin.class);
        registerPlugin(InAppUpdatePlugin.class);

        super.onCreate(savedInstanceState);

        // Get the WebView instance and set TextZoom to 100%
        WebView webView = (WebView) this.bridge.getWebView();
        WebSettings webSettings = webView.getSettings();
        webSettings.setTextZoom(100);
        // Set custom WebViewClient to handle load errors
        webView.setWebViewClient(new WebViewClient() {
            @Override
            public void onReceivedError(WebView view, WebResourceRequest request, WebResourceError error) {

                if (!isConnected()) {
                    view.loadUrl("file:///android_asset/public/offline.html");
                } else {
                    super.onReceivedError(view, request, error);
                }
            }

            private boolean isConnected() {
                ConnectivityManager cm = (ConnectivityManager) getSystemService(CONNECTIVITY_SERVICE);
                if (cm != null) {
                    NetworkInfo info = cm.getActiveNetworkInfo();
                    return info != null && info.isConnected();
                }
                return false;
            }
        });

    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        if (intent != null && "co.in.wify.wifytech.ACTION_OPEN_CAPACITOR_APP".equals(intent.getAction())) {
            // Handle the intent here
            // For example, you can navigate to a specific page in your Capacitor app
            String menu = intent.getStringExtra("menu");
            String logout = intent.getStringExtra("tms-key");

            if (menu != null) {
                if (menu.equals("profile")) {
                    navigateToRoute("/profile");
                } else if (menu.equals("learning")) {
                    navigateToRoute("/learning");
                }
            }

            if ("logout_from_tms".equals(logout)) {
                SharedPreferences pref = getBaseContext().getSharedPreferences("CapacitorStorage",
                        Context.MODE_PRIVATE);
                SharedPreferences.Editor edit = pref.edit();
                edit.clear();
                edit.apply();
            }
        }
    }

    private void navigateToRoute(String route) {
        // Get the current URL from the WebView
        String currentUrl = this.bridge.getWebView().getUrl();

        if (currentUrl != null) {
            try {
                // Parse the current URL
                URL url = new URL(currentUrl);

                // Construct the new URL with the same protocol, hostname, and the appended
                // route
                String newUrl = url.getProtocol() + "://" + url.getHost() + route;

                // Load the new URL in the WebView
                this.bridge.getWebView().loadUrl(newUrl);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

}
