// DO NOT EDIT THIS FILE! IT IS GENERATED EACH TIME "capacitor update" IS RUN

android {
  compileOptions {
      sourceCompatibility JavaVersion.VERSION_17
      targetCompatibility JavaVersion.VERSION_17
  }
}

apply from: "../capacitor-cordova-android-plugins/cordova.variables.gradle"
dependencies {
    implementation project(':capacitor-community-contacts')
    implementation project(':capacitor-community-keep-awake')
    implementation project(':capacitor-app')
    implementation project(':capacitor-browser')
    implementation project(':capacitor-camera')
    implementation project(':capacitor-device')
    implementation project(':capacitor-filesystem')
    implementation project(':capacitor-geolocation')
    implementation project(':capacitor-preferences')
    implementation project(':capacitor-screen-orientation')
    implementation project(':capacitor-share')
    implementation project(':capacitor-status-bar')
    implementation project(':jonz94-capacitor-sim')
    implementation project(':capacitor-sms-retriever')

}


if (hasProperty('postBuildExtras')) {
  postBuildExtras()
}
