{"name": "onboarding-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --host", "build": "tsc && vite build", "lint": "npx eslint --max-warnings=4 --ext=ts,tsx src", "format": "prettier --ignore-path .gitignore --write \"src/*.+(ts|tsx|json)\"", "preview": "vite preview", "codegen": "graphql-codegen --config codegen.ts"}, "dependencies": {"@apollo/client": "^3.8.6", "@capacitor-community/contacts": "^6.1.1", "@capacitor-community/keep-awake": "^5.0.1", "@capacitor/android": "^6.1.2", "@capacitor/app": "^5.0.6", "@capacitor/browser": "^5.0.6", "@capacitor/camera": "^6.1.1", "@capacitor/core": "^6.1.2", "@capacitor/device": "^5.0.3", "@capacitor/filesystem": "^5.1.3", "@capacitor/geolocation": "4", "@capacitor/ios": "^6.0.0", "@capacitor/preferences": "^5.0.5", "@capacitor/screen-orientation": "^6.0.1", "@capacitor/share": "^6.0.2", "@capacitor/status-bar": "^5.0.6", "@chakra-ui/icons": "^2.0.19", "@chakra-ui/react": "^2.7.0", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@fontsource/montserrat": "^5.0.3", "@graphql-codegen/cli": "^4.0.1", "@graphql-codegen/typescript-operations": "^4.4.0", "@graphql-codegen/typescript-react-apollo": "^4.3.2", "@jonz94/capacitor-sim": "^3.0.0", "@portabletext/react": "^3.0.4", "@react-google-maps/api": "^2.19.2", "@sanity/client": "^6.1.7", "@sanity/image-url": "^1.0.2", "@types/is-empty": "^1.2.1", "@types/mixpanel-browser": "^2.49.0", "autoprefixer": "^10.4.20", "axios": "^1.6.4", "capacitor-sms-retriever": "^0.1.3", "dayjs": "^1.11.9", "framer-motion": "^10.12.16", "graphql": "^16.7.1", "graphql-ws": "^5.14.2", "html-react-parser": "^5.0.6", "html2canvas": "^1.4.1", "i18next": "^23.1.0", "is-empty": "^1.2.0", "jwt-decode": "^4.0.0", "logrocket": "^6.0.0", "lottie-react": "^2.4.0", "lucide-react": "^0.503.0", "mixpanel-browser": "^2.49.0", "next-share": "^0.23.0", "path-to-regexp": "^8.2.0", "pdfjs-dist": "^4.10.38", "qrcode.react": "^4.0.1", "react": "^18.2.0", "react-apollo-network-status": "^5.2.1", "react-circular-progressbar": "^2.1.0", "react-countdown": "^2.3.5", "react-dom": "^18.2.0", "react-hook-form": "^7.45.1", "react-i18next": "^13.0.0", "react-icons": "^4.10.1", "react-json-view": "^1.21.3", "react-lottie-player": "^1.5.5", "react-markdown": "^9.0.1", "react-pdf": "^9.2.1", "react-player": "^2.12.0", "react-responsive-carousel": "^3.2.23", "react-router-dom": "^6.13.0", "react-select": "^5.8.0", "react-simple-pull-to-refresh": "^1.3.3", "react-simple-typewriter": "^5.0.1", "react-slick": "^0.30.2", "react-swipeable": "^7.0.2", "react-swipeable-list": "^1.9.3", "react-youtube": "^10.1.0", "screenfull": "^6.0.2", "slick-carousel": "^1.8.1", "socket.io-client": "^4.7.2", "swiper": "^10.3.1", "uuid": "^9.0.0"}, "devDependencies": {"@capacitor/cli": "^6.1.2", "@types/react": "^18.0.37", "@types/react-dom": "^18.0.11", "@types/react-slick": "^0.23.13", "@types/uuid": "^9.0.2", "@typescript-eslint/eslint-plugin": "^7.9.0", "@typescript-eslint/parser": "^7.9.0", "@vitejs/plugin-react-swc": "^3.0.0", "eslint": "^8.38.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-react": "^7.34.1", "prettier": "^3.2.5", "tailwindcss": "^3.3.2", "typescript": "^5.0.2", "vite": "^4.3.9"}}