# Google Play In-App Updates Integration

This document provides comprehensive information about the Google Play In-App Updates integration in the Wify Android app.

## Overview

The In-App Updates system allows users to update the app without leaving the app or going to the Play Store. It provides two types of update flows:

- **Immediate Updates**: Critical updates that require immediate installation
- **Flexible Updates**: Non-critical updates that can be downloaded in the background

## Architecture

### Components

1. **Native Android Layer**
   - `InAppUpdateManager.java`: Core update logic and Google Play integration
   - `InAppUpdatePlugin.java`: Capacitor bridge between native and web layers
   - `MainActivity.java`: Plugin registration

2. **TypeScript Service Layer**
   - `InAppUpdateService.ts`: Main service interface with platform detection
   - Event-driven architecture with real-time notifications

3. **React Integration**
   - `useInAppUpdate.ts`: Complete state management hooks
   - `useUpdateChecker.ts`: Simplified update checking hook
   - `InAppUpdateNotification.tsx`: UI components for update notifications
   - `AppWithUpdates.tsx`: App-wide integration wrapper

### Update Logic

The system uses intelligent update type determination based on:

- **Immediate Updates**: Priority ≥ 4 OR staleness ≥ 7 days
- **Flexible Updates**: Priority ≥ 2 OR staleness ≥ 3 days
- **No Update**: Priority < 2 AND staleness < 3 days

## Installation

### 1. Android Dependencies

The required dependencies are already added to `android/app/build.gradle`:

```gradle
implementation 'com.google.android.play:app-update:2.1.0'
implementation 'com.google.android.play:app-update-ktx:2.1.0'
```

### 2. Plugin Registration

The plugin is registered in `MainActivity.java`:

```java
registerPlugin(InAppUpdatePlugin.class);
```

### 3. App Integration

The app is wrapped with the update system in `main.tsx`:

```tsx
<AppWithUpdates>
  <AppRoutes />
</AppWithUpdates>
```

## Usage

### Basic Integration

```tsx
import { useInAppUpdate } from '../hooks/useInAppUpdate';

function MyComponent() {
  const {
    updateAvailable,
    updateType,
    checkForUpdate,
    startImmediateUpdate,
    startFlexibleUpdate
  } = useInAppUpdate();

  // Check for updates
  const handleCheck = async () => {
    await checkForUpdate();
  };

  // Handle updates based on type
  const handleUpdate = async () => {
    if (updateType === 'immediate') {
      await startImmediateUpdate();
    } else {
      await startFlexibleUpdate();
    }
  };

  return (
    <div>
      {updateAvailable && (
        <button onClick={handleUpdate}>
          Update Available ({updateType})
        </button>
      )}
    </div>
  );
}
```

### Simplified Usage

```tsx
import { useUpdateChecker } from '../hooks/useInAppUpdate';

function SimpleChecker() {
  const { updateAvailable, updateType, checkForUpdate } = useUpdateChecker();

  return (
    <div>
      <button onClick={checkForUpdate}>Check Updates</button>
      {updateAvailable && <p>Update available: {updateType}</p>}
    </div>
  );
}
```

### App-Level Integration

```tsx
import { useAppUpdates } from '../components/AppWithUpdates';

function GlobalComponent() {
  const { isServiceAvailable, checkForUpdate } = useAppUpdates();

  if (!isServiceAvailable) {
    return <p>Updates not available on this platform</p>;
  }

  return <button onClick={checkForUpdate}>Check for Updates</button>;
}
```

## API Reference

### InAppUpdateService

Main service class providing update functionality:

```typescript
interface InAppUpdateService {
  initialize(): Promise<void>;
  checkForUpdate(): Promise<UpdateCheckResult>;
  startImmediateUpdate(): Promise<{ success: boolean; message: string }>;
  startFlexibleUpdate(): Promise<{ success: boolean; message: string }>;
  completeFlexibleUpdate(): Promise<{ success: boolean; message: string }>;
  getUpdateInfo(): Promise<UpdateCheckResult>;
  isAvailable(): boolean;
}
```

### useInAppUpdate Hook

Complete hook with all update functionality:

```typescript
interface UpdateState {
  updateAvailable: boolean;
  updateType: "immediate" | "flexible" | null;
  priority: number;
  stalenessDays: number;
  isChecking: boolean;
  isDownloading: boolean;
  isDownloaded: boolean;
  isInstalling: boolean;
  isInstalled: boolean;
  error: string | null;
  isCancelled: boolean;
  isServiceAvailable: boolean;
  platform: string;
}
```

### Events

The system emits the following events:

- `updateAvailable`: Update is available
- `updateNotAvailable`: No update available
- `updateDownloaded`: Flexible update downloaded
- `updateInstalled`: Update installed successfully
- `updateFailed`: Update failed with error
- `updateCancelled`: Update cancelled by user

## UI Components

### InAppUpdateNotification

Full-featured notification component:

```tsx
<InAppUpdateNotification
  isVisible={true}
  onDismiss={() => {}}
  position="top"
  autoCheck={true}
/>
```

### InAppUpdateBanner

Simple banner notification:

```tsx
<InAppUpdateBanner
  isVisible={true}
  onDismiss={() => {}}
/>
```

### AppWithUpdates

App wrapper with automatic update management:

```tsx
<AppWithUpdates
  showBanner={true}
  autoCheck={true}
  checkInterval={24 * 60 * 60 * 1000} // 24 hours
>
  <YourApp />
</AppWithUpdates>
```

## Testing

### Development Testing

1. **Build and Install**: Create a debug build and install on device
2. **Upload to Play Console**: Upload to Internal Testing track
3. **Create New Version**: Upload a higher version number
4. **Test Update Flow**: Install lower version and test update detection

### Testing Commands

```bash
# Build debug APK
cd android && ./gradlew assembleDebug

# Install on device
adb install app/build/outputs/apk/debug/app-debug.apk

# Check logs
adb logcat | grep InAppUpdate
```

### Mock Testing

For development, you can mock the update service:

```typescript
// Mock update available
inAppUpdateService.addEventListener('updateAvailable', () => {
  // Handle mock update
});
```

## Platform Support

- **Android**: Full support with Google Play In-App Updates
- **iOS**: Not supported (graceful degradation)
- **Web**: Not supported (graceful degradation)

The service automatically detects the platform and provides appropriate fallbacks.

## Error Handling

The system includes comprehensive error handling:

1. **Service Initialization**: Graceful fallback if plugin unavailable
2. **Update Failures**: Detailed error messages and retry mechanisms
3. **Network Issues**: Automatic retry with exponential backoff
4. **User Cancellation**: Proper state management for cancelled updates

## Best Practices

1. **Check Periodically**: Implement periodic update checks (daily recommended)
2. **Handle Errors**: Always provide error handling and retry mechanisms
3. **User Experience**: Use appropriate UI patterns for different update types
4. **Testing**: Test thoroughly on different devices and Android versions
5. **Monitoring**: Monitor update success rates and user behavior

## Troubleshooting

### Common Issues

1. **Plugin Not Found**: Ensure plugin is properly registered in MainActivity
2. **No Updates Detected**: Check Play Console configuration and version codes
3. **Update Fails**: Check device compatibility and Play Store version
4. **Events Not Firing**: Verify event listener setup and cleanup

### Debug Logging

Enable debug logging to troubleshoot issues:

```bash
adb logcat | grep -E "(InAppUpdate|Capacitor)"
```

## Version History

- **v1.0**: Initial implementation with immediate and flexible updates
- **v1.1**: Added priority-based update logic and improved error handling
- **v1.2**: Enhanced UI components and app-wide integration

## Support

For issues or questions about the In-App Updates integration:

1. Check the troubleshooting section
2. Review Android logs for error details
3. Consult Google Play In-App Updates documentation
4. Contact the development team
