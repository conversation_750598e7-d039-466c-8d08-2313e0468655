# Google Play In-App Updates Integration

This document provides comprehensive information about the Google Play In-App Updates integration in the Wify Android app.

## Overview

The In-App Updates system allows users to update the app without leaving the app or going to the Play Store. It provides two types of update flows:

- **Immediate Updates**: Critical updates that require immediate installation
- **Flexible Updates**: Non-critical updates that can be downloaded in the background

## Architecture

### Components

1. **Native Android Layer**

   - `InAppUpdateManager.java`: Core update logic and Google Play integration
   - `InAppUpdatePlugin.java`: Capacitor bridge between native and web layers
   - `MainActivity.java`: Plugin registration

2. **TypeScript Service Layer**

   - `InAppUpdateService.ts`: Main service interface with platform detection
   - Event-driven architecture with real-time notifications

3. **React Integration**
   - `useInAppUpdate.ts`: Complete state management hooks
   - `useUpdateChecker.ts`: Simplified update checking hook
   - `InAppUpdateNotification.tsx`: UI components for update notifications
   - `AppWithUpdates.tsx`: App-wide integration wrapper

### Update Logic

The system uses intelligent update type determination based on:

- **Immediate Updates**: Priority ≥ 4 OR staleness ≥ 7 days
- **Flexible Updates**: Priority ≥ 2 OR staleness ≥ 3 days
- **No Update**: Priority < 2 AND staleness < 3 days

## Installation

### 1. Android Dependencies

The required dependencies are already added to `android/app/build.gradle`:

```gradle
implementation 'com.google.android.play:app-update:2.1.0'
implementation 'com.google.android.play:app-update-ktx:2.1.0'
```

### 2. Plugin Registration

The plugin is registered in `MainActivity.java`:

```java
registerPlugin(InAppUpdatePlugin.class);
```

### 3. App Integration

The app is wrapped with the update system in `main.tsx`:

```tsx
<AppWithUpdates>
  <AppRoutes />
</AppWithUpdates>
```

## Usage

### Basic Integration

```tsx
import { useInAppUpdate } from "../hooks/useInAppUpdate";

function MyComponent() {
  const { updateAvailable, updateType, checkForUpdate, startImmediateUpdate, startFlexibleUpdate } =
    useInAppUpdate();

  // Check for updates
  const handleCheck = async () => {
    await checkForUpdate();
  };

  // Handle updates based on type
  const handleUpdate = async () => {
    if (updateType === "immediate") {
      await startImmediateUpdate();
    } else {
      await startFlexibleUpdate();
    }
  };

  return (
    <div>
      {updateAvailable && <button onClick={handleUpdate}>Update Available ({updateType})</button>}
    </div>
  );
}
```

### Simplified Usage

```tsx
import { useUpdateChecker } from "../hooks/useInAppUpdate";

function SimpleChecker() {
  const { updateAvailable, updateType, checkForUpdate } = useUpdateChecker();

  return (
    <div>
      <button onClick={checkForUpdate}>Check Updates</button>
      {updateAvailable && <p>Update available: {updateType}</p>}
    </div>
  );
}
```

### App-Level Integration

```tsx
import { useAppUpdates } from "../components/AppWithUpdates";

function GlobalComponent() {
  const { isServiceAvailable, checkForUpdate } = useAppUpdates();

  if (!isServiceAvailable) {
    return <p>Updates not available on this platform</p>;
  }

  return <button onClick={checkForUpdate}>Check for Updates</button>;
}
```

## API Reference

### InAppUpdateService

Main service class providing update functionality:

```typescript
interface InAppUpdateService {
  initialize(): Promise<void>;
  checkForUpdate(): Promise<UpdateCheckResult>;
  startImmediateUpdate(): Promise<{ success: boolean; message: string }>;
  startFlexibleUpdate(): Promise<{ success: boolean; message: string }>;
  completeFlexibleUpdate(): Promise<{ success: boolean; message: string }>;
  getUpdateInfo(): Promise<UpdateCheckResult>;
  isAvailable(): boolean;
}
```

### useInAppUpdate Hook

Complete hook with all update functionality:

```typescript
interface UpdateState {
  updateAvailable: boolean;
  updateType: "immediate" | "flexible" | null;
  priority: number;
  stalenessDays: number;
  isChecking: boolean;
  isDownloading: boolean;
  isDownloaded: boolean;
  isInstalling: boolean;
  isInstalled: boolean;
  error: string | null;
  isCancelled: boolean;
  isServiceAvailable: boolean;
  platform: string;
}
```

### Events

The system emits the following events:

- `updateAvailable`: Update is available
- `updateNotAvailable`: No update available
- `updateDownloaded`: Flexible update downloaded
- `updateInstalled`: Update installed successfully
- `updateFailed`: Update failed with error
- `updateCancelled`: Update cancelled by user

## UI Components

### InAppUpdateNotification

Full-featured notification component:

```tsx
<InAppUpdateNotification isVisible={true} onDismiss={() => {}} position="top" autoCheck={true} />
```

### InAppUpdateBanner

Simple banner notification:

```tsx
<InAppUpdateBanner isVisible={true} onDismiss={() => {}} />
```

### AppWithUpdates

App wrapper with automatic update management:

```tsx
<AppWithUpdates
  showBanner={true}
  autoCheck={true}
  checkInterval={24 * 60 * 60 * 1000} // 24 hours
>
  <YourApp />
</AppWithUpdates>
```

## Testing

### Development Testing

1. **Build and Install**: Create a debug build and install on device
2. **Upload to Play Console**: Upload to Internal Testing track
3. **Create New Version**: Upload a higher version number
4. **Test Update Flow**: Install lower version and test update detection

## Release Process for In-App Updates

### Pre-Release Checklist

Before releasing a new version that will trigger in-app updates, ensure the following steps are completed:

#### 1. Version Management

**Update Version Numbers:**

- Update `versionCode` and `versionName` in `android/app/build.gradle`
- Ensure `versionCode` is incremented by at least 1 from the previous release
- Update `versionName` to reflect the new version (e.g., "19", "20", etc.)

```gradle
// In android/app/build.gradle
defaultConfig {
    versionCode 19        // Increment this
    versionName "19"      // Update this
    // ... other config
}
```

**Frontend Package Version:**

- Update version in `frontend/package.json` if needed for consistency
- This doesn't affect Android updates but helps with version tracking

#### 2. Update Priority Configuration

**Set Update Priority in Google Play Console:**

- Navigate to Google Play Console → Your App → Release Management
- When uploading the new APK/AAB, set the update priority (0-5)
- **Priority Guidelines:**
  - **Priority 5**: Critical security fixes, app-breaking bugs (triggers immediate updates)
  - **Priority 4**: Important bug fixes, significant features (triggers immediate updates)
  - **Priority 3**: Regular updates, minor features (triggers flexible updates after 3 days)
  - **Priority 2**: Minor improvements (triggers flexible updates after 3 days)
  - **Priority 1**: Optional updates (triggers flexible updates after 3 days)
  - **Priority 0**: Very minor updates (no automatic update prompts)

#### 3. Release Notes and Metadata

**Prepare Release Notes:**

- Write clear, user-friendly release notes
- Highlight important changes that justify the update
- Include any breaking changes or new permissions

**Update Store Listing:**

- Update app description if new features are added
- Add new screenshots if UI has changed significantly
- Update app metadata as needed

#### 4. Build and Upload Process

**Create Release Build:**

```bash
# Navigate to frontend directory
cd frontend

# Install dependencies
npm install

# Build the web assets
npm run build

# Sync with Capacitor
npx cap sync android

# Navigate to android directory
cd android

# Create release build
./gradlew assembleRelease
# OR for App Bundle (recommended)
./gradlew bundleRelease
```

**Upload to Google Play Console:**

1. Go to Google Play Console → Your App → Release Management → App Releases
2. Choose the appropriate track (Internal Testing, Closed Testing, Open Testing, or Production)
3. Upload the APK or AAB file
4. **Set the update priority** (this is crucial for in-app updates)
5. Add release notes
6. Review and publish

#### 5. Staged Rollout Strategy

**For Production Releases:**

- Start with a small percentage rollout (e.g., 5-10%)
- Monitor crash reports and user feedback
- Gradually increase rollout percentage
- This allows you to catch issues before they affect all users

#### 6. Post-Release Monitoring

**Monitor Update Adoption:**

- Check Google Play Console for update adoption rates
- Monitor app crash reports for any new issues
- Watch user reviews for feedback on the update

**In-App Update Metrics:**

- Monitor logs for update check frequency
- Track successful vs. failed update attempts
- Monitor user behavior around update prompts

### Testing the Update Flow

#### Pre-Production Testing

1. **Internal Testing Track:**

   - Upload version N to Internal Testing
   - Install and test the app
   - Upload version N+1 with higher priority
   - Test that in-app updates are detected and work correctly

2. **Version Comparison Testing:**
   - Install an older version manually
   - Ensure the newer version is available on Play Store
   - Test that the app detects and offers the update

#### Testing Commands

```bash
# Build debug APK for testing
cd android && ./gradlew assembleDebug

# Install specific version on device
adb install app/build/outputs/apk/debug/app-debug.apk

# Check update-related logs
adb logcat | grep -E "(InAppUpdate|AppUpdateManager)"

# Clear app data to reset update state
adb shell pm clear co.in.wify.wifytech
```

### Mock Testing

For development, you can mock the update service:

```typescript
// Mock update available
inAppUpdateService.addEventListener("updateAvailable", () => {
  // Handle mock update
});
```

### Update Priority Decision Matrix

| Update Type   | Priority | Staleness Trigger | Use Cases                                  |
| ------------- | -------- | ----------------- | ------------------------------------------ |
| **Immediate** | 4-5      | 7+ days           | Critical bugs, security fixes, app crashes |
| **Flexible**  | 2-3      | 3+ days           | New features, improvements, minor fixes    |
| **Optional**  | 0-1      | No auto-trigger   | Very minor updates, cosmetic changes       |

### Troubleshooting Release Issues

**Common Release Problems:**

1. **Updates Not Detected:**

   - Verify version code is higher than installed version
   - Check that the app is published on the same track
   - Ensure device has Play Store access and is signed in

2. **Wrong Update Type:**

   - Review the priority setting in Play Console
   - Check the staleness calculation logic
   - Verify the update type determination logic

3. **Update Fails:**
   - Check device storage space
   - Verify app signing consistency
   - Review Play Console for any policy violations

## Automation Tools

### Version Management Scripts

The project includes several automation tools to streamline the release process:

#### Version CLI Tool

```bash
# Check current version status
npm run version-check

# Auto-increment version
npm run version-auto

# Manually set version
npm run version-update 20 "2.0.0"

# View version history
npm run version-history

# Show configuration
npm run version-config
```

#### Release Helper Tool

```bash
# Interactive release helper
npm run release-helper

# Run full release workflow
npm run release-workflow

# Check prerequisites only
npm run release-check
```

#### Version Check Integration

The build process automatically checks version consistency:

```bash
# Manual version check
npm run check-version

# Pre-build with version check
npm run pre-build
```

### Release Checklist

A comprehensive release checklist is available at `frontend/docs/RELEASE_CHECKLIST.md` that covers:

- Pre-release preparation
- Version management
- Build process
- Google Play Console configuration
- Post-release monitoring
- Emergency procedures

### Automated Workflows

The release helper provides guided workflows for:

1. **Prerequisites Check**: Verify all required files and tools
2. **Version Management**: Interactive version updating
3. **Pre-Release Checks**: Automated testing and validation
4. **Build Process**: Guided build creation
5. **Release Guidance**: Step-by-step release instructions

## Platform Support

- **Android**: Full support with Google Play In-App Updates
- **iOS**: Not supported (graceful degradation)
- **Web**: Not supported (graceful degradation)

The service automatically detects the platform and provides appropriate fallbacks.

## Error Handling

The system includes comprehensive error handling:

1. **Service Initialization**: Graceful fallback if plugin unavailable
2. **Update Failures**: Detailed error messages and retry mechanisms
3. **Network Issues**: Automatic retry with exponential backoff
4. **User Cancellation**: Proper state management for cancelled updates

## Best Practices

1. **Check Periodically**: Implement periodic update checks (daily recommended)
2. **Handle Errors**: Always provide error handling and retry mechanisms
3. **User Experience**: Use appropriate UI patterns for different update types
4. **Testing**: Test thoroughly on different devices and Android versions
5. **Monitoring**: Monitor update success rates and user behavior

## Troubleshooting

### Common Issues

1. **Plugin Not Found**: Ensure plugin is properly registered in MainActivity
2. **No Updates Detected**: Check Play Console configuration and version codes
3. **Update Fails**: Check device compatibility and Play Store version
4. **Events Not Firing**: Verify event listener setup and cleanup

### Debug Logging

Enable debug logging to troubleshoot issues:

```bash
adb logcat | grep -E "(InAppUpdate|Capacitor)"
```

## Version History

- **v1.0**: Initial implementation with immediate and flexible updates
- **v1.1**: Added priority-based update logic and improved error handling
- **v1.2**: Enhanced UI components and app-wide integration

## Support

For issues or questions about the In-App Updates integration:

1. Check the troubleshooting section
2. Review Android logs for error details
3. Consult Google Play In-App Updates documentation
4. Contact the development team
