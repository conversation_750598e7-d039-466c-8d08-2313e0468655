# Google Play In-App Updates Implementation

This document describes the implementation of Google Play In-App Updates for the Wify Android app, providing seamless update experiences for users.

## 🎯 Overview

The In-App Updates feature allows your app to prompt users to update to the latest version without leaving the app. This implementation supports both **immediate** and **flexible** update flows based on update priority and staleness.

## 🏗️ Architecture

### Components

1. **Native Android Layer**

   - `InAppUpdateManager.java` - Core update logic
   - `InAppUpdatePlugin.java` - Capacitor bridge
   - Google Play Core Library integration

2. **TypeScript Service Layer**

   - `InAppUpdateService.ts` - Main service interface
   - Event handling and state management

3. **React Integration**
   - `useInAppUpdate.ts` - React hooks
   - `InAppUpdateNotification.tsx` - UI components
   - `InAppUpdateExample.tsx` - Usage examples

## 🚀 Quick Start

### 1. Basic Integration

Add to your main App component:

```tsx
import { inAppUpdateService } from "./services/InAppUpdateService";
import { InAppUpdateNotification, InAppUpdateBanner } from "./components/InAppUpdateNotification";

function App() {
  useEffect(() => {
    inAppUpdateService.initialize();
  }, []);

  return (
    <div>
      <InAppUpdateBanner />
      {/* Your app content */}
      <InAppUpdateNotification />
    </div>
  );
}
```

### 2. Using React Hooks

```tsx
import { useInAppUpdate } from "./hooks/useInAppUpdate";

function UpdateButton() {
  const { checkForUpdate, isChecking, updateAvailable } = useInAppUpdate();

  return (
    <button onClick={checkForUpdate} disabled={isChecking}>
      {isChecking ? "Checking..." : "Check for Updates"}
    </button>
  );
}
```

### 3. Automatic Update Checking

```tsx
import { useAutoUpdateCheck } from "./hooks/useInAppUpdate";

function MyComponent() {
  const updateState = useAutoUpdateCheck(true, 3000); // Check after 3 seconds

  // Component automatically checks for updates on mount
  return <div>My Component</div>;
}
```

## 📋 Update Flow Types

### Immediate Updates

- **When**: High priority updates (priority ≥ 4) or updates older than 7 days
- **Behavior**: Forces user to update before continuing
- **UI**: Google Play handles the entire flow
- **Use Case**: Critical security updates, major bug fixes

### Flexible Updates

- **When**: Medium priority updates (priority ≥ 2) or updates older than 3 days
- **Behavior**: Downloads in background, user chooses when to install
- **UI**: Custom notification with install button
- **Use Case**: Feature updates, performance improvements

## 🎛️ Configuration

### Update Priority Levels

Set in Google Play Console using the Developer API:

```json
{
  "releases": [
    {
      "versionCodes": ["19"],
      "inAppUpdatePriority": 5,
      "status": "completed"
    }
  ]
}
```

- **0**: Default (no prompt)
- **1**: Low priority
- **2**: Medium priority (flexible update)
- **3**: High priority
- **4-5**: Critical priority (immediate update)

### Staleness Thresholds

- **3 days**: Suggest flexible update
- **7 days**: Force immediate update

## 🔧 API Reference

### InAppUpdateService

```typescript
// Check for updates
await inAppUpdateService.checkForUpdate();

// Start immediate update
await inAppUpdateService.startImmediateUpdate();

// Complete flexible update
await inAppUpdateService.completeFlexibleUpdate();

// Get update info without triggering update
await inAppUpdateService.getUpdateInfo();
```

### useInAppUpdate Hook

```typescript
const {
  // State
  isChecking,
  updateAvailable,
  updateType,
  priority,
  stalenessDays,
  updateDownloaded,
  updateInstalled,
  updateFailed,
  error,

  // Actions
  checkForUpdate,
  startImmediateUpdate,
  completeFlexibleUpdate,
  clearError,
  resetState
} = useInAppUpdate();
```

### Event Listeners

```typescript
inAppUpdateService.addEventListener("updateAvailable", (data) => {
  console.log("Update available:", data);
});

inAppUpdateService.addEventListener("updateDownloaded", (data) => {
  console.log("Update downloaded:", data);
});
```

## 🎨 UI Components

### InAppUpdateNotification

Full-featured notification component:

```tsx
<InAppUpdateNotification
  showPriority={true}
  showStaleness={true}
  autoHide={true}
  autoHideDelay={10000}
/>
```

### InAppUpdateBanner

Simple banner for downloaded updates:

```tsx
<InAppUpdateBanner className="sticky top-0 z-50" />
```

## 🧪 Testing

### Testing in Development

1. **Internal Testing Track**: Upload APK to internal testing
2. **Version Mismatch**: Install lower version, then upload higher version
3. **Priority Testing**: Set different priority levels in Play Console
4. **Staleness Testing**: Use older APKs to test staleness logic

### Testing Commands

```bash
# Check current version
npm run version-check

# Update version for testing
npm run version-update

# Build for testing
cd android && ./gradlew assembleDebug
```

## 🔍 Debugging

### Enable Logging

```typescript
// In development, enable detailed logging
console.log("📱 Update check result:", result);
```

### Common Issues

1. **Updates not showing**: Check app is published on Play Store
2. **Immediate updates not working**: Verify priority level ≥ 4
3. **Flexible updates not downloading**: Check network connectivity
4. **Plugin not found**: Ensure plugin is registered in MainActivity

### Debug Checklist

- [ ] App is published on Google Play
- [ ] Package ID matches Play Store listing
- [ ] App is signed with same key as Play Store version
- [ ] Version code is lower than Play Store version
- [ ] Google Play Services is available on device

## 🔗 Integration with Existing Version System

This implementation works alongside your existing version checking system:

1. **Automated checks**: Your current system checks versions before builds
2. **In-app updates**: This system handles runtime updates for users
3. **Complementary**: Both systems work together for complete version management

## 📱 Platform Support

- **Android**: Full support with Google Play Core Library
- **iOS**: Not supported (Apple doesn't allow in-app updates)
- **Web**: Gracefully degrades (no-op)

## 🚀 Deployment

### Production Checklist

- [ ] Google Play Core Library added to dependencies
- [ ] InAppUpdatePlugin registered in MainActivity
- [ ] Update priority configured in Play Console
- [ ] Testing completed on internal track
- [ ] UI components integrated
- [ ] Error handling implemented

### Release Process

1. Set update priority in Play Console
2. Upload new APK/AAB
3. Publish to production
4. Monitor update adoption rates
5. Handle any reported issues

## 📊 Monitoring

Track update success rates:

```typescript
inAppUpdateService.addEventListener("updateInstalled", () => {
  // Track successful updates
  analytics.track("update_installed");
});

inAppUpdateService.addEventListener("updateFailed", (data) => {
  // Track failed updates
  analytics.track("update_failed", { error: data.error });
});
```

## 🔮 Future Enhancements

- [ ] Custom update scheduling
- [ ] Update size information
- [ ] Bandwidth-aware downloads
- [ ] A/B testing for update flows
- [ ] Analytics integration
- [ ] Offline update queuing

## 📝 Integration Steps Summary

1. **Dependencies**: Already added to `build.gradle`
2. **Native Code**: `InAppUpdateManager.java` and `InAppUpdatePlugin.java` created
3. **Plugin Registration**: Added to `MainActivity.java`
4. **TypeScript Service**: `InAppUpdateService.ts` provides the main interface
5. **React Hooks**: `useInAppUpdate.ts` for easy component integration
6. **UI Components**: `InAppUpdateNotification.tsx` for user interface
7. **Examples**: `InAppUpdateExample.tsx` shows various usage patterns

### Next Steps

1. Build and test the Android app
2. Upload to Google Play Console internal testing
3. Set update priorities for releases
4. Integrate UI components into your app
5. Test update flows with different priority levels

The implementation is now complete and ready for testing!
