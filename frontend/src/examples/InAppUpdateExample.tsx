import {
  Alert,
  AlertDescription,
  AlertIcon,
  AlertTitle,
  Badge,
  Box,
  Button,
  Card,
  CardBody,
  CardHeader,
  Code,
  Divider,
  Heading,
  HStack,
  Progress,
  Stat,
  StatLabel,
  StatNumber,
  Text,
  useToast,
  VStack
} from "@chakra-ui/react";
import { useState } from "react";
import { InAppUpdateBanner, InAppUpdateNotification } from "../components/InAppUpdateNotification";
import { useInAppUpdate } from "../hooks/useInAppUpdate";

/**
 * Complete In-App Update Example Component
 *
 * This component demonstrates all features of the In-App Update system:
 * - Update checking and status display
 * - Immediate and flexible update flows
 * - Event handling and state management
 * - Error handling and recovery
 * - Integration patterns
 */
export function InAppUpdateExample() {
  const [showNotification, setShowNotification] = useState(false);
  const [showBanner, setShowBanner] = useState(false);

  const toast = useToast();

  // Full update hook with all features
  const updateHook = useInAppUpdate();

  const {
    updateAvailable,
    updateType,
    priority,
    stalenessDays,
    isChecking,
    isDownloading,
    isDownloaded,
    isInstalling,
    isInstalled,
    error,
    isCancelled,
    isServiceAvailable,
    platform,
    checkForUpdate,
    startImmediateUpdate,
    startFlexibleUpdate,
    completeFlexibleUpdate,
    getUpdateInfo,
    clearError,
    reset
  } = updateHook;

  /**
   * Handle manual update check
   */
  const handleCheckForUpdate = async () => {
    try {
      const result = await checkForUpdate();

      toast({
        title: "Update Check Complete",
        description: result.message,
        status: result.success ? "success" : "error",
        duration: 3000,
        isClosable: true
      });
    } catch (err) {
      toast({
        title: "Update Check Failed",
        description: String(err),
        status: "error",
        duration: 5000,
        isClosable: true
      });
    }
  };

  /**
   * Handle get update info
   */
  const handleGetUpdateInfo = async () => {
    try {
      const result = await getUpdateInfo();

      toast({
        title: "Update Info Retrieved",
        description: `Available: ${result.updateAvailable}, Type: ${result.updateType || "N/A"}`,
        status: result.success ? "info" : "warning",
        duration: 3000,
        isClosable: true
      });
    } catch (err) {
      toast({
        title: "Failed to Get Update Info",
        description: String(err),
        status: "error",
        duration: 5000,
        isClosable: true
      });
    }
  };

  /**
   * Handle immediate update
   */
  const handleImmediateUpdate = async () => {
    try {
      await startImmediateUpdate();

      toast({
        title: "Immediate Update Started",
        description: "The app will update and restart automatically",
        status: "info",
        duration: 3000,
        isClosable: true
      });
    } catch (err) {
      toast({
        title: "Immediate Update Failed",
        description: String(err),
        status: "error",
        duration: 5000,
        isClosable: true
      });
    }
  };

  /**
   * Handle flexible update
   */
  const handleFlexibleUpdate = async () => {
    try {
      if (isDownloaded) {
        await completeFlexibleUpdate();

        toast({
          title: "Installing Update",
          description: "The app will restart to complete the update",
          status: "info",
          duration: 3000,
          isClosable: true
        });
      } else {
        await startFlexibleUpdate();

        toast({
          title: "Flexible Update Started",
          description: "Update will download in the background",
          status: "info",
          duration: 3000,
          isClosable: true
        });
      }
    } catch (err) {
      toast({
        title: "Flexible Update Failed",
        description: String(err),
        status: "error",
        duration: 5000,
        isClosable: true
      });
    }
  };

  /**
   * Get status color based on current state
   */
  const getStatusColor = () => {
    if (error) return "red";
    if (isInstalled) return "green";
    if (updateAvailable) return "blue";
    if (isChecking || isDownloading || isInstalling) return "yellow";
    return "gray";
  };

  /**
   * Get current status text
   */
  const getStatusText = () => {
    if (error) return "Error";
    if (isInstalled) return "Installed";
    if (isInstalling) return "Installing";
    if (isDownloaded) return "Downloaded";
    if (isDownloading) return "Downloading";
    if (isChecking) return "Checking";
    if (updateAvailable) return "Available";
    if (isCancelled) return "Cancelled";
    return "Up to Date";
  };

  return (
    <Box p={6} maxW="4xl" mx="auto">
      <VStack spacing={6} align="stretch">
        {/* Header */}
        <Box>
          <Heading size="lg" mb={2}>
            In-App Update System Demo
          </Heading>
          <Text color="gray.600">
            Complete demonstration of Google Play In-App Updates integration
          </Text>
        </Box>

        {/* Service Status */}
        <Card>
          <CardHeader>
            <Heading size="md">Service Status</Heading>
          </CardHeader>
          <CardBody>
            <HStack spacing={4}>
              <Stat>
                <StatLabel>Platform</StatLabel>
                <StatNumber fontSize="lg">{platform}</StatNumber>
              </Stat>

              <Stat>
                <StatLabel>Service Available</StatLabel>
                <StatNumber fontSize="lg">
                  <Badge colorScheme={isServiceAvailable ? "green" : "red"}>
                    {isServiceAvailable ? "Yes" : "No"}
                  </Badge>
                </StatNumber>
              </Stat>

              <Stat>
                <StatLabel>Update Status</StatLabel>
                <StatNumber fontSize="lg">
                  <Badge colorScheme={getStatusColor()}>{getStatusText()}</Badge>
                </StatNumber>
              </Stat>
            </HStack>
          </CardBody>
        </Card>

        {/* Update Information */}
        {updateAvailable && (
          <Card>
            <CardHeader>
              <Heading size="md">Update Information</Heading>
            </CardHeader>
            <CardBody>
              <VStack align="stretch" spacing={3}>
                <HStack justify="space-between">
                  <Text fontWeight="medium">Update Type:</Text>
                  <Badge colorScheme={updateType === "immediate" ? "red" : "blue"}>
                    {updateType?.toUpperCase()}
                  </Badge>
                </HStack>

                <HStack justify="space-between">
                  <Text fontWeight="medium">Priority:</Text>
                  <Text>{priority}</Text>
                </HStack>

                <HStack justify="space-between">
                  <Text fontWeight="medium">Staleness:</Text>
                  <Text>{stalenessDays} days</Text>
                </HStack>

                {(isDownloading || isInstalling) && (
                  <Box>
                    <Text fontSize="sm" color="gray.600" mb={2}>
                      {isDownloading ? "Downloading..." : "Installing..."}
                    </Text>
                    <Progress size="sm" isIndeterminate />
                  </Box>
                )}
              </VStack>
            </CardBody>
          </Card>
        )}

        {/* Error Display */}
        {error && (
          <Alert status="error">
            <AlertIcon />
            <Box>
              <AlertTitle>Update Error</AlertTitle>
              <AlertDescription>{error}</AlertDescription>
            </Box>
          </Alert>
        )}

        {/* Control Buttons */}
        <Card>
          <CardHeader>
            <Heading size="md">Update Controls</Heading>
          </CardHeader>
          <CardBody>
            <VStack spacing={4} align="stretch">
              {/* Basic Controls */}
              <HStack spacing={3} wrap="wrap">
                <Button
                  onClick={handleCheckForUpdate}
                  isLoading={isChecking}
                  loadingText="Checking..."
                  colorScheme="blue"
                  isDisabled={!isServiceAvailable}
                >
                  Check for Update
                </Button>

                <Button
                  onClick={handleGetUpdateInfo}
                  variant="outline"
                  isDisabled={!isServiceAvailable}
                >
                  Get Update Info
                </Button>

                <Button onClick={clearError} variant="ghost" isDisabled={!error}>
                  Clear Error
                </Button>

                <Button onClick={reset} variant="ghost" colorScheme="red">
                  Reset State
                </Button>
              </HStack>

              <Divider />

              {/* Update Action Buttons */}
              <HStack spacing={3} wrap="wrap">
                <Button
                  onClick={handleImmediateUpdate}
                  colorScheme="red"
                  isLoading={isInstalling && updateType === "immediate"}
                  loadingText="Installing..."
                  isDisabled={!updateAvailable || updateType !== "immediate"}
                >
                  Start Immediate Update
                </Button>

                <Button
                  onClick={handleFlexibleUpdate}
                  colorScheme="green"
                  isLoading={isDownloading || (isInstalling && updateType === "flexible")}
                  loadingText={isDownloading ? "Downloading..." : "Installing..."}
                  isDisabled={!updateAvailable || updateType !== "flexible"}
                >
                  {isDownloaded ? "Install Update" : "Start Flexible Update"}
                </Button>
              </HStack>
            </VStack>
          </CardBody>
        </Card>

        {/* UI Component Demos */}
        <Card>
          <CardHeader>
            <Heading size="md">UI Component Demos</Heading>
          </CardHeader>
          <CardBody>
            <VStack spacing={4} align="stretch">
              <HStack spacing={3}>
                <Button onClick={() => setShowNotification(!showNotification)} variant="outline">
                  {showNotification ? "Hide" : "Show"} Notification
                </Button>

                <Button onClick={() => setShowBanner(!showBanner)} variant="outline">
                  {showBanner ? "Hide" : "Show"} Banner
                </Button>
              </HStack>

              <Text fontSize="sm" color="gray.600">
                These buttons toggle the display of update notification components
              </Text>
            </VStack>
          </CardBody>
        </Card>

        {/* Integration Examples */}
        <Card>
          <CardHeader>
            <Heading size="md">Integration Examples</Heading>
          </CardHeader>
          <CardBody>
            <VStack align="stretch" spacing={4}>
              <Box>
                <Text fontWeight="medium" mb={2}>
                  Basic Hook Usage:
                </Text>
                <Code p={3} borderRadius="md" display="block" whiteSpace="pre">
                  {`const { updateAvailable, checkForUpdate } = useInAppUpdate();

// Check for updates
await checkForUpdate();`}
                </Code>
              </Box>

              <Box>
                <Text fontWeight="medium" mb={2}>
                  Simplified Checker:
                </Text>
                <Code p={3} borderRadius="md" display="block" whiteSpace="pre">
                  {`const { updateAvailable, updateType } = useUpdateChecker();

// Just check status without actions`}
                </Code>
              </Box>

              <Box>
                <Text fontWeight="medium" mb={2}>
                  App-level Integration:
                </Text>
                <Code p={3} borderRadius="md" display="block" whiteSpace="pre">
                  {`const { isServiceAvailable, checkForUpdate } = useAppUpdates();

// Global update utilities`}
                </Code>
              </Box>
            </VStack>
          </CardBody>
        </Card>
      </VStack>

      {/* Demo Components */}
      <InAppUpdateNotification
        isVisible={showNotification}
        onDismiss={() => setShowNotification(false)}
        position="top"
        autoCheck={false}
      />

      <InAppUpdateBanner isVisible={showBanner} onDismiss={() => setShowBanner(false)} />
    </Box>
  );
}
