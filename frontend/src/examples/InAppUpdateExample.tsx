import React, { useEffect } from 'react';
import { inAppUpdateService } from '../services/InAppUpdateService';
import { useInAppUpdate, useAutoUpdateCheck } from '../hooks/useInAppUpdate';
import { InAppUpdateNotification, InAppUpdateBanner } from '../components/InAppUpdateNotification';

/**
 * Example implementation of In-App Updates
 * 
 * This component demonstrates how to integrate Google Play In-App Updates
 * into your React application with various usage patterns.
 */

// Example 1: Basic usage with manual check
export const BasicInAppUpdateExample: React.FC = () => {
  const {
    isChecking,
    updateAvailable,
    updateType,
    priority,
    stalenessDays,
    updateDownloaded,
    error,
    checkForUpdate,
    completeFlexibleUpdate,
    clearError,
  } = useInAppUpdate();

  return (
    <div className="p-6 max-w-md mx-auto bg-white rounded-lg shadow-md">
      <h2 className="text-xl font-bold mb-4">App Updates</h2>
      
      <div className="space-y-4">
        <button
          onClick={checkForUpdate}
          disabled={isChecking}
          className="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
        >
          {isChecking ? 'Checking...' : 'Check for Updates'}
        </button>

        {updateAvailable && (
          <div className="p-3 bg-blue-50 border border-blue-200 rounded-md">
            <p className="font-medium text-blue-800">Update Available!</p>
            <p className="text-sm text-blue-600">
              Type: {updateType} | Priority: {priority} | Days: {stalenessDays}
            </p>
          </div>
        )}

        {updateDownloaded && (
          <div className="p-3 bg-green-50 border border-green-200 rounded-md">
            <p className="font-medium text-green-800">Update Ready!</p>
            <button
              onClick={completeFlexibleUpdate}
              className="mt-2 px-3 py-1 bg-green-600 text-white text-sm rounded hover:bg-green-700"
            >
              Install Now
            </button>
          </div>
        )}

        {error && (
          <div className="p-3 bg-red-50 border border-red-200 rounded-md">
            <p className="font-medium text-red-800">Error</p>
            <p className="text-sm text-red-600">{error}</p>
            <button
              onClick={clearError}
              className="mt-2 px-3 py-1 bg-red-600 text-white text-sm rounded hover:bg-red-700"
            >
              Dismiss
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

// Example 2: Automatic update checking on app start
export const AutoUpdateExample: React.FC = () => {
  const updateState = useAutoUpdateCheck(true, 3000); // Check after 3 seconds

  return (
    <div className="p-6">
      <h2 className="text-xl font-bold mb-4">Auto Update Check</h2>
      <p className="text-gray-600">
        This component automatically checks for updates when mounted.
      </p>
      
      {updateState.isChecking && (
        <div className="mt-4 p-3 bg-gray-50 border rounded-md">
          <p>🔄 Checking for updates...</p>
        </div>
      )}
      
      <InAppUpdateNotification />
    </div>
  );
};

// Example 3: Integration with app initialization
export const AppWithInAppUpdates: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  useEffect(() => {
    // Initialize the in-app update service
    inAppUpdateService.initialize();
  }, []);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Update banner at the top */}
      <InAppUpdateBanner />
      
      {/* Main app content */}
      <main>{children}</main>
      
      {/* Update notification overlay */}
      <InAppUpdateNotification 
        showPriority={true}
        showStaleness={true}
        autoHide={true}
        autoHideDelay={10000}
      />
    </div>
  );
};

// Example 4: Settings page with update controls
export const UpdateSettingsPage: React.FC = () => {
  const {
    isChecking,
    updateAvailable,
    updateType,
    priority,
    stalenessDays,
    updateDownloaded,
    updateInstalled,
    updateFailed,
    error,
    checkForUpdate,
    getUpdateInfo,
    completeFlexibleUpdate,
    clearError,
    resetState,
  } = useInAppUpdate();

  const handleManualCheck = async () => {
    await checkForUpdate();
  };

  const handleGetInfo = async () => {
    await getUpdateInfo();
  };

  return (
    <div className="p-6 max-w-2xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">App Update Settings</h1>
      
      <div className="space-y-6">
        {/* Update Status */}
        <div className="bg-white p-4 rounded-lg shadow">
          <h2 className="text-lg font-semibold mb-3">Update Status</h2>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="font-medium">Update Available:</span>
              <span className={`ml-2 ${updateAvailable ? 'text-green-600' : 'text-gray-500'}`}>
                {updateAvailable ? 'Yes' : 'No'}
              </span>
            </div>
            <div>
              <span className="font-medium">Update Type:</span>
              <span className="ml-2 text-gray-700">{updateType || 'N/A'}</span>
            </div>
            <div>
              <span className="font-medium">Priority:</span>
              <span className="ml-2 text-gray-700">{priority || 'N/A'}</span>
            </div>
            <div>
              <span className="font-medium">Days Available:</span>
              <span className="ml-2 text-gray-700">{stalenessDays || 'N/A'}</span>
            </div>
          </div>
        </div>

        {/* Update Actions */}
        <div className="bg-white p-4 rounded-lg shadow">
          <h2 className="text-lg font-semibold mb-3">Update Actions</h2>
          <div className="flex flex-wrap gap-3">
            <button
              onClick={handleManualCheck}
              disabled={isChecking}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
            >
              {isChecking ? 'Checking...' : 'Check for Updates'}
            </button>
            
            <button
              onClick={handleGetInfo}
              disabled={isChecking}
              className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 disabled:opacity-50"
            >
              Get Update Info
            </button>
            
            {updateDownloaded && (
              <button
                onClick={completeFlexibleUpdate}
                className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
              >
                Install Update
              </button>
            )}
            
            <button
              onClick={resetState}
              className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400"
            >
              Reset State
            </button>
          </div>
        </div>

        {/* Status Messages */}
        {(updateInstalled || updateFailed || error) && (
          <div className="bg-white p-4 rounded-lg shadow">
            <h2 className="text-lg font-semibold mb-3">Status Messages</h2>
            
            {updateInstalled && (
              <div className="p-3 bg-green-50 border border-green-200 rounded-md mb-3">
                <p className="text-green-800">✅ Update installed successfully!</p>
              </div>
            )}
            
            {updateFailed && (
              <div className="p-3 bg-red-50 border border-red-200 rounded-md mb-3">
                <p className="text-red-800">❌ Update failed</p>
              </div>
            )}
            
            {error && (
              <div className="p-3 bg-red-50 border border-red-200 rounded-md mb-3">
                <p className="text-red-800">Error: {error}</p>
                <button
                  onClick={clearError}
                  className="mt-2 px-3 py-1 bg-red-600 text-white text-sm rounded hover:bg-red-700"
                >
                  Clear Error
                </button>
              </div>
            )}
          </div>
        )}

        {/* Integration Info */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <h2 className="text-lg font-semibold mb-3">Integration Info</h2>
          <div className="text-sm text-gray-600 space-y-2">
            <p>• Updates are checked automatically when the app starts</p>
            <p>• Immediate updates are handled automatically by Google Play</p>
            <p>• Flexible updates require user confirmation to install</p>
            <p>• Update priority determines the urgency of the update</p>
            <p>• Staleness days indicate how long the update has been available</p>
          </div>
        </div>
      </div>
    </div>
  );
};

// Example 5: Simple integration for existing apps
export const SimpleInAppUpdateIntegration: React.FC = () => {
  useEffect(() => {
    // Initialize and check for updates on app start
    const initializeUpdates = async () => {
      await inAppUpdateService.initialize();
    };
    
    initializeUpdates();
  }, []);

  return (
    <>
      {/* Just add these two components to your existing app */}
      <InAppUpdateBanner />
      <InAppUpdateNotification />
    </>
  );
};
