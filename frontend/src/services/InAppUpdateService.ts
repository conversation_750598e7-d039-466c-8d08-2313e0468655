import { Capacitor } from "@capacitor/core";

/**
 * Google Play In-App Updates Service for Wify App
 *
 * This service provides a TypeScript interface for Google Play In-App Updates,
 * integrating with the existing version checking system.
 */

export interface InAppUpdatePlugin {
  /**
   * Check for available updates
   */
  checkForUpdate(): Promise<UpdateCheckResult>;

  /**
   * Start immediate update flow
   */
  startImmediateUpdate(): Promise<{ success: boolean; message: string }>;

  /**
   * Complete flexible update (install downloaded update)
   */
  completeFlexibleUpdate(): Promise<{ success: boolean; message: string }>;

  /**
   * Get update info without triggering update flow
   */
  getUpdateInfo(): Promise<UpdateCheckResult>;

  /**
   * Add event listener
   */
  addListener(eventName: string, listenerFunc: (data: UpdateEventData) => void): Promise<void>;

  /**
   * Remove event listener
   */
  removeListener(eventName: string): Promise<void>;
}

export interface UpdateCheckResult {
  updateAvailable: boolean;
  updateType?: "immediate" | "flexible";
  priority?: number;
  stalenessDays?: number;
  success: boolean;
  message: string;
}

export interface UpdateEventData {
  updateAvailable?: boolean;
  updateType?: "immediate" | "flexible";
  priority?: number;
  stalenessDays?: number;
  downloaded?: boolean;
  installed?: boolean;
  failed?: boolean;
  cancelled?: boolean;
  error?: string;
}

/**
 * In-App Update Service
 *
 * Provides a unified interface for Google Play In-App Updates with
 * platform detection and graceful degradation for web/iOS.
 */
class InAppUpdateService {
  private plugin: InAppUpdatePlugin | null = null;
  private isInitialized = false;
  private eventListeners: Map<string, ((data: UpdateEventData) => void)[]> = new Map();

  /**
   * Initialize the service
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    // Only initialize on Android platform
    if (Capacitor.getPlatform() === "android") {
      try {
        this.plugin = InAppUpdate as any;

        // Set up event listeners
        this.setupEventListeners();

        console.log("✅ InAppUpdateService initialized successfully");
      } catch (error) {
        console.warn("⚠️ InAppUpdate plugin not available:", error);
        this.plugin = null;
      }
    } else {
      console.log("ℹ️ InAppUpdate service not available on this platform");
    }

    this.isInitialized = true;
  }

  /**
   * Set up native event listeners
   */
  private setupEventListeners(): void {
    if (!this.plugin) return;

    // Listen for update events from native layer
    InAppUpdate.addListener("updateAvailable", (data: UpdateEventData) => {
      this.notifyListeners("updateAvailable", data);
    });

    InAppUpdate.addListener("updateNotAvailable", (data: UpdateEventData) => {
      this.notifyListeners("updateNotAvailable", data);
    });

    InAppUpdate.addListener("updateDownloaded", (data: UpdateEventData) => {
      this.notifyListeners("updateDownloaded", data);
    });

    InAppUpdate.addListener("updateInstalled", (data: UpdateEventData) => {
      this.notifyListeners("updateInstalled", data);
    });

    InAppUpdate.addListener("updateFailed", (data: UpdateEventData) => {
      this.notifyListeners("updateFailed", data);
    });

    InAppUpdate.addListener("updateCancelled", (data: UpdateEventData) => {
      this.notifyListeners("updateCancelled", data);
    });
  }

  /**
   * Check for available updates
   */
  async checkForUpdate(): Promise<UpdateCheckResult> {
    if (!this.plugin) {
      return {
        updateAvailable: false,
        success: false,
        message: "In-App Updates not available on this platform"
      };
    }

    try {
      const result = await this.plugin.checkForUpdate();
      return result;
    } catch (error) {
      console.error("Failed to check for updates:", error);
      return {
        updateAvailable: false,
        success: false,
        message: `Failed to check for updates: ${error}`
      };
    }
  }

  /**
   * Start immediate update flow
   */
  async startImmediateUpdate(): Promise<{ success: boolean; message: string }> {
    if (!this.plugin) {
      return {
        success: false,
        message: "In-App Updates not available on this platform"
      };
    }

    try {
      return await this.plugin.startImmediateUpdate();
    } catch (error) {
      console.error("Failed to start immediate update:", error);
      return {
        success: false,
        message: `Failed to start immediate update: ${error}`
      };
    }
  }

  /**
   * Start flexible update flow
   */
  async startFlexibleUpdate(): Promise<{ success: boolean; message: string }> {
    if (!this.plugin) {
      return {
        success: false,
        message: "In-App Updates not available on this platform"
      };
    }

    try {
      // For flexible updates, we call startFlexibleUpdate if available,
      // otherwise fall back to the generic method
      if ("startFlexibleUpdate" in this.plugin) {
        return await (this.plugin as any).startFlexibleUpdate();
      } else {
        // Fallback - this would need to be implemented in the plugin
        return {
          success: false,
          message: "Flexible update method not available"
        };
      }
    } catch (error) {
      console.error("Failed to start flexible update:", error);
      return {
        success: false,
        message: `Failed to start flexible update: ${error}`
      };
    }
  }

  /**
   * Complete flexible update (install downloaded update)
   */
  async completeFlexibleUpdate(): Promise<{ success: boolean; message: string }> {
    if (!this.plugin) {
      return {
        success: false,
        message: "In-App Updates not available on this platform"
      };
    }

    try {
      return await this.plugin.completeFlexibleUpdate();
    } catch (error) {
      console.error("Failed to complete flexible update:", error);
      return {
        success: false,
        message: `Failed to complete flexible update: ${error}`
      };
    }
  }

  /**
   * Get update info without triggering update flow
   */
  async getUpdateInfo(): Promise<UpdateCheckResult> {
    if (!this.plugin) {
      return {
        updateAvailable: false,
        success: false,
        message: "In-App Updates not available on this platform"
      };
    }

    try {
      return await this.plugin.getUpdateInfo();
    } catch (error) {
      console.error("Failed to get update info:", error);
      return {
        updateAvailable: false,
        success: false,
        message: `Failed to get update info: ${error}`
      };
    }
  }

  /**
   * Add event listener
   */
  addEventListener(eventName: string, listener: (data: UpdateEventData) => void): void {
    if (!this.eventListeners.has(eventName)) {
      this.eventListeners.set(eventName, []);
    }
    this.eventListeners.get(eventName)!.push(listener);
  }

  /**
   * Remove event listener
   */
  removeEventListener(eventName: string, listener?: (data: UpdateEventData) => void): void {
    if (!this.eventListeners.has(eventName)) {
      return;
    }

    if (listener) {
      const listeners = this.eventListeners.get(eventName)!;
      const index = listeners.indexOf(listener);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    } else {
      // Remove all listeners for this event
      this.eventListeners.set(eventName, []);
    }
  }

  /**
   * Notify all listeners for an event
   */
  private notifyListeners(eventName: string, data: UpdateEventData): void {
    const listeners = this.eventListeners.get(eventName);
    if (listeners) {
      listeners.forEach((listener) => {
        try {
          listener(data);
        } catch (error) {
          console.error(`Error in ${eventName} listener:`, error);
        }
      });
    }
  }

  /**
   * Check if the service is available on this platform
   */
  isAvailable(): boolean {
    return Capacitor.getPlatform() === "android" && this.plugin !== null;
  }

  /**
   * Get platform information
   */
  getPlatformInfo(): { platform: string; available: boolean } {
    return {
      platform: Capacitor.getPlatform(),
      available: this.isAvailable()
    };
  }
}

// Export singleton instance
export const inAppUpdateService = new InAppUpdateService();

// Import the plugin for use in the service
import { registerPlugin } from "@capacitor/core";

declare global {
  interface Window {
    InAppUpdate: InAppUpdatePlugin;
  }
}

// Register the plugin with Capacitor
const InAppUpdate = registerPlugin<InAppUpdatePlugin>("InAppUpdate");
