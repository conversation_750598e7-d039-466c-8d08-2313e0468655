import { registerPlugin } from '@capacitor/core';
import { Capacitor } from '@capacitor/core';

/**
 * In-App Update Service for Wify Android App
 * 
 * This service provides a TypeScript interface for Google Play In-App Updates,
 * integrating with the existing version checking system.
 */

export interface InAppUpdatePlugin {
  /**
   * Check for available updates
   */
  checkForUpdate(): Promise<UpdateCheckResult>;
  
  /**
   * Start immediate update flow
   */
  startImmediateUpdate(): Promise<{ success: boolean; message: string }>;
  
  /**
   * Complete flexible update (install downloaded update)
   */
  completeFlexibleUpdate(): Promise<{ success: boolean; message: string }>;
  
  /**
   * Get update info without triggering update flow
   */
  getUpdateInfo(): Promise<UpdateCheckResult>;
}

export interface UpdateCheckResult {
  updateAvailable: boolean;
  updateType?: 'immediate' | 'flexible';
  priority?: number;
  stalenessDays?: number;
  message?: string;
  status?: 'downloaded' | 'installed' | 'failed' | 'cancelled';
  error?: string;
}

export interface UpdateEventData {
  updateAvailable?: boolean;
  updateType?: 'immediate' | 'flexible';
  priority?: number;
  stalenessDays?: number;
  status?: 'downloaded' | 'installed' | 'failed' | 'cancelled';
  message?: string;
  error?: string;
}

// Register the plugin
const InAppUpdate = registerPlugin<InAppUpdatePlugin>('InAppUpdate');

/**
 * In-App Update Service Class
 */
export class InAppUpdateService {
  private static instance: InAppUpdateService;
  private listeners: Map<string, (data: UpdateEventData) => void> = new Map();
  private isListening = false;

  private constructor() {
    this.setupEventListeners();
  }

  /**
   * Get singleton instance
   */
  public static getInstance(): InAppUpdateService {
    if (!InAppUpdateService.instance) {
      InAppUpdateService.instance = new InAppUpdateService();
    }
    return InAppUpdateService.instance;
  }

  /**
   * Setup event listeners for update events
   */
  private setupEventListeners() {
    if (!Capacitor.isNativePlatform() || this.isListening) {
      return;
    }

    this.isListening = true;

    // Listen for update events from native layer
    InAppUpdate.addListener('updateAvailable', (data: UpdateEventData) => {
      console.log('📱 Update available:', data);
      this.notifyListeners('updateAvailable', data);
    });

    InAppUpdate.addListener('updateNotAvailable', (data: UpdateEventData) => {
      console.log('📱 No update available:', data);
      this.notifyListeners('updateNotAvailable', data);
    });

    InAppUpdate.addListener('updateDownloaded', (data: UpdateEventData) => {
      console.log('📱 Update downloaded:', data);
      this.notifyListeners('updateDownloaded', data);
    });

    InAppUpdate.addListener('updateInstalled', (data: UpdateEventData) => {
      console.log('📱 Update installed:', data);
      this.notifyListeners('updateInstalled', data);
    });

    InAppUpdate.addListener('updateFailed', (data: UpdateEventData) => {
      console.error('📱 Update failed:', data);
      this.notifyListeners('updateFailed', data);
    });

    InAppUpdate.addListener('updateCancelled', (data: UpdateEventData) => {
      console.log('📱 Update cancelled:', data);
      this.notifyListeners('updateCancelled', data);
    });
  }

  /**
   * Add event listener
   */
  public addEventListener(event: string, callback: (data: UpdateEventData) => void): void {
    this.listeners.set(event, callback);
  }

  /**
   * Remove event listener
   */
  public removeEventListener(event: string): void {
    this.listeners.delete(event);
  }

  /**
   * Notify all listeners
   */
  private notifyListeners(event: string, data: UpdateEventData): void {
    const callback = this.listeners.get(event);
    if (callback) {
      callback(data);
    }
  }

  /**
   * Check for available updates
   */
  public async checkForUpdate(): Promise<UpdateCheckResult> {
    if (!Capacitor.isNativePlatform()) {
      console.log('📱 In-app updates only available on native platforms');
      return { updateAvailable: false, message: 'Not available on web platform' };
    }

    try {
      console.log('📱 Checking for app updates...');
      const result = await InAppUpdate.checkForUpdate();
      console.log('📱 Update check result:', result);
      return result;
    } catch (error) {
      console.error('📱 Failed to check for updates:', error);
      throw error;
    }
  }

  /**
   * Start immediate update flow
   */
  public async startImmediateUpdate(): Promise<{ success: boolean; message: string }> {
    if (!Capacitor.isNativePlatform()) {
      return { success: false, message: 'Not available on web platform' };
    }

    try {
      console.log('📱 Starting immediate update...');
      const result = await InAppUpdate.startImmediateUpdate();
      console.log('📱 Immediate update result:', result);
      return result;
    } catch (error) {
      console.error('📱 Failed to start immediate update:', error);
      throw error;
    }
  }

  /**
   * Complete flexible update
   */
  public async completeFlexibleUpdate(): Promise<{ success: boolean; message: string }> {
    if (!Capacitor.isNativePlatform()) {
      return { success: false, message: 'Not available on web platform' };
    }

    try {
      console.log('📱 Completing flexible update...');
      const result = await InAppUpdate.completeFlexibleUpdate();
      console.log('📱 Flexible update completion result:', result);
      return result;
    } catch (error) {
      console.error('📱 Failed to complete flexible update:', error);
      throw error;
    }
  }

  /**
   * Get update info without triggering update
   */
  public async getUpdateInfo(): Promise<UpdateCheckResult> {
    if (!Capacitor.isNativePlatform()) {
      return { updateAvailable: false, message: 'Not available on web platform' };
    }

    try {
      console.log('📱 Getting update info...');
      const result = await InAppUpdate.getUpdateInfo();
      console.log('📱 Update info:', result);
      return result;
    } catch (error) {
      console.error('📱 Failed to get update info:', error);
      throw error;
    }
  }

  /**
   * Check for updates with automatic handling based on priority
   */
  public async checkAndHandleUpdate(): Promise<void> {
    try {
      const updateInfo = await this.checkForUpdate();
      
      if (updateInfo.updateAvailable) {
        console.log(`📱 Update available: ${updateInfo.updateType} (Priority: ${updateInfo.priority})`);
        
        if (updateInfo.updateType === 'immediate') {
          // Immediate updates are handled automatically by the native layer
          console.log('📱 Immediate update will be triggered automatically');
        } else if (updateInfo.updateType === 'flexible') {
          // For flexible updates, you might want to show a custom UI
          console.log('📱 Flexible update available - consider showing user notification');
          this.showFlexibleUpdateNotification(updateInfo);
        }
      } else {
        console.log('📱 No update available');
      }
    } catch (error) {
      console.error('📱 Error checking for updates:', error);
    }
  }

  /**
   * Show notification for flexible update
   */
  private showFlexibleUpdateNotification(updateInfo: UpdateCheckResult): void {
    // This is where you would integrate with your app's notification system
    // For example, showing a toast, modal, or banner
    console.log('📱 Showing flexible update notification:', updateInfo);
    
    // Example: You could dispatch a custom event that your UI components listen to
    const event = new CustomEvent('flexibleUpdateAvailable', { 
      detail: updateInfo 
    });
    window.dispatchEvent(event);
  }

  /**
   * Initialize the service and check for updates on app start
   */
  public async initialize(): Promise<void> {
    if (!Capacitor.isNativePlatform()) {
      console.log('📱 In-app updates not available on web platform');
      return;
    }

    console.log('📱 Initializing In-App Update Service...');
    
    // Check for updates on app start
    setTimeout(() => {
      this.checkAndHandleUpdate();
    }, 2000); // Wait 2 seconds after app start
  }
}

// Export singleton instance
export const inAppUpdateService = InAppUpdateService.getInstance();

// Export the plugin for direct access if needed
export { InAppUpdate };
