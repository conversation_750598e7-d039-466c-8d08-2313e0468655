import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, In<PERSON><PERSON>ory<PERSON><PERSON>, createHttpLink, split } from "@apollo/client";
import { setContext } from "@apollo/client/link/context";
import { GraphQLWsLink } from "@apollo/client/link/subscriptions";
import { getMainDefinition } from "@apollo/client/utilities";
import { Chakra<PERSON>rovider, Image } from "@chakra-ui/react";
import axios from "axios";
import { createClient } from "graphql-ws";
import React, { useEffect, useState } from "react";
import { createNetworkStatusNotifier } from "react-apollo-network-status";
import ReactDOM from "react-dom/client";
import { BrowserRouter } from "react-router-dom";
import AppRoutes from "./AppRoutes";
import { AppWithUpdates } from "./components/AppWithUpdates";
import { NetworkStatus } from "./components/NetworkStatus";
import "./index.css";
import { getToken } from "./utils/auth";

const { link, useApolloNetworkStatus } = createNetworkStatusNotifier();

/**
 * Custom hook to track Axios network status
 */
function useAxiosNetworkStatus() {
  const [numPendingRequests, setNumPendingRequests] = useState(0);

  useEffect(() => {
    const requestInterceptor = axios.interceptors.request.use((request) => {
      setNumPendingRequests((prev) => prev + 1);
      return request;
    });

    const responseInterceptor = axios.interceptors.response.use(
      (response) => {
        setNumPendingRequests((prev) => prev - 1);
        return response;
      },
      (error) => {
        setNumPendingRequests((prev) => prev - 1);
        return Promise.reject(error);
      }
    );

    // Cleanup interceptors on unmount
    return () => {
      axios.interceptors.request.eject(requestInterceptor);
      axios.interceptors.response.eject(responseInterceptor);
    };
  }, []);

  return { numPendingRequests };
}

/**
 * A global loading indicator that will be rendered when there are pending queries
 * @returns a component that will rendered when there are pending queries
 */
function GlobalLoadingIndicator() {
  const apolloStatus = useApolloNetworkStatus();
  const axiosStatus = useAxiosNetworkStatus();
  const location = window.location.pathname;

  // Disable loading indicator for some pages
  const disabledPaths = ["/sp/home/<USER>"];
  if (disabledPaths.some((path) => location.startsWith(path))) {
    return null;
  }

  const isLoading = apolloStatus.numPendingQueries > 0 || axiosStatus.numPendingRequests > 0;

  if (isLoading) {
    return (
      <div className="w-full h-screen flex justify-center items-center ">
        <Image src="/images/loading.gif" alt="Loading" className="w-[50px] h-[50px]" />
      </div>
    );
  } else {
    return null;
  }
}

const httpLink = createHttpLink({
  uri: import.meta.env.VITE_BACKEND_URL + "/graphql"
});

const wsLink = new GraphQLWsLink(
  createClient({
    url: import.meta.env.VITE_WEB_SOCKET_URL + "/graphql"
  })
);

const authLink = setContext(async (_, { headers }) => {
  const token = await getToken();
  return {
    headers: {
      ...headers,
      Authorization: `JWT ${token || ""}`
    }
  };
});

const splitLink = split(
  ({ query }) => {
    const definition = getMainDefinition(query);
    return definition.kind === "OperationDefinition" && definition.operation === "subscription";
  },
  wsLink,
  link.concat(authLink.concat(httpLink))
);

const client = new ApolloClient({
  uri: import.meta.env.VITE_BACKEND_URL + "/graphql",
  cache: new InMemoryCache(),
  connectToDevTools: true,
  link: splitLink
});

const root = ReactDOM.createRoot(document.getElementById("root") as HTMLElement);

// Register Service Worker
if ("serviceWorker" in navigator) {
  window.addEventListener("load", () => {
    navigator.serviceWorker.register("/sw.js").catch(() => console.log("SW registration failed"));
  });
}

root.render(
  <React.StrictMode>
    <ChakraProvider>
      <ApolloProvider client={client}>
        <NetworkStatus />
        <GlobalLoadingIndicator />
        <AppWithUpdates>
          <BrowserRouter>
            <AppRoutes />
          </BrowserRouter>
        </AppWithUpdates>
      </ApolloProvider>
    </ChakraProvider>
  </React.StrictMode>
);
