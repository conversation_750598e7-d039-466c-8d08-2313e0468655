import { useState, useEffect, useCallback } from 'react';
import { inAppUpdateService, UpdateCheckResult, UpdateEventData } from '../services/InAppUpdateService';

/**
 * React Hook for In-App Updates
 * 
 * This hook provides a convenient way to integrate Google Play In-App Updates
 * into React components with state management and event handling.
 */

export interface UseInAppUpdateState {
  // Update status
  isChecking: boolean;
  updateAvailable: boolean;
  updateType: 'immediate' | 'flexible' | null;
  priority: number | null;
  stalenessDays: number | null;
  
  // Update flow status
  isUpdating: boolean;
  updateDownloaded: boolean;
  updateInstalled: boolean;
  updateFailed: boolean;
  updateCancelled: boolean;
  
  // Error handling
  error: string | null;
  
  // Actions
  checkForUpdate: () => Promise<void>;
  startImmediateUpdate: () => Promise<void>;
  completeFlexibleUpdate: () => Promise<void>;
  getUpdateInfo: () => Promise<void>;
  clearError: () => void;
  resetState: () => void;
}

export const useInAppUpdate = (): UseInAppUpdateState => {
  // State management
  const [isChecking, setIsChecking] = useState(false);
  const [updateAvailable, setUpdateAvailable] = useState(false);
  const [updateType, setUpdateType] = useState<'immediate' | 'flexible' | null>(null);
  const [priority, setPriority] = useState<number | null>(null);
  const [stalenessDays, setStalenessDays] = useState<number | null>(null);
  
  const [isUpdating, setIsUpdating] = useState(false);
  const [updateDownloaded, setUpdateDownloaded] = useState(false);
  const [updateInstalled, setUpdateInstalled] = useState(false);
  const [updateFailed, setUpdateFailed] = useState(false);
  const [updateCancelled, setUpdateCancelled] = useState(false);
  
  const [error, setError] = useState<string | null>(null);

  // Event handlers
  const handleUpdateAvailable = useCallback((data: UpdateEventData) => {
    setUpdateAvailable(true);
    setUpdateType(data.updateType || null);
    setPriority(data.priority || null);
    setStalenessDays(data.stalenessDays || null);
    setIsChecking(false);
    setIsUpdating(true);
  }, []);

  const handleUpdateNotAvailable = useCallback((data: UpdateEventData) => {
    setUpdateAvailable(false);
    setUpdateType(null);
    setPriority(null);
    setStalenessDays(null);
    setIsChecking(false);
    setIsUpdating(false);
  }, []);

  const handleUpdateDownloaded = useCallback((data: UpdateEventData) => {
    setUpdateDownloaded(true);
    setIsUpdating(false);
  }, []);

  const handleUpdateInstalled = useCallback((data: UpdateEventData) => {
    setUpdateInstalled(true);
    setUpdateDownloaded(false);
    setIsUpdating(false);
  }, []);

  const handleUpdateFailed = useCallback((data: UpdateEventData) => {
    setUpdateFailed(true);
    setError(data.error || 'Update failed');
    setIsUpdating(false);
    setIsChecking(false);
  }, []);

  const handleUpdateCancelled = useCallback((data: UpdateEventData) => {
    setUpdateCancelled(true);
    setIsUpdating(false);
    setIsChecking(false);
  }, []);

  // Setup event listeners
  useEffect(() => {
    inAppUpdateService.addEventListener('updateAvailable', handleUpdateAvailable);
    inAppUpdateService.addEventListener('updateNotAvailable', handleUpdateNotAvailable);
    inAppUpdateService.addEventListener('updateDownloaded', handleUpdateDownloaded);
    inAppUpdateService.addEventListener('updateInstalled', handleUpdateInstalled);
    inAppUpdateService.addEventListener('updateFailed', handleUpdateFailed);
    inAppUpdateService.addEventListener('updateCancelled', handleUpdateCancelled);

    return () => {
      inAppUpdateService.removeEventListener('updateAvailable');
      inAppUpdateService.removeEventListener('updateNotAvailable');
      inAppUpdateService.removeEventListener('updateDownloaded');
      inAppUpdateService.removeEventListener('updateInstalled');
      inAppUpdateService.removeEventListener('updateFailed');
      inAppUpdateService.removeEventListener('updateCancelled');
    };
  }, [
    handleUpdateAvailable,
    handleUpdateNotAvailable,
    handleUpdateDownloaded,
    handleUpdateInstalled,
    handleUpdateFailed,
    handleUpdateCancelled
  ]);

  // Action functions
  const checkForUpdate = useCallback(async () => {
    try {
      setIsChecking(true);
      setError(null);
      await inAppUpdateService.checkForUpdate();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to check for updates');
      setIsChecking(false);
    }
  }, []);

  const startImmediateUpdate = useCallback(async () => {
    try {
      setError(null);
      setIsUpdating(true);
      await inAppUpdateService.startImmediateUpdate();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to start immediate update');
      setIsUpdating(false);
    }
  }, []);

  const completeFlexibleUpdate = useCallback(async () => {
    try {
      setError(null);
      await inAppUpdateService.completeFlexibleUpdate();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to complete flexible update');
    }
  }, []);

  const getUpdateInfo = useCallback(async () => {
    try {
      setIsChecking(true);
      setError(null);
      await inAppUpdateService.getUpdateInfo();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to get update info');
      setIsChecking(false);
    }
  }, []);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  const resetState = useCallback(() => {
    setIsChecking(false);
    setUpdateAvailable(false);
    setUpdateType(null);
    setPriority(null);
    setStalenessDays(null);
    setIsUpdating(false);
    setUpdateDownloaded(false);
    setUpdateInstalled(false);
    setUpdateFailed(false);
    setUpdateCancelled(false);
    setError(null);
  }, []);

  return {
    // State
    isChecking,
    updateAvailable,
    updateType,
    priority,
    stalenessDays,
    isUpdating,
    updateDownloaded,
    updateInstalled,
    updateFailed,
    updateCancelled,
    error,
    
    // Actions
    checkForUpdate,
    startImmediateUpdate,
    completeFlexibleUpdate,
    getUpdateInfo,
    clearError,
    resetState,
  };
};

/**
 * Hook for automatic update checking on component mount
 */
export const useAutoUpdateCheck = (checkOnMount = true, delay = 2000) => {
  const updateHook = useInAppUpdate();

  useEffect(() => {
    if (checkOnMount) {
      const timer = setTimeout(() => {
        updateHook.checkForUpdate();
      }, delay);

      return () => clearTimeout(timer);
    }
  }, [checkOnMount, delay, updateHook.checkForUpdate]);

  return updateHook;
};

/**
 * Hook for listening to flexible update notifications
 */
export const useFlexibleUpdateNotification = () => {
  const [flexibleUpdateAvailable, setFlexibleUpdateAvailable] = useState<UpdateCheckResult | null>(null);

  useEffect(() => {
    const handleFlexibleUpdate = (event: CustomEvent) => {
      setFlexibleUpdateAvailable(event.detail);
    };

    window.addEventListener('flexibleUpdateAvailable', handleFlexibleUpdate as EventListener);

    return () => {
      window.removeEventListener('flexibleUpdateAvailable', handleFlexibleUpdate as EventListener);
    };
  }, []);

  const dismissNotification = useCallback(() => {
    setFlexibleUpdateAvailable(null);
  }, []);

  return {
    flexibleUpdateAvailable,
    dismissNotification,
  };
};
