import { useCallback, useEffect, useState } from "react";
import {
  inAppUpdateService,
  UpdateCheckResult,
  UpdateEventData
} from "../services/InAppUpdateService";

/**
 * Update state interface
 */
export interface UpdateState {
  // Update availability
  updateAvailable: boolean;
  updateType: "immediate" | "flexible" | null;
  priority: number;
  stalenessDays: number;

  // Update flow states
  isChecking: boolean;
  isDownloading: boolean;
  isDownloaded: boolean;
  isInstalling: boolean;
  isInstalled: boolean;

  // Error states
  error: string | null;
  isCancelled: boolean;

  // Service availability
  isServiceAvailable: boolean;
  platform: string;
}

/**
 * Initial update state
 */
const initialState: UpdateState = {
  updateAvailable: false,
  updateType: null,
  priority: 0,
  stalenessDays: 0,
  isChecking: false,
  isDownloading: false,
  isDownloaded: false,
  isInstalling: false,
  isInstalled: false,
  error: null,
  isCancelled: false,
  isServiceAvailable: false,
  platform: "unknown"
};

/**
 * Main hook for In-App Updates
 *
 * Provides complete state management and control methods for Google Play In-App Updates.
 * Handles all update flows, state transitions, and error handling.
 */
export function useInAppUpdate() {
  const [state, setState] = useState<UpdateState>(initialState);

  /**
   * Update state helper
   */
  const updateState = useCallback((updates: Partial<UpdateState>) => {
    setState((prev) => ({ ...prev, ...updates }));
  }, []);

  /**
   * Initialize the service and set up event listeners
   */
  useEffect(() => {
    let mounted = true;

    const initializeService = async () => {
      try {
        await inAppUpdateService.initialize();

        if (!mounted) return;

        const platformInfo = inAppUpdateService.getPlatformInfo();
        updateState({
          isServiceAvailable: platformInfo.available,
          platform: platformInfo.platform
        });

        // Set up event listeners
        const handleUpdateAvailable = (data: UpdateEventData) => {
          if (!mounted) return;
          updateState({
            updateAvailable: true,
            updateType: data.updateType || null,
            priority: data.priority || 0,
            stalenessDays: data.stalenessDays || 0,
            isChecking: false,
            error: null
          });
        };

        const handleUpdateNotAvailable = (_data: UpdateEventData) => {
          if (!mounted) return;
          updateState({
            updateAvailable: false,
            updateType: null,
            isChecking: false,
            error: null
          });
        };

        const handleUpdateDownloaded = (_data: UpdateEventData) => {
          if (!mounted) return;
          updateState({
            isDownloading: false,
            isDownloaded: true,
            error: null
          });
        };

        const handleUpdateInstalled = (_data: UpdateEventData) => {
          if (!mounted) return;
          updateState({
            isInstalling: false,
            isInstalled: true,
            error: null
          });
        };

        const handleUpdateFailed = (data: UpdateEventData) => {
          if (!mounted) return;
          updateState({
            isChecking: false,
            isDownloading: false,
            isInstalling: false,
            error: data.error || "Update failed",
            isCancelled: false
          });
        };

        const handleUpdateCancelled = (_data: UpdateEventData) => {
          if (!mounted) return;
          updateState({
            isChecking: false,
            isDownloading: false,
            isInstalling: false,
            isCancelled: true,
            error: null
          });
        };

        // Add event listeners
        inAppUpdateService.addEventListener("updateAvailable", handleUpdateAvailable);
        inAppUpdateService.addEventListener("updateNotAvailable", handleUpdateNotAvailable);
        inAppUpdateService.addEventListener("updateDownloaded", handleUpdateDownloaded);
        inAppUpdateService.addEventListener("updateInstalled", handleUpdateInstalled);
        inAppUpdateService.addEventListener("updateFailed", handleUpdateFailed);
        inAppUpdateService.addEventListener("updateCancelled", handleUpdateCancelled);

        // Cleanup function
        return () => {
          inAppUpdateService.removeEventListener("updateAvailable", handleUpdateAvailable);
          inAppUpdateService.removeEventListener("updateNotAvailable", handleUpdateNotAvailable);
          inAppUpdateService.removeEventListener("updateDownloaded", handleUpdateDownloaded);
          inAppUpdateService.removeEventListener("updateInstalled", handleUpdateInstalled);
          inAppUpdateService.removeEventListener("updateFailed", handleUpdateFailed);
          inAppUpdateService.removeEventListener("updateCancelled", handleUpdateCancelled);
        };
      } catch (error) {
        if (!mounted) return;
        console.error("Failed to initialize InAppUpdateService:", error);
        updateState({
          error: `Failed to initialize update service: ${error}`,
          isServiceAvailable: false
        });
      }
    };

    initializeService();

    return () => {
      mounted = false;
    };
  }, [updateState]);

  /**
   * Check for available updates
   */
  const checkForUpdate = useCallback(async (): Promise<UpdateCheckResult> => {
    if (!state.isServiceAvailable) {
      const result: UpdateCheckResult = {
        updateAvailable: false,
        success: false,
        message: "Update service not available on this platform"
      };
      return result;
    }

    updateState({ isChecking: true, error: null });

    try {
      const result = await inAppUpdateService.checkForUpdate();

      if (!result.success) {
        updateState({
          isChecking: false,
          error: result.message
        });
      }

      return result;
    } catch (error) {
      const errorMessage = `Failed to check for updates: ${error}`;
      updateState({
        isChecking: false,
        error: errorMessage
      });

      return {
        updateAvailable: false,
        success: false,
        message: errorMessage
      };
    }
  }, [state.isServiceAvailable, updateState]);

  /**
   * Start immediate update
   */
  const startImmediateUpdate = useCallback(async () => {
    if (!state.isServiceAvailable) {
      updateState({ error: "Update service not available on this platform" });
      return;
    }

    updateState({ isInstalling: true, error: null });

    try {
      const result = await inAppUpdateService.startImmediateUpdate();

      if (!result.success) {
        updateState({
          isInstalling: false,
          error: result.message
        });
      }
    } catch (error) {
      updateState({
        isInstalling: false,
        error: `Failed to start immediate update: ${error}`
      });
    }
  }, [state.isServiceAvailable, updateState]);

  /**
   * Start flexible update
   */
  const startFlexibleUpdate = useCallback(async () => {
    if (!state.isServiceAvailable) {
      updateState({ error: "Update service not available on this platform" });
      return;
    }

    updateState({ isDownloading: true, error: null });

    try {
      const result = await inAppUpdateService.startFlexibleUpdate();

      if (!result.success) {
        updateState({
          isDownloading: false,
          error: result.message
        });
      }
    } catch (error) {
      updateState({
        isDownloading: false,
        error: `Failed to start flexible update: ${error}`
      });
    }
  }, [state.isServiceAvailable, updateState]);

  /**
   * Complete flexible update (install downloaded update)
   */
  const completeFlexibleUpdate = useCallback(async () => {
    if (!state.isServiceAvailable) {
      updateState({ error: "Update service not available on this platform" });
      return;
    }

    updateState({ isInstalling: true, error: null });

    try {
      const result = await inAppUpdateService.completeFlexibleUpdate();

      if (!result.success) {
        updateState({
          isInstalling: false,
          error: result.message
        });
      }
    } catch (error) {
      updateState({
        isInstalling: false,
        error: `Failed to complete flexible update: ${error}`
      });
    }
  }, [state.isServiceAvailable, updateState]);

  /**
   * Get update info without triggering update
   */
  const getUpdateInfo = useCallback(async (): Promise<UpdateCheckResult> => {
    if (!state.isServiceAvailable) {
      return {
        updateAvailable: false,
        success: false,
        message: "Update service not available on this platform"
      };
    }

    try {
      return await inAppUpdateService.getUpdateInfo();
    } catch (error) {
      return {
        updateAvailable: false,
        success: false,
        message: `Failed to get update info: ${error}`
      };
    }
  }, [state.isServiceAvailable]);

  /**
   * Clear error state
   */
  const clearError = useCallback(() => {
    updateState({ error: null, isCancelled: false });
  }, [updateState]);

  /**
   * Reset all state
   */
  const reset = useCallback(() => {
    setState((prev) => ({
      ...initialState,
      isServiceAvailable: prev.isServiceAvailable,
      platform: prev.platform
    }));
  }, []);

  return {
    // State
    ...state,

    // Actions
    checkForUpdate,
    startImmediateUpdate,
    startFlexibleUpdate,
    completeFlexibleUpdate,
    getUpdateInfo,
    clearError,
    reset
  };
}

/**
 * Simplified hook for basic update checking
 */
export function useUpdateChecker() {
  const {
    updateAvailable,
    updateType,
    priority,
    stalenessDays,
    isChecking,
    error,
    isServiceAvailable,
    checkForUpdate,
    clearError
  } = useInAppUpdate();

  return {
    updateAvailable,
    updateType,
    priority,
    stalenessDays,
    isChecking,
    error,
    isServiceAvailable,
    checkForUpdate,
    clearError
  };
}
