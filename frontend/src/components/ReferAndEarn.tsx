import { Contacts } from "@capacitor-community/contacts";
import { Capacitor } from "@capacitor/core";
import { Share } from "@capacitor/share";
import {
  Box,
  Button,
  Divider,
  Flex,
  Grid,
  Input,
  Text,
  useClipboard,
  useToast
} from "@chakra-ui/react";
import mixpanel from "mixpanel-browser";
import { WhatsappShareButton } from "next-share";
import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { PiCopyThin, PiShareNetworkThin, PiWhatsappLogoThin } from "react-icons/pi";
import {
  OnboardingStage,
  useCreateContactsMutation,
  useGenerateReferralCodeQuery,
  useGetLatestReferralConfigurationQuery
} from "../__generated__";
import { getContactPermission } from "../services/CapacitorInit";
import { chunkArray } from "../utils/common";

interface ReferAndEarnProps {
  name: string;
}

const ReferAndEarn: React.FC<ReferAndEarnProps> = ({ name }) => {
  const { setValue } = useClipboard("");
  const { data: referral } = useGenerateReferralCodeQuery();
  const { data } = useGetLatestReferralConfigurationQuery({
    variables: {
      filter: OnboardingStage.SignUp
    }
  });
  const toast = useToast();
  const { t } = useTranslation();
  const userName = name;
  const newUserPoints = data?.getLatestReferralConfiguration?.new_user_points;
  const [buttonLoading, setButtonLoading] = useState(false);

  //GraphQl Mutation
  const [createContacts] = useCreateContactsMutation();

  const syncContact = async () => {
    try {
      console.log("Syncing contacts...");

      if (Capacitor.getPlatform() !== "android") return null;
      setButtonLoading(true);

      // Request permission to access contacts
      await getContactPermission();
      setButtonLoading(false);

      // Retrieve contacts from the device
      const contacts = await Contacts.getContacts({
        projection: {
          phones: true,
          emails: true,
          name: true
        }
      });
      console.log("Fetched contacts:", contacts);

      // Batch size for sending contacts
      const batchSize = 500;
      const contactChunks = chunkArray(contacts.contacts, batchSize);

      for (const chunk of contactChunks) {
        await createContacts({
          variables: {
            contacts: JSON.stringify(chunk)
          }
        });
      }

      console.log("All contacts synced successfully.");
    } catch (error) {
      setButtonLoading(false);
      console.error("Error syncing contacts:", error);
    }
  };

  const handleShare = async () => {
    try {
      if (Capacitor.getPlatform() === "android") {
        syncContact();
        await Share.share({
          url: `${import.meta.env.VITE_FRONTEND_URL}`,
          text: t("share_message"),
          title: t("profile_share"),
          dialogTitle: "Share with friends"
        });
      }

      if (Capacitor.getPlatform() === "android" && navigator.share) {
        mixpanel.track("share_clicked", {
          "Share Clicked ": "Yes",
          Source: "Refer and Earn",
          "Refer Id": referral?.generateReferralCodeFrUser
        });
        await navigator.share({
          url: `${import.meta.env.VITE_FRONTEND_URL}/signup?referral_code=${
            referral?.generateReferralCodeFrUser
          }`
        });
      } else {
        await navigator.clipboard.writeText(
          `${import.meta.env.VITE_FRONTEND_URL}/signup?referral_code=${
            referral?.generateReferralCodeFrUser
          }`
        );
        toast({
          position: "top",
          title: t("error_link_copy"),
          status: "info"
        });
      }
    } catch (error) {
      console.log(`Error Sharing `, error);
      setButtonLoading(false);
      await syncContact();
    }
  };

  return (
    <Box backgroundColor={"#fff"} my={"2"} rounded={"lg"}>
      <Box
        display={"flex"}
        justifyContent={"space-between"}
        alignItems={"center"}
        position={"relative"}
      >
        <Text fontWeight={"semibold"} fontSize={"xl"} p={3}>
          {t("profile_refer")}
        </Text>
      </Box>
      <Divider />
      <Box p={2} position={"relative"}>
        <Box className="w-4/5">
          <Text fontSize={"sm"} className="md:text-start">
            {t("profile_refer_friends")}
          </Text>
          <Flex mb={2}>
            <Input
              value={referral?.generateReferralCodeFrUser || ""}
              onChange={(e) => {
                setValue(e.target.value);
              }}
              size={"md"}
              textAlign={"center"}
              mr={2}
            />
          </Flex>
        </Box>
        <Box
          className="-left-20 -top-14"
          position={"absolute"}
          backgroundImage={"url(/confetti.svg)"}
          width={"full"}
          height={50}
          backgroundPosition={"center"}
          backgroundSize={"contain"}
          backgroundRepeat={"no-repeat"}
        />
        <Box
          className="right-0 -top-20"
          position={"absolute"}
          backgroundImage={"url(/giftbox.png?v=123)"}
          width={150}
          height={150}
          backgroundPosition={"center"}
          backgroundSize={"contain"}
          backgroundRepeat={"no-repeat"}
        />
      </Box>
      <Divider />
      <Grid gap={2} padding={2} templateColumns={"repeat(2 , 1fr)"}>
        <Button onClick={handleShare} isLoading={buttonLoading} size={"sm"}>
          <Box display={"flex"} justifyContent={"center"} alignItems={"center"} gap={2}>
            {Capacitor.getPlatform() === "android" ? (
              <>
                <PiShareNetworkThin className="text-xl" /> {t("profile_share")}
              </>
            ) : (
              <>
                <PiCopyThin className="text-xl" /> {t("profile_copy")}
              </>
            )}
          </Box>
        </Button>
        <Button size={"sm"}>
          <WhatsappShareButton
            url={`${import.meta.env.VITE_FRONTEND_URL}/sign_up?referral_code=${
              referral?.generateReferralCodeFrUser
            }`}
            onClick={() => {
              mixpanel.track("referral_whatsapp_clicked", {
                Source: "Refer and Earn",
                ReferralCode: referral?.generateReferralCodeFrUser
              });
            }}
            title={t("error_link_signup", { userName, newUserPoints })}
            separator=":: "
          >
            <Box display={"flex"} gap={2}>
              <PiWhatsappLogoThin className="text-xl text-green-600" /> {t("profile_whatsapp")}
            </Box>
          </WhatsappShareButton>
        </Button>
      </Grid>
    </Box>
  );
};

export default ReferAndEarn;
