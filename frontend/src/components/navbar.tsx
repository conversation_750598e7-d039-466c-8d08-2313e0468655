import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>r,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DrawerOverlay,
  Grid,
  GridItem,
  Image,
  Text,
  Tooltip,
  useDisclosure
} from "@chakra-ui/react";
import { IoMdLogOut } from "react-icons/io";

import { <PERSON>rowser } from "@capacitor/browser";
import { Capacitor } from "@capacitor/core";
import { useContext, useState } from "react";
import { useTranslation } from "react-i18next";
import { LuMenu } from "react-icons/lu";
import { useLocation, useNavigate } from "react-router-dom";
import {
  useBellNotificationSubscription,
  useGetBellNotificationCountQuery,
  useUserDetailsQuery
} from "../__generated__";
import { OneAppIntegrationContext } from "../providers/OneAppIntegration";
import { logoutUser } from "../utils/auth";
import Error from "./Error";
import LanguageModal from "./LanguageModal";

interface NavbarProps {
  menuData: {
    id: string;
    title: string;
    icon: JSX.Element;
    link: string;
  }[];
}

const Navbar: React.FC<NavbarProps> = ({ menuData }) => {
  const [languageChangeShow, setLanguageChangeShow] = useState<boolean>(false);
  const { isOpen, onOpen, onClose } = useDisclosure();
  const { t } = useTranslation();
  const location = useLocation();

  //Graphql Query
  const { data: userDetails, error: userDetailsError } = useUserDetailsQuery();

  //Graphql Subscription
  const { data: bellData } = useBellNotificationSubscription({
    variables: {
      userId: userDetails?.userDetail?.data?.id || -1
    }
  });

  const { data: bellNotificationCount } = useGetBellNotificationCountQuery({
    fetchPolicy: "no-cache"
  });

  //constants
  const userData = userDetails?.userDetail?.data;
  const bellCount =
    bellData?.bellNotification || bellNotificationCount?.getBellNotificationCount || 0;
  const navigate = useNavigate();
  const ttt_data = useContext(OneAppIntegrationContext);
  const isInTechnicianRoute = location.pathname.includes("/technician/registration");

  const openAboutUsUrl = async (aboutUrl: string) => {
    await Browser.open({ url: aboutUrl });
  };

  const handleProfileClick = () => {
    if (isInTechnicianRoute) {
      console.log("Profile click blocked - in technician registration route");
      return;
    }
    console.log("Profile click allowed - navigating to profile");
    navigate("/profile");
    onClose();
  };

  if (userDetailsError) {
    return <Error error={userDetailsError} />;
  }

  if (ttt_data !== null) {
    return null;
  }

  return (
    <>
      <Box className="fixed bg-white backdrop-blur-xl top-0 left-0 right-0 z-[500]">
        <Box className="w-full gap-x-5 md:container md:mx-auto flex justify-between pl-1 pr-1">
          <div className="flex items-center ">
            <LuMenu
              style={{ cursor: "pointer" }}
              onClick={() => {
                onOpen();
              }}
              size={28}
            />
            <Image className="p-2" src="/wify_logo.webp" width={110} />
          </div>
          <Box className="flex items-center gap-3 mr-1">
            <Tooltip label="Change Language">
              <Box
                onClick={() => {
                  setLanguageChangeShow(true);
                }}
                className="cursor-pointer "
              >
                <img src="/icons/language_change.png" width={30} />
              </Box>
            </Tooltip>
            {userData?.meta?.is_user_journey_completed && (
              <Box className="relative">
                <div className="absolute -top-1 -right-1 w-4 h-4 flex justify-center items-center bg-red-600 rounded-full text-white text-xs">
                  {bellCount}
                </div>
                {bellCount <= 0 ? (
                  <img
                    src="/icons/no_notification.png"
                    onClick={() => {
                      navigate("/notification?filter=%7B%7D");
                      onClose();
                    }}
                    style={{ cursor: "pointer" }}
                    width={24}
                  />
                ) : (
                  <img
                    className="rounded-full"
                    src="/icons/notification.gif"
                    onClick={() => {
                      navigate("/notification");
                      onClose();
                    }}
                    style={{ cursor: "pointer" }}
                    width={24}
                  />
                )}
              </Box>
            )}
          </Box>
        </Box>
      </Box>
      <Drawer isOpen={isOpen} placement="left" onClose={onClose} size={"full"}>
        <DrawerOverlay />
        <DrawerContent>
          <DrawerCloseButton size={"md"} />
          <DrawerHeader>
            <Grid
              templateColumns={"repeat(5, 1fr)"}
              onClick={handleProfileClick}
              className={`${isInTechnicianRoute ? "pointer-events-none" : "cursor-pointer"}`}
              style={{
                opacity: isInTechnicianRoute ? 0.5 : 1
              }}
            >
              <GridItem colSpan={1}>
                <Avatar
                  src={userData?.photoUrl as string}
                  name={userData?.name || "error while fetching name"}
                />
              </GridItem>
              <GridItem colSpan={4}>
                <Text fontSize={"large"}>{userData?.name}</Text>
                <Text fontSize={"small"} color={isInTechnicianRoute ? "gray.500" : "inherit"}>
                  {userData?.phone ? userData?.phone : userData?.email}
                </Text>
              </GridItem>
            </Grid>
          </DrawerHeader>
          <DrawerBody>
            {menuData.map((item) => (
              <div key={`${item.id}_menu`}>
                <Box
                  className="w-full px-4 py-3 cursor-pointer hover:bg-gray-50"
                  onClick={() => {
                    if (item.link.startsWith("http")) {
                      if (Capacitor.getPlatform() == "android") {
                        openAboutUsUrl(item.link);
                      } else {
                        window.location.replace(item.link);
                      }
                    }
                    navigate(`${item.link}`);
                    onClose();
                  }}
                >
                  <Box display="flex" alignItems="center" gap={3}>
                    {item.icon}
                    <Text fontSize="large">{item.title}</Text>
                  </Box>
                </Box>
                <Divider />
              </div>
            ))}
          </DrawerBody>
          <DrawerFooter>
            <Box width={"100%"}>
              <Divider />
              <Button variant={"link"} className="w-full">
                <Box width={"100%"}>
                  <Box width={"100%"} my={3} display={"flex"} alignItems={"center"} gap={3}>
                    <IoMdLogOut className="text-red-500" size={34} />
                    <Box fontSize={"large"} onClick={() => logoutUser(true)}>
                      <Text textColor={"black"}>{t("sidemenu_logout")}</Text>
                    </Box>
                  </Box>
                </Box>
              </Button>
            </Box>
          </DrawerFooter>
        </DrawerContent>
      </Drawer>
      <LanguageModal onClose={() => setLanguageChangeShow(false)} isOpen={languageChangeShow} />
    </>
  );
};

export default Navbar;
