import React from "react";
import { useFlexibleUpdateNotification, useInAppUpdate } from "../hooks/useInAppUpdate";

/**
 * In-App Update Notification Component
 *
 * This component displays update notifications and handles user interactions
 * for both immediate and flexible update flows.
 */

interface InAppUpdateNotificationProps {
  className?: string;
  showPriority?: boolean;
  showStaleness?: boolean;
  autoHide?: boolean;
  autoHideDelay?: number;
}

export const InAppUpdateNotification: React.FC<InAppUpdateNotificationProps> = ({
  className = "",
  showPriority = true,
  showStaleness = true,
  autoHide = false,
  autoHideDelay = 5000
}) => {
  const {
    updateAvailable,
    updateType,
    priority,
    stalenessDays,
    updateDownloaded,
    updateInstalled,
    updateFailed,
    updateCancelled,
    error,
    isUpdating,
    completeFlexibleUpdate,
    clearError
  } = useInAppUpdate();

  const { flexibleUpdateAvailable, dismissNotification } = useFlexibleUpdateNotification();

  // Auto-hide functionality
  React.useEffect(() => {
    if (autoHide && (updateInstalled || updateCancelled)) {
      const timer = setTimeout(() => {
        clearError();
      }, autoHideDelay);
      return () => clearTimeout(timer);
    }
  }, [autoHide, autoHideDelay, updateInstalled, updateCancelled, clearError]);

  // Don't render if no update-related state is active
  if (
    !updateAvailable &&
    !updateDownloaded &&
    !updateInstalled &&
    !updateFailed &&
    !updateCancelled &&
    !error &&
    !flexibleUpdateAvailable
  ) {
    return null;
  }

  const getNotificationStyle = () => {
    if (error || updateFailed) return "bg-red-50 border-red-200 text-red-800";
    if (updateInstalled) return "bg-green-50 border-green-200 text-green-800";
    if (updateDownloaded) return "bg-blue-50 border-blue-200 text-blue-800";
    if (updateCancelled) return "bg-yellow-50 border-yellow-200 text-yellow-800";
    return "bg-blue-50 border-blue-200 text-blue-800";
  };

  const getIcon = () => {
    if (error || updateFailed) return "❌";
    if (updateInstalled) return "✅";
    if (updateDownloaded) return "📥";
    if (updateCancelled) return "⚠️";
    if (isUpdating) return "⏳";
    return "🔄";
  };

  const renderUpdateMessage = () => {
    if (error) {
      return (
        <div>
          <p className="font-medium">Update Error</p>
          <p className="text-sm">{error}</p>
        </div>
      );
    }

    if (updateFailed) {
      return (
        <div>
          <p className="font-medium">Update Failed</p>
          <p className="text-sm">The app update could not be completed. Please try again later.</p>
        </div>
      );
    }

    if (updateInstalled) {
      return (
        <div>
          <p className="font-medium">Update Installed</p>
          <p className="text-sm">Your app has been successfully updated!</p>
        </div>
      );
    }

    if (updateCancelled) {
      return (
        <div>
          <p className="font-medium">Update Cancelled</p>
          <p className="text-sm">
            The update was cancelled. You can check for updates again later.
          </p>
        </div>
      );
    }

    if (updateDownloaded) {
      return (
        <div>
          <p className="font-medium">Update Ready to Install</p>
          <p className="text-sm">A new version has been downloaded and is ready to install.</p>
          {showPriority && priority && <p className="text-xs mt-1">Priority: {priority}/5</p>}
          {showStaleness && stalenessDays && (
            <p className="text-xs">Available for {stalenessDays} days</p>
          )}
        </div>
      );
    }

    if (flexibleUpdateAvailable) {
      return (
        <div>
          <p className="font-medium">App Update Available</p>
          <p className="text-sm">A new version of the app is available for download.</p>
          {showPriority && flexibleUpdateAvailable.priority && (
            <p className="text-xs mt-1">Priority: {flexibleUpdateAvailable.priority}/5</p>
          )}
          {showStaleness && flexibleUpdateAvailable.stalenessDays && (
            <p className="text-xs">Available for {flexibleUpdateAvailable.stalenessDays} days</p>
          )}
        </div>
      );
    }

    if (updateAvailable && updateType === "flexible") {
      return (
        <div>
          <p className="font-medium">Update Available</p>
          <p className="text-sm">A new version is available for download.</p>
          {showPriority && priority && <p className="text-xs mt-1">Priority: {priority}/5</p>}
          {showStaleness && stalenessDays && (
            <p className="text-xs">Available for {stalenessDays} days</p>
          )}
        </div>
      );
    }

    if (isUpdating) {
      return (
        <div>
          <p className="font-medium">Updating...</p>
          <p className="text-sm">Please wait while the update is being processed.</p>
        </div>
      );
    }

    return null;
  };

  const renderActions = () => {
    if (updateDownloaded) {
      return (
        <div className="flex gap-2 mt-3">
          <button
            onClick={completeFlexibleUpdate}
            className="px-4 py-2 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700 transition-colors"
          >
            Install Now
          </button>
          <button
            onClick={clearError}
            className="px-4 py-2 bg-gray-300 text-gray-700 text-sm rounded-md hover:bg-gray-400 transition-colors"
          >
            Later
          </button>
        </div>
      );
    }

    if (flexibleUpdateAvailable) {
      return (
        <div className="flex gap-2 mt-3">
          <button
            onClick={dismissNotification}
            className="px-4 py-2 bg-gray-300 text-gray-700 text-sm rounded-md hover:bg-gray-400 transition-colors"
          >
            Dismiss
          </button>
        </div>
      );
    }

    if (error || updateFailed) {
      return (
        <div className="flex gap-2 mt-3">
          <button
            onClick={clearError}
            className="px-4 py-2 bg-gray-300 text-gray-700 text-sm rounded-md hover:bg-gray-400 transition-colors"
          >
            Dismiss
          </button>
        </div>
      );
    }

    if (updateInstalled || updateCancelled) {
      return (
        <div className="flex gap-2 mt-3">
          <button
            onClick={clearError}
            className="px-4 py-2 bg-gray-300 text-gray-700 text-sm rounded-md hover:bg-gray-400 transition-colors"
          >
            OK
          </button>
        </div>
      );
    }

    return null;
  };

  return (
    <div
      className={`fixed top-4 right-4 max-w-sm p-4 border rounded-lg shadow-lg z-50 ${getNotificationStyle()} ${className}`}
    >
      <div className="flex items-start gap-3">
        <span className="text-xl flex-shrink-0">{getIcon()}</span>
        <div className="flex-1">
          {renderUpdateMessage()}
          {renderActions()}
        </div>
        <button
          onClick={flexibleUpdateAvailable ? dismissNotification : clearError}
          className="text-gray-400 hover:text-gray-600 flex-shrink-0"
        >
          ✕
        </button>
      </div>
    </div>
  );
};

/**
 * Simple banner component for update notifications
 */
export const InAppUpdateBanner: React.FC<{ className?: string }> = ({ className = "" }) => {
  const { updateDownloaded, completeFlexibleUpdate, clearError } = useInAppUpdate();

  if (!updateDownloaded) {
    return null;
  }

  return (
    <div className={`bg-blue-600 text-white p-3 ${className}`}>
      <div className="flex items-center justify-between max-w-7xl mx-auto">
        <div className="flex items-center gap-2">
          <span>📥</span>
          <span className="text-sm font-medium">Update ready to install</span>
        </div>
        <div className="flex gap-2">
          <button
            onClick={completeFlexibleUpdate}
            className="px-3 py-1 bg-white text-blue-600 text-sm rounded hover:bg-gray-100 transition-colors"
          >
            Install
          </button>
          <button
            onClick={clearError}
            className="px-3 py-1 text-white text-sm hover:bg-blue-700 rounded transition-colors"
          >
            Later
          </button>
        </div>
      </div>
    </div>
  );
};
