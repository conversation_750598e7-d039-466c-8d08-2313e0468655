import {
  Alert,
  AlertDescription,
  AlertIcon,
  AlertTitle,
  Badge,
  Box,
  Button,
  CloseButton,
  Flex,
  Heading,
  HStack,
  Progress,
  Text,
  useToast,
  VStack
} from "@chakra-ui/react";
import React, { useEffect } from "react";
import { useTranslation } from "react-i18next";
import { useInAppUpdate } from "../hooks/useInAppUpdate";

/**
 * Props for InAppUpdateNotification component
 */
interface InAppUpdateNotificationProps {
  /**
   * Whether to show the notification
   */
  isVisible?: boolean;

  /**
   * Callback when notification is dismissed
   */
  onDismiss?: () => void;

  /**
   * Position of the notification
   */
  position?: "top" | "bottom";

  /**
   * Whether to auto-check for updates on mount
   */
  autoCheck?: boolean;
}

/**
 * Full-featured In-App Update Notification Component
 *
 * Displays update notifications with appropriate actions based on update type.
 * Handles both immediate and flexible update flows with progress indication.
 */
export function InAppUpdateNotification({
  isVisible = true,
  onDismiss,
  position = "top",
  autoCheck = true
}: InAppUpdateNotificationProps) {
  const {
    updateAvailable,
    updateType,
    priority,
    stalenessDays,
    isChecking,
    isDownloading,
    isDownloaded,
    isInstalling,
    isInstalled,
    error,
    isCancelled: _isCancelled,
    isServiceAvailable,
    platform: _platform,
    checkForUpdate,
    startImmediateUpdate,
    startFlexibleUpdate,
    completeFlexibleUpdate,
    clearError
  } = useInAppUpdate();

  const toast = useToast();

  // Auto-check for updates on mount with debouncing
  const autoCheckTimeoutRef = React.useRef<NodeJS.Timeout | null>(null);
  const hasAutoCheckedRef = React.useRef(false);

  useEffect(() => {
    if (autoCheck && isServiceAvailable && !hasAutoCheckedRef.current) {
      // Clear any existing timeout
      if (autoCheckTimeoutRef.current) {
        clearTimeout(autoCheckTimeoutRef.current);
      }

      // Debounce auto-check to prevent rapid re-renders from triggering multiple checks
      autoCheckTimeoutRef.current = setTimeout(() => {
        checkForUpdate();
        hasAutoCheckedRef.current = true;
      }, 100); // 100ms debounce
    }

    // Reset auto-check flag if autoCheck is disabled
    if (!autoCheck) {
      hasAutoCheckedRef.current = false;
    }

    return () => {
      if (autoCheckTimeoutRef.current) {
        clearTimeout(autoCheckTimeoutRef.current);
      }
    };
  }, [autoCheck, isServiceAvailable, checkForUpdate]);

  // Show toast notifications for important events
  useEffect(() => {
    if (isInstalled) {
      toast({
        title: "Update Installed",
        description: "The app has been updated successfully!",
        status: "success",
        duration: 5000,
        isClosable: true
      });
    }
  }, [isInstalled, toast]);

  useEffect(() => {
    if (error) {
      toast({
        title: "Update Error",
        description: error,
        status: "error",
        duration: 8000,
        isClosable: true
      });
    }
  }, [error, toast]);

  // Don't render if not visible or service not available
  if (!isVisible || !isServiceAvailable) {
    return null;
  }

  // Don't render if no update available and not checking
  if (!updateAvailable && !isChecking && !error) {
    return null;
  }

  /**
   * Handle immediate update
   */
  const handleImmediateUpdate = async () => {
    try {
      await startImmediateUpdate();
    } catch (err) {
      console.error("Failed to start immediate update:", err);
    }
  };

  /**
   * Handle flexible update
   */
  const handleFlexibleUpdate = async () => {
    try {
      if (isDownloaded) {
        await completeFlexibleUpdate();
      } else {
        await startFlexibleUpdate();
      }
    } catch (err) {
      console.error("Failed to handle flexible update:", err);
    }
  };

  /**
   * Handle dismiss
   */
  const handleDismiss = () => {
    clearError();
    onDismiss?.();
  };

  /**
   * Get alert status based on update type and priority
   */
  const getAlertStatus = () => {
    if (error) return "error";
    if (updateType === "immediate" || priority >= 4) return "warning";
    return "info";
  };

  /**
   * Get update urgency text
   */
  const getUrgencyText = () => {
    if (priority >= 4) return "Critical";
    if (priority >= 2) return "Important";
    return "Available";
  };

  return (
    <Box
      position="fixed"
      top={position === "top" ? 4 : undefined}
      bottom={position === "bottom" ? 4 : undefined}
      left={4}
      right={4}
      zIndex={1000}
      maxW="md"
      mx="auto"
    >
      <Alert
        status={getAlertStatus()}
        variant="subtle"
        flexDirection="column"
        alignItems="flex-start"
        borderRadius="md"
        boxShadow="lg"
        p={4}
      >
        <Flex w="100%" align="flex-start">
          <AlertIcon />
          <Box flex="1" ml={2}>
            <AlertTitle fontSize="lg" mb={1}>
              {isChecking && "Checking for Updates..."}
              {error && "Update Error"}
              {updateAvailable && !error && `${getUrgencyText()} Update`}
            </AlertTitle>

            {isChecking && (
              <AlertDescription>
                <Text>Checking for app updates...</Text>
                <Progress size="sm" isIndeterminate mt={2} />
              </AlertDescription>
            )}

            {error && (
              <AlertDescription>
                <Text>{error}</Text>
                <Button size="sm" mt={2} onClick={clearError}>
                  Retry
                </Button>
              </AlertDescription>
            )}

            {updateAvailable && !error && (
              <AlertDescription>
                <VStack align="stretch" spacing={3}>
                  <Box>
                    <Text>
                      A new version of the app is available.
                      {stalenessDays > 0 && ` (${stalenessDays} days old)`}
                    </Text>

                    <HStack mt={1} spacing={2}>
                      <Badge colorScheme={updateType === "immediate" ? "red" : "blue"}>
                        {updateType?.toUpperCase()}
                      </Badge>
                      <Badge variant="outline">Priority: {priority}</Badge>
                    </HStack>
                  </Box>

                  {isDownloading && (
                    <Box>
                      <Text fontSize="sm" color="gray.600">
                        Downloading update...
                      </Text>
                      <Progress size="sm" isIndeterminate />
                    </Box>
                  )}

                  {isDownloaded && (
                    <Box>
                      <Text fontSize="sm" color="green.600">
                        ✓ Update downloaded and ready to install
                      </Text>
                    </Box>
                  )}

                  {isInstalling && (
                    <Box>
                      <Text fontSize="sm" color="blue.600">
                        Installing update...
                      </Text>
                      <Progress size="sm" isIndeterminate />
                    </Box>
                  )}

                  <HStack spacing={2}>
                    {updateType === "immediate" && (
                      <Button
                        colorScheme="red"
                        size="sm"
                        onClick={handleImmediateUpdate}
                        isLoading={isInstalling}
                        loadingText="Installing..."
                      >
                        Update Now
                      </Button>
                    )}

                    {updateType === "flexible" && (
                      <Button
                        colorScheme="blue"
                        size="sm"
                        onClick={handleFlexibleUpdate}
                        isLoading={isDownloading || isInstalling}
                        loadingText={isDownloading ? "Downloading..." : "Installing..."}
                      >
                        {isDownloaded ? "Install Update" : "Download Update"}
                      </Button>
                    )}

                    {updateType === "flexible" && !isDownloaded && (
                      <Button variant="ghost" size="sm" onClick={handleDismiss}>
                        Later
                      </Button>
                    )}
                  </HStack>
                </VStack>
              </AlertDescription>
            )}
          </Box>

          <CloseButton size="sm" onClick={handleDismiss} ml={2} />
        </Flex>
      </Alert>
    </Box>
  );
}

/**
 * Props for InAppUpdateBanner component
 */
interface InAppUpdateBannerProps {
  /**
   * Whether to show the banner
   */
  isVisible?: boolean;

  /**
   * Callback when banner is dismissed
   */
  onDismiss?: () => void;
}

/**
 * Simple In-App Update Banner Component
 *
 * A minimal banner notification for updates that doesn't interfere with the UI.
 */
export function InAppUpdateBanner({
  isVisible = true,
  onDismiss: _onDismiss
}: InAppUpdateBannerProps) {
  const {
    updateAvailable,
    updateType,
    isServiceAvailable,
    startImmediateUpdate,
    startFlexibleUpdate,
    isChecking,
    error
  } = useInAppUpdate();
  const { t } = useTranslation();

  // Centralized rendering logic
  const shouldRender = React.useMemo(() => {
    return isVisible && isServiceAvailable && (updateAvailable || isChecking || error);
  }, [isVisible, isServiceAvailable, updateAvailable, isChecking, error]);

  // Don't render if conditions aren't met
  if (!shouldRender) {
    return null;
  }

  // Toast for error handling with debouncing
  const toast = useToast();
  const lastErrorRef = React.useRef<string | null>(null);
  const errorToastShownRef = React.useRef(false);

  React.useEffect(() => {
    if (error && error !== lastErrorRef.current && !errorToastShownRef.current) {
      toast({
        title: "Update Check Failed",
        description: "Unable to check for updates. Please try again later.",
        status: "error",
        duration: 5000,
        isClosable: true
      });

      lastErrorRef.current = error;
      errorToastShownRef.current = true;

      // Reset toast flag after a delay to allow future error toasts
      setTimeout(() => {
        errorToastShownRef.current = false;
      }, 10000); // 10 seconds debounce
    }

    // Clear error reference when error is resolved
    if (!error) {
      lastErrorRef.current = null;
      errorToastShownRef.current = false;
    }
  }, [error, toast]);

  const handleUpdate = async () => {
    // Clear any previous errors when user manually retries
    if (error) {
      lastErrorRef.current = null;
      errorToastShownRef.current = false;
    }

    if (updateType === "immediate") {
      await startImmediateUpdate();
    } else {
      await startFlexibleUpdate();
    }
  };

  return (
    <Box
      position="fixed"
      top={0}
      left={0}
      right={0}
      bottom={0}
      bg="rgba(0, 0, 0, 0.5)"
      zIndex={1000}
      display="flex"
      alignItems="center"
      justifyContent="center"
      p={4}
    >
      <Box
        bg="white"
        borderRadius="20px"
        p={8}
        maxW="sm"
        w="full"
        textAlign="center"
        boxShadow="xl"
      >
        <Box mb={6} justifyItems={"center"}>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            xmlnsXlink="http://www.w3.org/1999/xlink"
            version="1.1"
            id="Layer_1"
            x="0px"
            y="0px"
            width="100%"
            viewBox="0 0 406 168"
            enable-background="new 0 0 406 168"
            xmlSpace="preserve"
          >
            <path
              fill="#FEFFFF"
              opacity="1.000000"
              stroke="none"
              d=" M252.000000,169.000000   C168.000015,169.000000 84.500038,169.000000 1.000045,169.000000   C1.000030,113.000031 1.000030,57.000065 1.000015,1.000075   C136.333267,1.000050 271.666534,1.000050 406.999878,1.000025   C406.999908,56.999920 406.999908,112.999840 406.999939,168.999878   C355.500000,169.000000 304.000000,169.000000 252.000000,169.000000  M112.843246,102.234703   C112.578667,102.537048 112.314079,102.839394 111.729111,103.718765   C109.855202,108.749619 108.060265,113.812218 106.059044,118.791916   C105.462395,120.276550 104.578148,122.280952 103.329712,122.763161   C96.277275,125.487183 88.988045,126.880966 81.363365,125.394478   C71.645126,123.499832 65.357277,117.159859 59.979931,109.442642   C59.239475,108.379990 57.527161,107.994514 56.264782,107.295525   C56.431488,108.775604 56.138241,110.530937 56.834370,111.694191   C66.245117,127.419830 82.677200,133.591553 100.235565,128.134811   C102.272072,127.501915 104.282539,126.785255 106.204735,126.141068   C111.168869,133.549301 114.789040,135.048828 123.290932,133.180359   C126.521103,132.470428 129.678589,131.429779 133.110306,131.269913   C134.600861,133.598114 136.091400,135.926315 137.160553,138.815079   C136.017075,140.498215 134.873596,142.181335 133.730118,143.864456   C135.677704,143.269577 137.626450,142.678467 139.571671,142.075974   C139.880768,141.980240 140.174011,141.833298 141.228149,141.905640   C143.774750,143.439880 146.463852,144.781570 148.842941,146.542160   C158.437973,153.642746 169.462173,157.185776 181.070480,157.828125   C199.987854,158.874908 216.410934,152.262146 230.259857,139.262070   C238.617798,131.416382 243.375610,121.339462 248.971313,111.088737   C270.299927,111.051262 291.628571,111.013786 313.550720,110.942497   C313.706604,110.296738 313.862457,109.650970 314.032227,108.220100   C315.188721,104.440735 315.197540,100.886368 311.952393,97.382477   C311.305725,97.239655 310.659027,97.096832 309.163361,96.980179   C306.938354,97.156616 304.713379,97.333046 302.474091,97.510620   C301.889526,94.844681 301.334259,92.312370 301.037079,89.133759   C302.139648,87.917160 303.103149,86.525032 304.372711,85.519142   C307.350464,83.159805 307.578308,81.145844 304.561401,78.463989   C300.963470,75.265648 297.672882,71.705704 294.400055,68.163025   C292.191925,65.772835 290.431122,65.816849 288.221008,68.216141   C284.728333,72.007805 281.066132,75.652229 277.337158,79.213959   C275.111481,81.339821 275.264496,83.105698 277.447723,85.056183   C278.431885,85.935402 279.247833,87.002937 280.004517,88.689545   C278.346985,91.135704 276.608856,93.532036 275.056305,96.043106   C273.715118,98.212341 272.443207,98.431427 270.216064,96.998207   C266.047211,94.315468 261.574799,95.127625 257.732269,98.796265   C256.558441,99.916977 255.383072,101.036110 253.341507,102.066956   C252.227921,101.018730 250.318924,100.082207 250.153030,98.900879   C249.693756,95.630531 249.961365,92.258095 250.276154,88.349182   C256.922241,82.694427 263.919983,77.313766 266.597046,67.710854   C270.288116,61.636944 272.316681,55.070316 268.858551,48.480457   C265.451324,41.987598 258.791840,41.057034 252.120392,40.959240   C251.133621,40.944778 250.144943,41.060280 248.262451,41.014606   C241.518158,42.381157 234.773880,43.747707 227.928665,44.419102   C218.037308,36.047085 207.102585,30.420172 193.915756,28.651529   C173.892227,25.965937 156.789749,31.534121 141.913559,44.682579   C138.108704,48.045528 134.845871,52.021736 131.333038,55.717228   C130.223572,54.749615 129.194351,53.851982 128.065979,52.134071   C127.667442,44.749504 125.379234,41.922180 119.457703,42.007046   C117.027969,42.041870 114.607605,42.730633 111.512077,43.102444   C110.999901,43.708229 110.487732,44.314014 109.404274,45.274742   C104.553574,49.453552 99.702873,53.632362 94.874809,57.791679   C87.666275,50.289867 80.437286,49.587212 72.242630,57.576672   C68.074226,61.640701 65.290512,67.124992 61.322464,72.051537   C61.205517,72.698746 61.088573,73.345963 60.949261,74.649010   C60.885296,75.081474 60.821331,75.513947 60.144501,76.102104   C60.397118,77.081062 60.649731,78.060028 61.430058,79.103729   C61.605339,79.129875 61.780624,79.156029 62.096565,79.800728   C63.079418,79.598190 64.062271,79.395653 65.972549,79.107826   C83.966270,79.129166 101.959999,79.150513 120.091476,79.941597   C119.973122,87.831421 119.854774,95.721245 119.060173,103.323624   C117.194466,102.937737 115.328758,102.551849 112.843246,102.234703  z"
            />
            <path
              fill="#EBF1FB"
              opacity="1.000000"
              stroke="none"
              d=" M119.736420,103.611069   C119.854774,95.721245 119.973122,87.831421 120.493134,79.497215   C130.770309,79.052834 140.645828,79.052834 150.490799,79.052834   C150.635147,77.780357 150.895157,77.196556 150.713394,76.834709   C147.927383,71.289032 138.701218,73.237274 137.505066,65.522804   C137.396835,64.824730 134.539062,64.148407 132.975586,64.186089   C131.148270,64.230103 129.340134,65.069008 126.961327,65.716011   C127.398186,61.084846 127.781662,57.019600 128.165131,52.954350   C129.194351,53.851982 130.223572,54.749615 131.333038,55.717228   C134.845871,52.021736 138.108704,48.045528 141.913559,44.682579   C156.789749,31.534121 173.892227,25.965937 193.915756,28.651529   C207.102585,30.420172 218.037308,36.047085 227.920959,45.098022   C225.267776,47.686668 222.785995,49.896187 219.944824,51.447529   C212.646271,55.432724 206.833221,62.036495 197.944489,63.628300   C186.030319,58.968872 174.451920,57.800453 161.906143,63.334072   C166.213638,69.706520 170.090454,75.441841 174.003052,81.516609   C174.991684,83.898376 175.944550,85.940697 176.586349,88.149879   C173.117691,89.680061 169.960083,91.043381 166.122879,92.700134   C172.936508,103.303658 179.421494,113.395721 186.182266,123.916985   C189.381592,120.879967 192.007141,118.387634 194.966629,116.037689   C196.824966,117.792168 198.349365,119.404251 199.945007,121.369690   C203.559814,127.569359 207.103363,133.415649 210.934814,139.736954   C219.778687,130.985962 223.655884,121.084679 225.340424,109.739304   C225.227753,104.613586 227.470428,101.476624 231.878494,99.648468   C232.924469,99.214668 233.794586,98.373894 234.786728,97.790230   C239.830933,94.822845 244.888672,91.878456 249.941666,88.926010   C249.961365,92.258095 249.693756,95.630531 250.153030,98.900879   C250.318924,100.082207 252.227921,101.018730 253.693253,102.433456   C252.855469,103.051422 251.479752,103.006760 250.513977,103.614052   C248.289764,105.012680 246.264816,106.728180 244.159012,108.315109   C245.454697,109.271347 246.750381,110.227592 248.046066,111.183830   C243.375610,121.339462 238.617798,131.416382 230.259857,139.262070   C216.410934,152.262146 199.987854,158.874908 181.070480,157.828125   C169.462173,157.185776 158.437973,153.642746 148.842941,146.542160   C146.463852,144.781570 143.774750,143.439880 141.061432,141.525116   C149.787994,134.765869 158.682968,128.389511 167.572739,122.005920   C168.918350,121.039658 170.382629,120.175720 171.507874,118.993919   C172.016708,118.459534 172.223633,116.982353 171.843506,116.446686   C171.484863,115.941284 170.070740,115.799660 169.310699,116.057495   C168.254288,116.415863 167.368683,117.287247 166.420700,117.954285   C156.806534,124.719254 147.194534,131.487320 137.581940,138.254517   C136.091400,135.926315 134.600861,133.598114 133.217117,130.662659   C144.456268,123.572159 156.479446,118.400528 165.169449,108.197975   C152.363586,113.792427 141.977768,123.989319 128.098755,127.046448   C125.854492,121.687477 123.649345,116.755928 121.392410,111.453964   C120.805901,108.592720 120.271156,106.101898 119.736420,103.611069  z"
            />
            <path
              fill="#F1CDCE"
              opacity="1.000000"
              stroke="none"
              d=" M199.873749,121.016327   C198.349365,119.404251 196.824966,117.792168 194.993256,115.693130   C188.756424,106.131790 182.826920,97.057404 176.897415,87.983017   C175.944550,85.940697 174.991684,83.898376 174.331268,81.374954   C181.940811,76.186668 189.287170,71.523529 196.535980,66.713402   C197.388138,66.147934 197.703857,64.774063 198.268250,63.774948   C206.833221,62.036495 212.646271,55.432724 219.944824,51.447529   C222.785995,49.896187 225.267776,47.686668 227.971436,45.445602   C234.773880,43.747707 241.518158,42.381157 248.749023,41.404675   C254.836319,50.572884 260.437042,59.351025 266.037750,68.129166   C263.919983,77.313766 256.922241,82.694427 250.108917,88.637589   C244.888672,91.878456 239.830933,94.822845 234.786728,97.790230   C233.794586,98.373894 232.924469,99.214668 231.878494,99.648468   C227.470428,101.476624 225.227753,104.613586 225.252441,109.362564   C224.773514,108.010712 224.654739,106.761559 224.463837,104.753983   C215.897034,110.419540 207.885391,115.717941 199.873749,121.016327  M212.772446,66.438141   C208.791290,72.822739 209.043594,80.794022 213.387222,85.860466   C217.851715,91.067924 225.965256,93.553917 231.243790,91.331772   C238.182404,88.410759 242.254379,82.036163 242.002609,74.489044   C241.795044,68.266945 237.335938,62.051537 231.630890,60.032242   C224.684525,57.573578 218.494980,59.491402 212.772446,66.438141  z"
            />
            <path
              fill="#A6CBF8"
              opacity="1.000000"
              stroke="none"
              d=" M128.115555,52.544212   C127.781662,57.019600 127.398186,61.084846 126.961327,65.716011   C129.340134,65.069008 131.148270,64.230103 132.975586,64.186089   C134.539062,64.148407 137.396835,64.824730 137.505066,65.522804   C138.701218,73.237274 147.927383,71.289032 150.713394,76.834709   C150.895157,77.196556 150.635147,77.780357 150.490799,79.052834   C140.645828,79.052834 130.770309,79.052834 120.424255,79.112350   C101.959999,79.150513 83.966270,79.129166 65.125313,79.081696   C63.504021,79.097771 62.729965,79.139969 61.955910,79.182175   C61.780624,79.156029 61.605339,79.129875 61.186131,78.683296   C60.880592,77.490715 60.818981,76.718559 60.757370,75.946411   C60.821331,75.513947 60.885296,75.081474 61.209915,74.154907   C61.607330,73.096291 61.744091,72.531769 61.880852,71.967255   C65.290512,67.124992 68.074226,61.640701 72.242630,57.576672   C80.437286,49.587212 87.666275,50.289867 94.874809,57.791679   C99.702873,53.632362 104.553574,49.453552 110.007195,45.080620   C111.134399,44.298302 111.658691,43.710114 112.182983,43.121925   C114.607605,42.730633 117.027969,42.041870 119.457703,42.007046   C125.379234,41.922180 127.667442,44.749504 128.115555,52.544212  z"
            />
            <path
              fill="#A6CAF4"
              opacity="1.000000"
              stroke="none"
              d=" M248.508698,111.136284   C246.750381,110.227592 245.454697,109.271347 244.159012,108.315109   C246.264816,106.728180 248.289764,105.012680 250.513977,103.614052   C251.479752,103.006760 252.855469,103.051422 254.126709,102.477966   C255.383072,101.036110 256.558441,99.916977 257.732269,98.796265   C261.574799,95.127625 266.047211,94.315468 270.216064,96.998207   C272.443207,98.431427 273.715118,98.212341 275.056305,96.043106   C276.608856,93.532036 278.346985,91.135704 280.366821,88.471664   C282.531830,90.172050 284.233948,92.198456 286.157196,93.987183   C290.855835,98.357208 291.427002,98.260620 296.065491,93.700661   C297.517853,92.272911 299.201355,91.080307 300.779022,89.780067   C301.334259,92.312370 301.889526,94.844681 302.474091,97.510620   C304.713379,97.333046 306.938354,97.156616 309.750793,97.218964   C310.898865,97.624054 311.459473,97.790359 312.020111,97.956665   C315.197540,100.886368 315.188721,104.440735 313.775269,108.772476   C313.331238,109.875343 313.144196,110.425827 312.957184,110.976311   C291.628571,111.013786 270.299927,111.051262 248.508698,111.136284  z"
            />
            <path
              fill="#27264A"
              opacity="1.000000"
              stroke="none"
              d=" M128.137878,127.473862   C141.977768,123.989319 152.363586,113.792427 165.169449,108.197975   C156.479446,118.400528 144.456268,123.572159 133.096573,130.297638   C129.678589,131.429779 126.521103,132.470428 123.290932,133.180359   C114.789040,135.048828 111.168869,133.549301 106.204735,126.141068   C104.282539,126.785255 102.272072,127.501915 100.235565,128.134811   C82.677200,133.591553 66.245117,127.419830 56.834370,111.694191   C56.138241,110.530937 56.431488,108.775604 56.264778,107.295532   C57.527161,107.994514 59.239475,108.379990 59.979931,109.442642   C65.357277,117.159859 71.645126,123.499832 81.363365,125.394478   C88.988045,126.880966 96.277275,125.487183 103.329712,122.763161   C104.578148,122.280952 105.462395,120.276550 106.059044,118.791916   C108.060265,113.812218 109.855202,108.749619 112.192841,103.384949   C112.925400,102.756081 113.194229,102.461021 113.463051,102.165970   C115.328758,102.551849 117.194466,102.937737 119.398300,103.467346   C120.271156,106.101898 120.805901,108.592720 121.233803,111.744995   C118.758781,115.526169 116.404152,118.656334 114.016068,121.760757   C112.995338,123.087654 111.904854,124.360886 110.846069,125.658508   C112.594017,127.004677 114.348946,129.480225 116.088509,129.469467   C120.108925,129.444626 124.121887,128.214920 128.137878,127.473862  M114.122314,116.632523   C116.843193,113.481522 119.459679,110.314110 115.800781,105.517555   C111.157089,109.153435 110.598274,113.787422 109.968140,119.651848   C111.802704,118.391304 112.703583,117.772308 114.122314,116.632523  z"
            />
            <path
              fill="#232249"
              opacity="1.000000"
              stroke="none"
              d=" M300.908051,89.456917   C299.201355,91.080307 297.517853,92.272911 296.065491,93.700661   C291.427002,98.260620 290.855835,98.357208 286.157196,93.987183   C284.233948,92.198456 282.531830,90.172050 280.434601,88.119400   C279.247833,87.002937 278.431885,85.935402 277.447723,85.056183   C275.264496,83.105698 275.111481,81.339821 277.337158,79.213959   C281.066132,75.652229 284.728333,72.007805 288.221008,68.216141   C290.431122,65.816849 292.191925,65.772835 294.400055,68.163025   C297.672882,71.705704 300.963470,75.265648 304.561401,78.463989   C307.578308,81.145844 307.350464,83.159805 304.372711,85.519142   C303.103149,86.525032 302.139648,87.917160 300.908051,89.456917  M299.107483,85.329613   C300.413910,84.623779 301.720306,83.917953 303.048492,83.200371   C298.350922,78.374557 294.864746,74.793182 292.131256,71.985069   C288.127167,75.635384 284.423126,79.012169 280.646423,82.455185   C281.703400,83.643250 282.684967,84.746544 283.431641,86.468079   C290.842041,94.341660 290.844299,94.343971 298.508240,86.834099   C298.709686,86.636719 298.725891,86.250290 299.107483,85.329613  z"
            />
            <path
              fill="#CF585F"
              opacity="1.000000"
              stroke="none"
              d=" M266.317383,67.920013   C260.437042,59.351025 254.836319,50.572884 249.196381,41.455200   C250.144943,41.060280 251.133621,40.944778 252.120392,40.959240   C258.791840,41.057034 265.451324,41.987598 268.858551,48.480457   C272.316681,55.070316 270.288116,61.636944 266.317383,67.920013  M258.947601,48.614868   C260.576599,53.012325 263.252197,56.505646 267.306824,59.071999   C269.284363,55.317638 268.919922,51.208469 265.512634,47.571587   C263.679108,45.614521 260.612579,44.812656 258.102417,43.489563   C257.818024,43.965256 257.533661,44.440948 257.249298,44.916641   C257.769562,45.907520 258.289825,46.898403 258.947601,48.614868  z"
            />
            <path
              fill="#2B2C50"
              opacity="1.000000"
              stroke="none"
              d=" M137.371246,138.534790   C147.194534,131.487320 156.806534,124.719254 166.420700,117.954285   C167.368683,117.287247 168.254288,116.415863 169.310699,116.057495   C170.070740,115.799660 171.484863,115.941284 171.843506,116.446686   C172.223633,116.982353 172.016708,118.459534 171.507874,118.993919   C170.382629,120.175720 168.918350,121.039658 167.572739,122.005920   C158.682968,128.389511 149.787994,134.765869 140.684631,141.427307   C140.174011,141.833298 139.880768,141.980240 139.571671,142.075974   C137.626450,142.678467 135.677704,143.269577 133.730118,143.864456   C134.873596,142.181335 136.017075,140.498215 137.371246,138.534790  z"
            />
            <path
              fill="#EBF1FB"
              opacity="1.000000"
              stroke="none"
              d=" M60.450935,76.024261   C60.818981,76.718559 60.880592,77.490715 60.922272,78.650925   C60.649731,78.060028 60.397118,77.081062 60.450935,76.024261  z"
            />
            <path
              fill="#EBF1FB"
              opacity="1.000000"
              stroke="none"
              d=" M62.026237,79.491447   C62.729965,79.139969 63.504021,79.097771 64.661598,79.124344   C64.062271,79.395653 63.079418,79.598190 62.026237,79.491447  z"
            />
            <path
              fill="#EBF1FB"
              opacity="1.000000"
              stroke="none"
              d=" M111.847534,43.112183   C111.658691,43.710114 111.134399,44.298302 110.292839,44.903145   C110.487732,44.314014 110.999901,43.708229 111.847534,43.112183  z"
            />
            <path
              fill="#EBF1FB"
              opacity="1.000000"
              stroke="none"
              d=" M311.986267,97.669571   C311.459473,97.790359 310.898865,97.624054 310.175293,97.205879   C310.659027,97.096832 311.305725,97.239655 311.986267,97.669571  z"
            />
            <path
              fill="#EBF1FB"
              opacity="1.000000"
              stroke="none"
              d=" M61.601662,72.009399   C61.744091,72.531769 61.607330,73.096291 61.221100,73.826988   C61.088573,73.345963 61.205517,72.698746 61.601662,72.009399  z"
            />
            <path
              fill="#EBF1FB"
              opacity="1.000000"
              stroke="none"
              d=" M313.253967,110.959404   C313.144196,110.425827 313.331238,109.875343 313.768311,109.165031   C313.862457,109.650970 313.706604,110.296738 313.253967,110.959404  z"
            />
            <path
              fill="#EBF1FB"
              opacity="1.000000"
              stroke="none"
              d=" M113.153152,102.200333   C113.194229,102.461021 112.925400,102.756081 112.353043,103.096436   C112.314079,102.839394 112.578667,102.537048 113.153152,102.200333  z"
            />
            <path
              fill="#D94C56"
              opacity="1.000000"
              stroke="none"
              d=" M197.944489,63.628304   C197.703857,64.774063 197.388138,66.147934 196.535980,66.713402   C189.287170,71.523529 181.940811,76.186668 174.295502,81.035507   C170.090454,75.441841 166.213638,69.706520 161.906143,63.334072   C174.451920,57.800453 186.030319,58.968872 197.944489,63.628304  z"
            />
            <path
              fill="#D94C57"
              opacity="1.000000"
              stroke="none"
              d=" M199.945007,121.369698   C207.885391,115.717941 215.897034,110.419540 224.463837,104.753983   C224.654739,106.761559 224.773514,108.010712 224.980255,109.636612   C223.655884,121.084679 219.778687,130.985962 210.934814,139.736954   C207.103363,133.415649 203.559814,127.569359 199.945007,121.369698  z"
            />
            <path
              fill="#2844A7"
              opacity="1.000000"
              stroke="none"
              d=" M176.586349,88.149879   C182.826920,97.057404 188.756424,106.131790 194.659302,115.550735   C192.007141,118.387634 189.381592,120.879967 186.182266,123.916985   C179.421494,113.395721 172.936508,103.303658 166.122879,92.700134   C169.960083,91.043381 173.117691,89.680061 176.586349,88.149879  z"
            />
            <path
              fill="#F4F4F7"
              opacity="1.000000"
              stroke="none"
              d=" M128.098755,127.046448   C124.121887,128.214920 120.108925,129.444626 116.088509,129.469467   C114.348946,129.480225 112.594017,127.004677 110.846069,125.658508   C111.904854,124.360886 112.995338,123.087654 114.016068,121.760757   C116.404152,118.656334 118.758781,115.526169 121.285583,112.115402   C123.649345,116.755928 125.854492,121.687477 128.098755,127.046448  z"
            />
            <path
              fill="#EBB6B6"
              opacity="1.000000"
              stroke="none"
              d=" M212.943817,66.103729   C218.494980,59.491402 224.684525,57.573578 231.630890,60.032242   C237.335938,62.051537 241.795044,68.266945 242.002609,74.489044   C242.254379,82.036163 238.182404,88.410759 231.243790,91.331772   C225.965256,93.553917 217.851715,91.067924 213.387222,85.860466   C209.043594,80.794022 208.791290,72.822739 212.943817,66.103729  M235.911758,69.978394   C235.305801,69.188927 234.790405,68.304596 234.079041,67.625565   C229.830078,63.569664 225.572220,63.042706 219.982574,66.037300   C215.631332,68.368423 214.297348,72.283409 214.806854,76.972488   C215.357422,82.039528 218.739761,85.704292 223.869095,86.867798   C232.443237,88.812714 238.771271,80.546669 235.911758,69.978394  z"
            />
            <path
              fill="#D9D8E4"
              opacity="1.000000"
              stroke="none"
              d=" M113.863388,116.892914   C112.703583,117.772308 111.802704,118.391304 109.968140,119.651848   C110.598274,113.787422 111.157089,109.153435 115.800781,105.517555   C119.459679,110.314110 116.843193,113.481522 113.863388,116.892914  z"
            />
            <path
              fill="#F0F1F5"
              opacity="1.000000"
              stroke="none"
              d=" M283.666534,85.849838   C282.684967,84.746544 281.703400,83.643250 280.646423,82.455185   C284.423126,79.012169 288.127167,75.635384 292.131256,71.985069   C294.864746,74.793182 298.350922,78.374557 303.048492,83.200371   C301.720306,83.917953 300.413910,84.623779 298.557556,85.669502   C293.227234,85.956207 288.446899,85.903023 283.666534,85.849838  z"
            />
            <path
              fill="#9FB9E2"
              opacity="1.000000"
              stroke="none"
              d=" M283.549072,86.158958   C288.446899,85.903023 293.227234,85.956207 298.418030,85.980705   C298.725891,86.250290 298.709686,86.636719 298.508240,86.834099   C290.844299,94.343971 290.842041,94.341660 283.549072,86.158958  z"
            />
            <path
              fill="#DA444D"
              opacity="1.000000"
              stroke="none"
              d=" M258.878845,48.252075   C258.289825,46.898403 257.769562,45.907520 257.249298,44.916641   C257.533661,44.440948 257.818024,43.965256 258.102417,43.489563   C260.612579,44.812656 263.679108,45.614521 265.512634,47.571587   C268.919922,51.208469 269.284363,55.317638 267.306824,59.071999   C263.252197,56.505646 260.576599,53.012325 258.878845,48.252075  z"
            />
            <path
              fill="#BA4A6B"
              opacity="1.000000"
              stroke="none"
              d=" M236.076981,70.341873   C238.771271,80.546669 232.443237,88.812714 223.869095,86.867798   C218.739761,85.704292 215.357422,82.039528 214.806854,76.972488   C214.297348,72.283409 215.631332,68.368423 219.982574,66.037300   C225.572220,63.042706 229.830078,63.569664 234.079041,67.625565   C234.790405,68.304596 235.305801,69.188927 236.076981,70.341873  M217.921280,73.885696   C217.439667,78.081848 218.637192,81.902237 222.766342,83.102989   C225.218399,83.816048 229.085602,83.336617 230.848587,81.755997   C232.775589,80.028366 234.301987,76.041641 233.636169,73.717621   C232.893692,71.125992 229.821518,68.187675 227.165497,67.389290   C222.875275,66.099678 219.731644,68.958382 217.921280,73.885696  z"
            />
            <path
              fill="#201F7C"
              opacity="1.000000"
              stroke="none"
              d=" M217.993057,73.487160   C219.731644,68.958382 222.875275,66.099678 227.165497,67.389290   C229.821518,68.187675 232.893692,71.125992 233.636169,73.717621   C234.301987,76.041641 232.775589,80.028366 230.848587,81.755997   C229.085602,83.336617 225.218399,83.816048 222.766342,83.102989   C218.637192,81.902237 217.439667,78.081848 217.993057,73.487160  z"
            />
          </svg>
        </Box>
        {/* Title */}
        <Heading size="lg" mb={4} color="gray.800">
          {t("update_banner_title")}
        </Heading>
        {/* Description */}
        <Text color="gray.600" lineHeight="1.6">
          {t("update_app_reminder")}
        </Text>
        <Text fontWeight="semibold" mb={6} color="gray.800">
          {t("download_update")}
        </Text>
        {/* Update Button */}
        <VStack spacing={3} w="full">
          <Button
            colorScheme="blue"
            size="lg"
            w="full"
            borderRadius="12px"
            py={6}
            fontSize="lg"
            fontWeight="semibold"
            onClick={handleUpdate}
          >
            {t("update")}
          </Button>

          {/* Maybe Later Button - only show for flexible updates */}
          {updateType === "flexible" && (
            <Button
              colorScheme="gray"
              size="lg"
              w="full"
              borderRadius="12px"
              py={6}
              fontSize="lg"
              fontWeight=""
              onClick={_onDismiss}
            >
              Maybe Later
            </Button>
          )}
        </VStack>
      </Box>
    </Box>
  );
}
