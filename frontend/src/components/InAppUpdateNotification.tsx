import {
  Alert,
  AlertDescription,
  AlertIcon,
  AlertTitle,
  Badge,
  Box,
  Button,
  CloseButton,
  Flex,
  Heading,
  HStack,
  Progress,
  Text,
  useToast,
  VStack
} from "@chakra-ui/react";
import { useEffect } from "react";
import { useTranslation } from "react-i18next";
import { useInAppUpdate } from "../hooks/useInAppUpdate";

/**
 * Props for InAppUpdateNotification component
 */
interface InAppUpdateNotificationProps {
  /**
   * Whether to show the notification
   */
  isVisible?: boolean;

  /**
   * Callback when notification is dismissed
   */
  onDismiss?: () => void;

  /**
   * Position of the notification
   */
  position?: "top" | "bottom";

  /**
   * Whether to auto-check for updates on mount
   */
  autoCheck?: boolean;
}

/**
 * Full-featured In-App Update Notification Component
 *
 * Displays update notifications with appropriate actions based on update type.
 * Handles both immediate and flexible update flows with progress indication.
 */
export function InAppUpdateNotification({
  isVisible = true,
  onDismiss,
  position = "top",
  autoCheck = true
}: InAppUpdateNotificationProps) {
  const {
    updateAvailable,
    updateType,
    priority,
    stalenessDays,
    isChecking,
    isDownloading,
    isDownloaded,
    isInstalling,
    isInstalled,
    error,
    isCancelled: _isCancelled,
    isServiceAvailable,
    platform: _platform,
    checkForUpdate,
    startImmediateUpdate,
    startFlexibleUpdate,
    completeFlexibleUpdate,
    clearError
  } = useInAppUpdate();

  const toast = useToast();

  // Auto-check for updates on mount
  useEffect(() => {
    if (autoCheck && isServiceAvailable) {
      checkForUpdate();
    }
  }, [autoCheck, isServiceAvailable, checkForUpdate]);

  // Show toast notifications for important events
  useEffect(() => {
    if (isInstalled) {
      toast({
        title: "Update Installed",
        description: "The app has been updated successfully!",
        status: "success",
        duration: 5000,
        isClosable: true
      });
    }
  }, [isInstalled, toast]);

  useEffect(() => {
    if (error) {
      toast({
        title: "Update Error",
        description: error,
        status: "error",
        duration: 8000,
        isClosable: true
      });
    }
  }, [error, toast]);

  // Don't render if not visible or service not available
  if (!isVisible || !isServiceAvailable) {
    return null;
  }

  // Don't render if no update available and not checking
  if (!updateAvailable && !isChecking && !error) {
    return null;
  }

  /**
   * Handle immediate update
   */
  const handleImmediateUpdate = async () => {
    try {
      await startImmediateUpdate();
    } catch (err) {
      console.error("Failed to start immediate update:", err);
    }
  };

  /**
   * Handle flexible update
   */
  const handleFlexibleUpdate = async () => {
    try {
      if (isDownloaded) {
        await completeFlexibleUpdate();
      } else {
        await startFlexibleUpdate();
      }
    } catch (err) {
      console.error("Failed to handle flexible update:", err);
    }
  };

  /**
   * Handle dismiss
   */
  const handleDismiss = () => {
    clearError();
    onDismiss?.();
  };

  /**
   * Get alert status based on update type and priority
   */
  const getAlertStatus = () => {
    if (error) return "error";
    if (updateType === "immediate" || priority >= 4) return "warning";
    return "info";
  };

  /**
   * Get update urgency text
   */
  const getUrgencyText = () => {
    if (priority >= 4) return "Critical";
    if (priority >= 2) return "Important";
    return "Available";
  };

  return (
    <Box
      position="fixed"
      top={position === "top" ? 4 : undefined}
      bottom={position === "bottom" ? 4 : undefined}
      left={4}
      right={4}
      zIndex={1000}
      maxW="md"
      mx="auto"
    >
      <Alert
        status={getAlertStatus()}
        variant="subtle"
        flexDirection="column"
        alignItems="flex-start"
        borderRadius="md"
        boxShadow="lg"
        p={4}
      >
        <Flex w="100%" align="flex-start">
          <AlertIcon />
          <Box flex="1" ml={2}>
            <AlertTitle fontSize="lg" mb={1}>
              {isChecking && "Checking for Updates..."}
              {error && "Update Error"}
              {updateAvailable && !error && `${getUrgencyText()} Update`}
            </AlertTitle>

            {isChecking && (
              <AlertDescription>
                <Text>Checking for app updates...</Text>
                <Progress size="sm" isIndeterminate mt={2} />
              </AlertDescription>
            )}

            {error && (
              <AlertDescription>
                <Text>{error}</Text>
                <Button size="sm" mt={2} onClick={clearError}>
                  Retry
                </Button>
              </AlertDescription>
            )}

            {updateAvailable && !error && (
              <AlertDescription>
                <VStack align="stretch" spacing={3}>
                  <Box>
                    <Text>
                      A new version of the app is available.
                      {stalenessDays > 0 && ` (${stalenessDays} days old)`}
                    </Text>

                    <HStack mt={1} spacing={2}>
                      <Badge colorScheme={updateType === "immediate" ? "red" : "blue"}>
                        {updateType?.toUpperCase()}
                      </Badge>
                      <Badge variant="outline">Priority: {priority}</Badge>
                    </HStack>
                  </Box>

                  {isDownloading && (
                    <Box>
                      <Text fontSize="sm" color="gray.600">
                        Downloading update...
                      </Text>
                      <Progress size="sm" isIndeterminate />
                    </Box>
                  )}

                  {isDownloaded && (
                    <Box>
                      <Text fontSize="sm" color="green.600">
                        ✓ Update downloaded and ready to install
                      </Text>
                    </Box>
                  )}

                  {isInstalling && (
                    <Box>
                      <Text fontSize="sm" color="blue.600">
                        Installing update...
                      </Text>
                      <Progress size="sm" isIndeterminate />
                    </Box>
                  )}

                  <HStack spacing={2}>
                    {updateType === "immediate" && (
                      <Button
                        colorScheme="red"
                        size="sm"
                        onClick={handleImmediateUpdate}
                        isLoading={isInstalling}
                        loadingText="Installing..."
                      >
                        Update Now
                      </Button>
                    )}

                    {updateType === "flexible" && (
                      <Button
                        colorScheme="blue"
                        size="sm"
                        onClick={handleFlexibleUpdate}
                        isLoading={isDownloading || isInstalling}
                        loadingText={isDownloading ? "Downloading..." : "Installing..."}
                      >
                        {isDownloaded ? "Install Update" : "Download Update"}
                      </Button>
                    )}

                    {updateType === "flexible" && !isDownloaded && (
                      <Button variant="ghost" size="sm" onClick={handleDismiss}>
                        Later
                      </Button>
                    )}
                  </HStack>
                </VStack>
              </AlertDescription>
            )}
          </Box>

          <CloseButton size="sm" onClick={handleDismiss} ml={2} />
        </Flex>
      </Alert>
    </Box>
  );
}

/**
 * Props for InAppUpdateBanner component
 */
interface InAppUpdateBannerProps {
  /**
   * Whether to show the banner
   */
  isVisible?: boolean;

  /**
   * Callback when banner is dismissed
   */
  onDismiss?: () => void;
}

/**
 * Simple In-App Update Banner Component
 *
 * A minimal banner notification for updates that doesn't interfere with the UI.
 */
export function InAppUpdateBanner({
  isVisible = true,
  onDismiss: _onDismiss
}: InAppUpdateBannerProps) {
  const {
    updateAvailable,
    updateType,
    isServiceAvailable,
    startImmediateUpdate,
    startFlexibleUpdate
  } = useInAppUpdate();
  const { t } = useTranslation();

  // Don't render if not visible, service not available, or no update
  if (!isVisible || !isServiceAvailable || !updateAvailable) {
    return null;
  }

  const handleUpdate = async () => {
    if (updateType === "immediate") {
      await startImmediateUpdate();
    } else {
      await startFlexibleUpdate();
    }
  };

  return (
    <Box
      position="fixed"
      top={0}
      left={0}
      right={0}
      bottom={0}
      bg="rgba(0, 0, 0, 0.5)"
      zIndex={1000}
      display="flex"
      alignItems="center"
      justifyContent="center"
      p={4}
    >
      <Box
        bg="white"
        borderRadius="20px"
        p={8}
        maxW="sm"
        w="full"
        textAlign="center"
        boxShadow="xl"
      >
        <Box mb={6} justifyItems={"center"}>
          <img src="/images/rocket_update.avif" alt="🚀" width={200} height={200} />
        </Box>
        {/* Title */}
        <Heading size="lg" mb={4} color="gray.800">
          {t("update_banner_title")}
        </Heading>
        {/* Description */}
        <Text color="gray.600" lineHeight="1.6">
          {t("update_app_reminder")}
        </Text>
        <Text fontWeight="semibold" mb={6} color="gray.800">
          {t("download_update")}
        </Text>
        {/* Update Button */}
        <Button
          colorScheme="blue"
          size="lg"
          w="full"
          borderRadius="12px"
          py={6}
          fontSize="lg"
          fontWeight="semibold"
          onClick={handleUpdate}
        >
          {t("update")}
        </Button>
      </Box>
    </Box>
  );
}
