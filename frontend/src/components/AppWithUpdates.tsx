import React, { useEffect } from "react";
import { inAppUpdateService } from "../services/InAppUpdateService";
import { InAppUpdateBanner, InAppUpdateNotification } from "./InAppUpdateNotification";

/**
 * App wrapper component that includes In-App Update functionality
 *
 * This component initializes the update service and provides update UI components
 * throughout the entire application.
 */

interface AppWithUpdatesProps {
  children: React.ReactNode;
}

export const AppWithUpdates: React.FC<AppWithUpdatesProps> = ({ children }) => {
  useEffect(() => {
    // Initialize the in-app update service when the app starts
    const initializeUpdates = async () => {
      try {
        console.log("🚀 Initializing In-App Update Service...");
        await inAppUpdateService.initialize();
      } catch (error) {
        console.error("❌ Failed to initialize In-App Update Service:", error);
      }
    };

    initializeUpdates();
  }, []);

  return (
    <>
      {/* Update banner at the top of the app */}
      <InAppUpdateBanner />

      {/* Main app content */}
      {children}

      {/* Update notification overlay */}
      <InAppUpdateNotification
        showPriority={true}
        showStaleness={true}
        autoHide={true}
        autoHideDelay={10000}
        className="z-[9999]" // Ensure it's above other components
      />
    </>
  );
};
