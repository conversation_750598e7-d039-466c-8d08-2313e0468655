import React, { useEffect, useState } from "react";
import { inAppUpdateService } from "../services/InAppUpdateService";
import { InAppUpdateBanner } from "./InAppUpdateNotification";

/**
 * Props for AppWithUpdates component
 */
interface AppWithUpdatesProps {
  /**
   * Child components to render
   */
  children: React.ReactNode;

  /**
   * Whether to show update banner
   */
  showBanner?: boolean;

  /**
   * Whether to auto-check for updates on app start
   */
  autoCheck?: boolean;

  /**
   * Interval for periodic update checks (in milliseconds)
   * Set to 0 to disable periodic checks
   */
  checkInterval?: number;
}

/**
 * App Wrapper with In-App Updates
 *
 * This component wraps the entire app and provides:
 * - Automatic update service initialization
 * - Periodic update checking
 * - Update banner notifications
 * - Global update state management
 */
export function AppWithUpdates({
  children,
  showBanner = true,
  autoCheck = true,
  checkInterval = 24 * 60 * 60 * 1000 // 24 hours
}: AppWithUpdatesProps) {
  const [isInitialized, setIsInitialized] = useState(false);
  const [showUpdateBanner, setShowUpdateBanner] = useState(false);
  const [initError, setInitError] = useState<string | null>(null);

  // Robust interval management state
  const intervalRef = React.useRef<NodeJS.Timeout | null>(null);
  const retryCountRef = React.useRef(0);
  const backoffDelayRef = React.useRef(1000); // Start with 1 second
  const isCheckingRef = React.useRef(false); // Guard flag to prevent concurrent checks
  const maxRetries = 5;
  const maxBackoffDelay = 5 * 60 * 1000; // 5 minutes

  /**
   * Initialize the update service
   */
  useEffect(() => {
    let mounted = true;

    const initializeUpdateService = async () => {
      try {
        console.log("🚀 Initializing In-App Update service...");

        await inAppUpdateService.initialize();

        if (!mounted) return;

        const platformInfo = inAppUpdateService.getPlatformInfo();
        console.log("📱 Platform info:", platformInfo);

        setIsInitialized(true);

        // Auto-check for updates if enabled and service is available
        if (autoCheck && platformInfo.available) {
          console.log("🔍 Auto-checking for updates...");

          try {
            const result = await inAppUpdateService.checkForUpdate();
            console.log("📋 Update check result:", result);

            if (result.updateAvailable) {
              setShowUpdateBanner(true);
            }
          } catch (error) {
            console.warn("⚠️ Auto update check failed:", error);
          }
        }
      } catch (error) {
        if (!mounted) return;

        console.error("❌ Failed to initialize update service:", error);
        setInitError(`Failed to initialize update service: ${error}`);
        setIsInitialized(true); // Still mark as initialized to continue app loading
      }
    };

    initializeUpdateService();

    return () => {
      mounted = false;
    };
  }, [autoCheck]);

  /**
   * Robust periodic update check with backoff strategy and visibility checks
   */
  const performUpdateCheck = React.useCallback(async () => {
    // Guard against concurrent execution
    if (isCheckingRef.current) {
      console.log("🔄 Update check already in progress - skipping");
      return;
    }

    // Skip if tab/window is hidden to save resources
    if (document.visibilityState === "hidden") {
      console.log("🔄 Skipping update check - tab is hidden");
      return;
    }

    // Set guard flag
    isCheckingRef.current = true;

    try {
      console.log("🔄 Periodic update check...");

      const result = await inAppUpdateService.checkForUpdate();
      console.log("📋 Periodic check result:", result);

      if (result.updateAvailable) {
        setShowUpdateBanner(true);
      }

      // Reset retry count and backoff delay on success
      retryCountRef.current = 0;
      backoffDelayRef.current = 1000;
    } catch (error) {
      console.warn("⚠️ Periodic update check failed:", error);

      retryCountRef.current += 1;

      if (retryCountRef.current >= maxRetries) {
        console.error(
          `❌ Max retries (${maxRetries}) reached for update checks. Stopping periodic checks.`
        );
        if (intervalRef.current) {
          clearInterval(intervalRef.current);
          intervalRef.current = null;
        }
        return;
      }

      // Exponential backoff with jitter
      const jitter = Math.random() * 1000; // Add up to 1 second of jitter
      backoffDelayRef.current = Math.min(backoffDelayRef.current * 2 + jitter, maxBackoffDelay);

      console.log(
        `⏳ Retrying in ${backoffDelayRef.current / 1000}s (attempt ${retryCountRef.current}/${maxRetries})`
      );
    } finally {
      // Always clear the guard flag
      isCheckingRef.current = false;
    }
  }, [maxRetries, maxBackoffDelay]);

  /**
   * Set up periodic update checks with robust interval management
   */
  useEffect(() => {
    if (!isInitialized || !checkInterval || checkInterval <= 0) {
      return;
    }

    if (!inAppUpdateService.isAvailable()) {
      return;
    }

    console.log(`⏰ Setting up periodic update checks every ${checkInterval / 1000 / 60} minutes`);

    // Clear any existing interval
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }

    // Reset retry state
    retryCountRef.current = 0;
    backoffDelayRef.current = 1000;

    // Set up the interval
    intervalRef.current = setInterval(performUpdateCheck, checkInterval);

    // Handle visibility change events
    const handleVisibilityChange = () => {
      if (document.visibilityState === "visible" && retryCountRef.current < maxRetries) {
        // When tab becomes visible again, perform an immediate check if we haven't exceeded retries
        console.log(" Tab became visible - performing immediate update check");
        performUpdateCheck();
      }
    };

    document.addEventListener("visibilitychange", handleVisibilityChange);

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
      document.removeEventListener("visibilitychange", handleVisibilityChange);
    };
  }, [isInitialized, checkInterval, performUpdateCheck, maxRetries]);

  /**
   * Global cleanup on page unload - defensive measure
   */
  useEffect(() => {
    const handleBeforeUnload = () => {
      console.log("🧹 Page unloading - cleaning up update timers");

      // Clear any active intervals
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }

      // Reset checking state
      isCheckingRef.current = false;

      // Reset retry state
      retryCountRef.current = 0;
      backoffDelayRef.current = 1000;
    };

    const handlePageHide = () => {
      console.log("🧹 Page hidden - cleaning up update timers");

      // Clear any active intervals when page is hidden (mobile Safari, etc.)
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }

      // Reset checking state
      isCheckingRef.current = false;
    };

    // Add global cleanup listeners
    window.addEventListener("beforeunload", handleBeforeUnload);
    window.addEventListener("pagehide", handlePageHide);

    return () => {
      // Remove global cleanup listeners
      window.removeEventListener("beforeunload", handleBeforeUnload);
      window.removeEventListener("pagehide", handlePageHide);

      // Final cleanup on component unmount
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
      isCheckingRef.current = false;
    };
  }, []);

  /**
   * Set up global update event listeners
   */
  useEffect(() => {
    if (!isInitialized || !inAppUpdateService.isAvailable()) {
      return;
    }

    const handleUpdateAvailable = () => {
      console.log("📢 Update available event received");
      setShowUpdateBanner(true);
    };

    const handleUpdateNotAvailable = () => {
      console.log("📢 No update available event received");
      setShowUpdateBanner(false);
    };

    const handleUpdateInstalled = () => {
      console.log("📢 Update installed event received");
      setShowUpdateBanner(false);

      // Optionally reload the app or show a success message
      // window.location.reload();
    };

    const handleUpdateFailed = (data: any) => {
      console.error("📢 Update failed event received:", data);
      // Keep banner visible so user can retry
    };

    const handleUpdateCancelled = () => {
      console.log("📢 Update cancelled event received");
      // Keep banner visible for flexible updates
    };

    // Add event listeners
    inAppUpdateService.addEventListener("updateAvailable", handleUpdateAvailable);
    inAppUpdateService.addEventListener("updateNotAvailable", handleUpdateNotAvailable);
    inAppUpdateService.addEventListener("updateInstalled", handleUpdateInstalled);
    inAppUpdateService.addEventListener("updateFailed", handleUpdateFailed);
    inAppUpdateService.addEventListener("updateCancelled", handleUpdateCancelled);

    return () => {
      // Remove event listeners
      inAppUpdateService.removeEventListener("updateAvailable", handleUpdateAvailable);
      inAppUpdateService.removeEventListener("updateNotAvailable", handleUpdateNotAvailable);
      inAppUpdateService.removeEventListener("updateInstalled", handleUpdateInstalled);
      inAppUpdateService.removeEventListener("updateFailed", handleUpdateFailed);
      inAppUpdateService.removeEventListener("updateCancelled", handleUpdateCancelled);
    };
  }, [isInitialized]);

  /**
   * Handle banner dismiss
   */
  const handleBannerDismiss = () => {
    setShowUpdateBanner(false);
  };

  /**
   * Log initialization status
   */
  useEffect(() => {
    if (isInitialized) {
      const platformInfo = inAppUpdateService.getPlatformInfo();

      if (initError) {
        console.warn("⚠️ App initialized with update service error:", initError);
      } else if (platformInfo.available) {
        console.log("✅ App initialized with In-App Updates enabled");
      } else {
        console.log(
          `ℹ️ App initialized without In-App Updates (platform: ${platformInfo.platform})`
        );
      }
    }
  }, [isInitialized, initError]);

  return (
    <>
      {/* Update Banner */}
      {showBanner && (
        <InAppUpdateBanner isVisible={showUpdateBanner} onDismiss={handleBannerDismiss} />
      )}

      {/* Main App Content */}
      <div
        style={{
          paddingTop: showBanner && showUpdateBanner ? "48px" : "0",
          transition: "padding-top 0.3s ease"
        }}
      >
        {children}
      </div>
    </>
  );
}

/**
 * Hook to access update service from anywhere in the app
 */
export function useAppUpdates() {
  const [isServiceAvailable, setIsServiceAvailable] = useState(false);
  const [platform, setPlatform] = useState("unknown");

  useEffect(() => {
    const platformInfo = inAppUpdateService.getPlatformInfo();
    setIsServiceAvailable(platformInfo.available);
    setPlatform(platformInfo.platform);
  }, []);

  const checkForUpdate = async () => {
    if (!isServiceAvailable) {
      throw new Error("Update service not available on this platform");
    }

    return await inAppUpdateService.checkForUpdate();
  };

  const getUpdateInfo = async () => {
    if (!isServiceAvailable) {
      throw new Error("Update service not available on this platform");
    }

    return await inAppUpdateService.getUpdateInfo();
  };

  return {
    isServiceAvailable,
    platform,
    checkForUpdate,
    getUpdateInfo,
    service: inAppUpdateService
  };
}
