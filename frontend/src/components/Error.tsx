import { Box, Image, Text } from "@chakra-ui/react";
import React from "react";
import React<PERSON><PERSON> from "react-json-view";

interface ErrorProps {
  error: Error;
}

const Error: React.FC<ErrorProps> = ({ error }) => {
  return (
    <Box className="p-6 gap-4 flex justify-center items-center flex-col">
      <Image width={100} src="/icons/error.png" />
      <Box className="text-3xl font-bold">There was some error</Box>
      <Text>This is what you need to tell us about it</Text>
      <ReactJson
        theme={"shapeshifter:inverted"}
        iconStyle="triangle"
        style={{ width: "100%" }}
        src={{
          name: error.name,
          message: error.message,
          stack: error.stack
        }}
      />
    </Box>
  );
};

export default Error;
